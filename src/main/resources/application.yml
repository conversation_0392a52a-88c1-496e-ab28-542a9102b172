hasura:
  endpoint: ${HASURA_GRAPHQL_ENDPOINT}
  admin-secret-header: ${HASURA_GRAPHQL_ADMIN_SECRET_HEADER}
cache:
  expire-after-write: 20
  initial-capacity: 10

spring:
  datasource:
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    driver-class-name: org.postgresql.Driver
    hikari:
      auto-commit: true
      connection-timeout: 30000
      minimum-idle: 5
      maximum-pool-size: 10
      idle-timeout: 600000
      max-lifetime: 1800000
  jpa:
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  jackson:
    default-property-inclusion: non-null
  flyway:
    enabled: true
    locations: classpath:db/migration/structure, classpath:db/migration/data, classpath:db/migration/patch
  application:
    name: gedsys2-base
  rabbitmq:
    username: ${RABBITMQ_USERNAME:guest}
    password: ${RABBITMQ_PASSWORD:guest}
    listener:
      simple:
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 1
          multiplier: 2.0
        acknowledge-mode: manual
        default-requeue-rejected: false
    template:
      retry:
        enabled: true
        initial-interval: 1000
        max-attempts: 3
        multiplier: 2.0
    port: ${RABBITMQ_PORT:5672}
    host: ${RABBITMQ_HOST:localhost}
  docker:
    compose:
      enabled: false

server:
  port: 8080
  servlet:
    context-path: /core
