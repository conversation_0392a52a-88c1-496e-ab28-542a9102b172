{"properties": [{"name": "hasura.endpoint", "type": "java.lang.String", "description": "A description for 'hasura.endpoint'"}, {"name": "hasura.admin-secret-header", "type": "java.lang.String", "description": "A description for 'hasura.admin-secret-header'"}, {"name": "cache.expire-after-write", "type": "java.lang.String", "description": "A description for 'cache.expire-after-write'"}, {"name": "cache.initial-capacity", "type": "java.lang.String", "description": "A description for 'cache.initial-capacity'"}]}