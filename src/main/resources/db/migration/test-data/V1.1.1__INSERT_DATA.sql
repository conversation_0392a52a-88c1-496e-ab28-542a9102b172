INSERT INTO secciones (id, codigo, nombre, responsable, tipo, estado, padre_id)
VALUES ('91f94320-526f-4b8c-bfb4-1d286e2e161f', '01.00.00', '<PERSON><PERSON><PERSON> del Alcalde', 'jmarin', 'DESPACHO', 'ACTIVA', null);

INSERT INTO secciones (id, codigo, nombre, responsable, tipo, estado, padre_id)
VALUES ('7db4ac97-f37a-4733-bf75-df7194d59c82', '01.01.00', 'Secretaría de Gobierno', 'emontoya', 'SECRETARIA', 'ACTIVA', '91f94320-526f-4b8c-bfb4-1d286e2e161f');

INSERT INTO secciones (id, codigo, nombre, responsable, tipo, estado, padre_id)
VALUES ('2f2ec234-ed88-441f-ac6d-329547414511', '01.01.01', 'Juridica', 'dplata', 'AUXILIAR', 'ACTIVA', '7db4ac97-f37a-4733-bf75-df7194d59c82');


INSERT INTO series_documentales (id, codigo, nombre, tipo, estado, padre_id)
VALUES ('b350dd0e-5add-4dc8-b09d-bb173cf677d6', '01', 'Comunicaciones Oficiales', 'SERIE', 'ACTIVA', null);

INSERT INTO series_documentales (id, codigo, nombre, tipo, estado, padre_id)
VALUES ('b6e40635-30fe-4e93-9f5c-cb911badb3a6', '01.01', 'Comunicaciones Oficiales 2019', 'SUBSERIE', 'ACTIVA', 'b350dd0e-5add-4dc8-b09d-bb173cf677d6');


INSERT INTO tipos_documentales (id, nombre, estado)
VALUES ('169c4894-637f-4c0c-83a5-7d494841eb73', 'Carta', 'ACTIVO');


INSERT INTO clasificaciones_documentales (id, seccion_id, subserie_id)
VALUES ('3f41922e-fd89-4c3f-9ad5-2a3a896fb127', '7db4ac97-f37a-4733-bf75-df7194d59c82', 'b6e40635-30fe-4e93-9f5c-cb911badb3a6');


INSERT INTO unidades_documentales (id, nombre,estado, clasificacion_id) VALUES ('0d405a15-11a2-426a-89ca-7aede3280887', 'Actas de Gobierno Actual', 'ABIERTA', '3f41922e-fd89-4c3f-9ad5-2a3a896fb127');

INSERT INTO definicion_metadatos (id, nombre, descripcion, tipo, formato, rules, patron)
VALUES ('7475841c-e792-4737-aabe-4a4820e1e3dd', 'Folios', 'Número de folios del documento', 'CONTENIDO', 'NUMERICO', 'requerido:true,positivo:true', 'folios');

-- INSERT INTO consecutivos (id, nombre, prefijo, sufijo, contador, estado)
-- VALUES ('9ea6b00d-4a3b-4f4e-9c43-22a3141ec138', 'Consecutivo con clasificación', 'C', '1', 0, 'ACTIVO');
--
-- INSERT INTO consecutivos (id, nombre, prefijo, sufijo, contador, estado)
-- VALUES ('5eb7a7c5-7eb7-4ea9-a53d-0106c96683b6', 'Consecutivo sin clasificación', 'C', '1', 0, 'ACTIVO');

INSERT INTO plantillas (id, titulo, estado, tipo_documental_id, tipo_plantilla)
VALUES ('7d780d3a-bb98-4ba9-bafd-6e82393032b0', 'Plantilla 1', 'ACTIVA', '169c4894-637f-4c0c-83a5-7d494841eb73', 'PRODUCCION');

INSERT INTO metadatos_plantilla (definicion_metadato_id, plantilla_id) VALUES ('7475841c-e792-4737-aabe-4a4820e1e3dd', '7d780d3a-bb98-4ba9-bafd-6e82393032b0');

-- Usuarios externos para pruebas (Grupo: Entidades gubernamentales)
INSERT INTO external_users (id, name, identification_type, identification_number, salutation, notes, status)
VALUES ('f8c3de3d-1fea-4d7c-a8b0-29f63c4c3454', 'Ministerio de Educación Nacional', 'NIT', '899999001-7', 'Estimado Ministerio', 'Entidad gubernamental encargada de la educación', 'ACTIVO');

INSERT INTO external_users (id, name, identification_type, identification_number, salutation, notes, status)
VALUES ('e7b2a645-8ef0-4e9a-b8d1-10a96c7c8e32', 'Ministerio de Salud y Protección Social', 'NIT', '900474727-4', 'Estimado Ministerio', 'Entidad gubernamental encargada de la salud', 'ACTIVO');

-- Propiedades para Ministerio de Educación
INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('a1b2c3d4-e5f6-4a5b-9c8d-1a2b3c4d5e6f', 'EMAIL', 'Correo principal', '<EMAIL>', 'Correo de atención al ciudadano', 'f8c3de3d-1fea-4d7c-a8b0-29f63c4c3454');

INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('b2c3d4e5-f6a7-5b6c-0d1e-2f3a4b5c6d7e', 'PHONE', 'Teléfono principal', '6013078079', 'Línea de atención Bogotá', 'f8c3de3d-1fea-4d7c-a8b0-29f63c4c3454');

INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('c3d4e5f6-a7b8-6c7d-1e2f-3a4b5c6d7e8f', 'ADDRESS', 'Dirección principal', 'Calle 43 No. 57-14 CAN, Bogotá', 'Dirección sede principal', 'f8c3de3d-1fea-4d7c-a8b0-29f63c4c3454');

-- Propiedades para Ministerio de Salud
INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('d4e5f6a7-b8c9-7d8e-2f3a-4b5c6d7e8f9a', 'EMAIL', 'Correo principal', '<EMAIL>', 'Correo de atención al ciudadano', 'e7b2a645-8ef0-4e9a-b8d1-10a96c7c8e32');

INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('e5f6a7b8-c9d0-8e9f-3a4b-5c6d7e8f9a0b', 'PHONE', 'Teléfono principal', '6013305000', 'Línea de atención Bogotá', 'e7b2a645-8ef0-4e9a-b8d1-10a96c7c8e32');

INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('f6a7b8c9-d0e1-9f0a-4b5c-6d7e8f9a0b1c', 'ADDRESS', 'Dirección principal', 'Carrera 13 No. 32-76, Bogotá', 'Dirección sede principal', 'e7b2a645-8ef0-4e9a-b8d1-10a96c7c8e32');

-- Usuarios externos para pruebas (Grupo: Empresas privadas)
INSERT INTO external_users (id, name, identification_type, identification_number, salutation, notes, status)
VALUES ('a9b8c7d6-e5f4-4a3b-8c7d-9e8f7a6b5c4d', 'Ecopetrol S.A.', 'NIT', '899999068-1', 'Estimados señores', 'Empresa colombiana de petróleos', 'ACTIVO');

INSERT INTO external_users (id, name, identification_type, identification_number, salutation, notes, status)
VALUES ('b8c7d6e5-f4e3-3b2a-7d6e-8f7a6b5c4d3e', 'Grupo Bancolombia', 'NIT', '890903938-8', 'Estimados señores', 'Entidad financiera', 'ACTIVO');

-- Propiedades para Ecopetrol
INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('a7b8c9d0-e1f2-0a1b-5c6d-7e8f9a0b1c2d', 'EMAIL', 'Correo principal', '<EMAIL>', 'Correo de atención', 'a9b8c7d6-e5f4-4a3b-8c7d-9e8f7a6b5c4d');

INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('b8c9d0e1-f2a3-1b2c-6d7e-8f9a0b1c2d3e', 'PHONE', 'Teléfono principal', '6013234000', 'Línea de atención Bogotá', 'a9b8c7d6-e5f4-4a3b-8c7d-9e8f7a6b5c4d');

INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('c9d0e1f2-a3b4-2c3d-7e8f-9a0b1c2d3e4f', 'ADDRESS', 'Dirección principal', 'Carrera 13 No. 36-24, Bogotá', 'Dirección sede principal', 'a9b8c7d6-e5f4-4a3b-8c7d-9e8f7a6b5c4d');

-- Propiedades para Bancolombia
INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('d0e1f2a3-b4c5-3d4e-8f9a-0b1c2d3e4f5a', 'EMAIL', 'Correo principal', '<EMAIL>', 'Correo de servicio al cliente', 'b8c7d6e5-f4e3-3b2a-7d6e-8f7a6b5c4d3e');

INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('e1f2a3b4-c5d6-4e5f-9a0b-1c2d3e4f5a6b', 'PHONE', 'Teléfono principal', '6014404000', 'Línea de atención Bogotá', 'b8c7d6e5-f4e3-3b2a-7d6e-8f7a6b5c4d3e');

INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('f2a3b4c5-d6e7-5f6a-0b1c-2d3e4f5a6b7c', 'ADDRESS', 'Dirección principal', 'Carrera 48 No. 26-85, Medellín', 'Dirección sede principal', 'b8c7d6e5-f4e3-3b2a-7d6e-8f7a6b5c4d3e');

-- Usuario externo para pruebas (Persona natural)
INSERT INTO external_users (id, name, identification_type, identification_number, salutation, notes, status)
VALUES ('c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d', 'Juan Carlos Rodríguez Pérez', 'CC', '79876543', 'Estimado señor', 'Ciudadano colombiano', 'ACTIVO');

-- Propiedades para Juan Carlos Rodríguez
INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('a3b4c5d6-e7f8-6a7b-1c2d-3e4f5a6b7c8d', 'EMAIL', 'Correo personal', '<EMAIL>', 'Correo personal', 'c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d');

INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('b4c5d6e7-f8a9-7b8c-2d3e-4f5a6b7c8d9e', 'MOBILE', 'Celular personal', '3102345678', 'Número de celular personal', 'c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d');

INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('c5d6e7f8-a9b0-8c9d-3e4f-5a6b7c8d9e0f', 'ADDRESS', 'Dirección residencia', 'Calle 45 No. 23-67, Apto 502, Bogotá', 'Dirección de residencia', 'c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d');

INSERT INTO external_users_properties (id, property_type, property_name, property_value, notes, external_user_id)
VALUES ('d6e7f8a9-b0c1-9d0e-4f5a-6b7c8d9e0f1a', 'DATE', 'Fecha de nacimiento', '1985-06-15', 'Fecha de nacimiento', 'c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d');
