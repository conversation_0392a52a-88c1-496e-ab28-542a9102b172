-- C<PERSON>r tabla usuarios_secciones
CREATE TABLE usuarios_secciones
(
    id          UUID         NOT NULL,
    username    VARCHAR(255) NOT NULL,
    relacion    VARCHAR(50)  NOT NULL,
    seccion_id  UUID         NOT NULL,
    CONSTRAINT pk_usuarios_secciones PRIMARY KEY (id),
    CONSTRAINT FK_USUARIOS_SECCIONES_ON_SECCION FOREIGN KEY (seccion_id) REFERENCES secciones (id)
);

-- Crear índice para mejorar el rendimiento de las búsquedas por username
CREATE INDEX idx_usuarios_secciones_username ON usuarios_secciones (username);

-- <PERSON><PERSON>r índice para mejorar el rendimiento de las búsquedas por seccion_id
CREATE INDEX idx_usuarios_secciones_seccion ON usuarios_secciones (seccion_id);

-- Crear restricción única para evitar duplicados de username y seccion_id con la misma relación
CREATE UNIQUE INDEX idx_unique_usuario_seccion_relacion ON usuarios_secciones (username, seccion_id, relacion);