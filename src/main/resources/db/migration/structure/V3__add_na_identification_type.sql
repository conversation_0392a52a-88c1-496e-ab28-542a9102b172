-- Migración para agregar soporte al tipo de identificación NA
-- Permite que identification_number sea NULL para usuarios con tipo NA
-- Agrega constraint para validar consistencia entre tipo e identificación

-- Permitir NULL en la columna identification_number
ALTER TABLE external_users 
ALTER COLUMN identification_number DROP NOT NULL;

-- Agregar constraint para validar consistencia
-- Regla: (tipo=NA AND número=NULL) OR (tipo≠NA AND número≠NULL)
ALTER TABLE external_users 
ADD CONSTRAINT chk_identification_consistency 
CHECK (
    (identification_type = 'NA' AND identification_number IS NULL) OR
    (identification_type != 'NA' AND identification_number IS NOT NULL)
);

-- Comentarios para documentar la migración
COMMENT ON CONSTRAINT chk_identification_consistency ON external_users IS 
'Valida que usuarios con tipo NA tengan número NULL y usuarios con otros tipos tengan número no NULL';
