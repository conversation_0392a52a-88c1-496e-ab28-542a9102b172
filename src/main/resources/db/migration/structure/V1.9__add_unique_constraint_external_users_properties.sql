-- Migración para agregar restricción de unicidad en external_users_properties
-- Previene la creación de propiedades duplicadas para el mismo usuario externo

-- <PERSON><PERSON>, eliminar cualquier duplicado existente manteniendo solo el registro más reciente
-- (basado en el ID más pequeño que indica creación más temprana)
DELETE FROM external_users_properties
WHERE id NOT IN (
    SELECT MIN(id::text)::uuid
    FROM external_users_properties
    GROUP BY external_user_id, property_value
);

-- Agregar índice único para evitar duplicados de property_value por usuario
CREATE UNIQUE INDEX idx_unique_external_user_property_value 
ON external_users_properties (external_user_id, property_value);
