-- Consolidated table creation script

-- Secciones table (from previous structure)
CREATE TABLE secciones
(
    id          UUID         NOT NULL,
    codigo      VARCHAR(50)  NOT NULL,
    estado      VARCHAR(50) DEFAULT 'ACTIVA',
    nombre      VARCHAR(255) NOT NULL,
    responsable VARCHAR(50),
    tipo        VARCHAR(50)  NOT NULL,
    padre_id    UUID,
    CONSTRAINT pk_secciones PRIMARY KEY (id),
    CONSTRAINT FK_SECCION_PADRE FOREIGN KEY (padre_id) REFERENCES secciones (id)
);

-- Series documentales table (from previous structure)
CREATE TABLE series_documentales
(
    id       UUID         NOT NULL,
    codigo   VARCHAR(50)  NOT NULL,
    nombre   VARCHAR(255) NOT NULL,
    tipo     VARCHAR(50)  NOT NULL,
    estado   VARCHAR(50)  NOT NULL,
    padre_id UUID,
    CONSTRAINT pk_series_documentales PRIMARY KEY (id),
    CONSTRAINT FK_SERIE_PADRE FOREIGN KEY (padre_id) REFERENCES series_documentales (id)
);

-- Tipos documentales table (from previous structure)
CREATE TABLE tipos_documentales
(
    id     UUID NOT NULL,
    nombre VARCHAR(255),
    estado VARCHAR(50),
    CONSTRAINT pk_tipos_documentales PRIMARY KEY (id)
);

-- Clasificaciones documentales
CREATE TABLE clasificaciones_documentales
(
    id          UUID NOT NULL,
    seccion_id  UUID NOT NULL,
    subserie_id UUID NOT NULL,
    CONSTRAINT pk_clasificaciones_documentales PRIMARY KEY (id),
    CONSTRAINT FK_CLASIFICACIONES_DOCUMENTALES_ON_SECCION FOREIGN KEY (seccion_id) REFERENCES secciones (id),
    CONSTRAINT FK_CLASIFICACIONES_DOCUMENTALES_ON_SUBSERIE FOREIGN KEY (subserie_id) REFERENCES series_documentales (id)
);

-- Clasificaciones tipologicas
CREATE TABLE clasificaciones_tipologicas
(
    clasificacion_id UUID NOT NULL,
    tipologia_id     UUID NOT NULL,
    CONSTRAINT FK_CLASIFICACION_ON_CT FOREIGN KEY (clasificacion_id) REFERENCES clasificaciones_documentales (id),
    CONSTRAINT FK_TIPO_DOCUMENTAL_ON_CT FOREIGN KEY (tipologia_id) REFERENCES tipos_documentales (id)
);

-- Unidades documentales
CREATE TABLE unidades_documentales
(
    id               UUID NOT NULL,
    nombre           VARCHAR(255),
    estado           VARCHAR(255),
    clasificacion_id UUID,
    CONSTRAINT pk_unidades_documentales PRIMARY KEY (id),
    CONSTRAINT FK_UNIDADES_DOCUMENTALES_ON_CLASIFICACION FOREIGN KEY (clasificacion_id) REFERENCES clasificaciones_documentales (id)
);

-- Definicion metadatos
CREATE TABLE definicion_metadatos
(
    id          UUID         NOT NULL,
    nombre      VARCHAR(255) NOT NULL,
    descripcion VARCHAR(255),
    tipo        VARCHAR(255) NOT NULL,
    formato     VARCHAR(255) NOT NULL,
    rules       VARCHAR(255),
    patron      VARCHAR(255) NOT NULL,
    CONSTRAINT pk_definicion_metadatos PRIMARY KEY (id),
    CONSTRAINT uc_definicion_metadatos_patron UNIQUE (patron)
);

-- Consecutivos
CREATE TABLE consecutivos
(
    id                 UUID NOT NULL,
    prefijo            VARCHAR(255),
    sufijo             VARCHAR(255),
    contador           INTEGER,
    estado             VARCHAR(255),
    tipo_consecutivo   VARCHAR(50),
    tipo_documental_id UUID,
    clasificacion_id   UUID,
    CONSTRAINT pk_consecutivos PRIMARY KEY (id),
    CONSTRAINT fk_consecutivos_tipo_documental FOREIGN KEY (tipo_documental_id) REFERENCES tipos_documentales (id),
    CONSTRAINT fk_consecutivos_clasificacion FOREIGN KEY (clasificacion_id) REFERENCES clasificaciones_documentales (id)
);

-- Plantillas
CREATE TABLE plantillas
(
    id                 UUID NOT NULL,
    titulo             VARCHAR(255),
    estado             VARCHAR(255),
    consecutivo_id     UUID,
    tipo_documental_id UUID,
    CONSTRAINT pk_plantillas PRIMARY KEY (id),
    CONSTRAINT uc_plantillas_consecutivo UNIQUE (consecutivo_id),
    CONSTRAINT FK_PLANTILLAS_ON_CONSECUTIVO FOREIGN KEY (consecutivo_id) REFERENCES consecutivos (id),
    CONSTRAINT FK_PLANTILLAS_ON_TIPO_DOCUMENTAL FOREIGN KEY (tipo_documental_id) REFERENCES tipos_documentales (id)
);

-- Metadatos plantilla
CREATE TABLE metadatos_plantilla
(
    definicion_metadato_id UUID NOT NULL,
    plantilla_id           UUID NOT NULL,
    CONSTRAINT pk_metadatos_plantilla PRIMARY KEY (definicion_metadato_id, plantilla_id),
    CONSTRAINT fk_metpla_on_definicion_metadato_entity FOREIGN KEY (definicion_metadato_id) REFERENCES definicion_metadatos (id),
    CONSTRAINT fk_metpla_on_plantilla_entity FOREIGN KEY (plantilla_id) REFERENCES plantillas (id)
);

-- Access control
CREATE TABLE access_control
(
    id             UUID         NOT NULL,
    interest_group VARCHAR(255) NOT NULL,
    detail         VARCHAR(255) NOT NULL,
    notes          TEXT,
    permissions    VARCHAR(255),
    code           VARCHAR(50)  NOT NULL,
    access_level   VARCHAR(50)  NOT NULL,
    CONSTRAINT pk_access_control PRIMARY KEY (id)
);

-- Documentos
CREATE TABLE documentos
(
    id                   UUID         NOT NULL,
    file_id              VARCHAR(255),
    estado               VARCHAR(255),
    titulo               VARCHAR(255) NOT NULL,
    autor                VARCHAR(255),
    created_at           TIMESTAMP,
    updated_at           TIMESTAMP,
    unidad_documental_id UUID         NOT NULL,
    tipo_documental_id   UUID         NOT NULL,
    CONSTRAINT pk_documentos PRIMARY KEY (id),
    CONSTRAINT FK_DOCUMENTOS_ON_CLASIFICACION FOREIGN KEY (unidad_documental_id) REFERENCES unidades_documentales (id),
    CONSTRAINT FK_DOCUMENTOS_ON_TIPO_DOCUMENTAL FOREIGN KEY (tipo_documental_id) REFERENCES tipos_documentales (id)
);

-- Metadatos
CREATE TABLE metadatos
(
    id           UUID NOT NULL,
    patron       VARCHAR(255),
    nombre       VARCHAR(255),
    tipo         VARCHAR(50),
    formato      VARCHAR(50),
    valor        TEXT,
    documento_id UUID,
    CONSTRAINT pk_metadatos PRIMARY KEY (id),
    CONSTRAINT fk_metadatos_on_documento FOREIGN KEY (documento_id) REFERENCES documentos (id)
);

-- Firma usuario
CREATE TABLE firma_usuario
(
    id            UUID NOT NULL,
    estado        VARCHAR(50),
    firmado_en    TIMESTAMP,
    firmante      VARCHAR(255),
    height        INTEGER,
    observaciones TEXT,
    page          INTEGER,
    width         INTEGER,
    x             INTEGER,
    y             INTEGER,
    documento_id  UUID NOT NULL,
    CONSTRAINT pk_firma_usuario PRIMARY KEY (id),
    CONSTRAINT FK_FIRMA_DOCUMENTO FOREIGN KEY (documento_id) REFERENCES documentos (id)
);

-- Anexos
CREATE TABLE anexos
(
    id           UUID         NOT NULL,
    nombre       VARCHAR(255) NOT NULL,
    descripcion  TEXT,
    file_id      VARCHAR(255) NOT NULL,
    hash         VARCHAR(255) NOT NULL,
    bytes        BIGINT       NOT NULL,
    extension    VARCHAR(50)  NOT NULL,
    documento_id UUID         NOT NULL,
    CONSTRAINT pk_anexos PRIMARY KEY (id),
    CONSTRAINT FK_ANEXO_DOCUMENTO FOREIGN KEY (documento_id) REFERENCES documentos (id)
);

-- Aprobaciones
CREATE TABLE aprobaciones
(
    id            UUID         NOT NULL,
    aprobador     VARCHAR(255) NOT NULL,
    observaciones TEXT,
    estado        VARCHAR(50)  NOT NULL,
    aprobado_en   TIMESTAMP,
    documento_id  UUID,
    CONSTRAINT pk_aprobaciones PRIMARY KEY (id),
    CONSTRAINT FK_APROBACION_DOCUMENTO FOREIGN KEY (documento_id) REFERENCES documentos (id)
);

-- External users
CREATE TABLE external_users
(
    id                    UUID NOT NULL,
    name                  VARCHAR(255),
    identification_type   VARCHAR(50),
    identification_number VARCHAR(255),
    salutation            VARCHAR(255),
    notes                 TEXT,
    status                VARCHAR(50),
    CONSTRAINT pk_external_users PRIMARY KEY (id)
);

-- Radicados
CREATE TABLE radicados
(
    id               UUID NOT NULL,
    estado           VARCHAR(50),
    numero_radicado  INTEGER,
    fecha_expedicion TIMESTAMP,
    emisor           VARCHAR(255),
    observaciones    TEXT,
    prefijo          VARCHAR(255),
    sufijo           VARCHAR(255),
    tipo             VARCHAR(50),
    documento_id     UUID NOT NULL,
    consecutivo_id   UUID NOT NULL,
    created_at       TIMESTAMP,
    updated_at       TIMESTAMP,
    CONSTRAINT pk_radicados PRIMARY KEY (id),
    CONSTRAINT FK_RADICADO_DOCUMENTO FOREIGN KEY (documento_id) REFERENCES documentos (id),
    CONSTRAINT FK_RADICADO_CONSECUTIVO FOREIGN KEY (consecutivo_id) REFERENCES consecutivos (id)
);

CREATE TABLE external_users_properties
(
    id               UUID         NOT NULL,
    property_type    VARCHAR(50)  NOT NULL,
    property_name    VARCHAR(255) NOT NULL,
    property_value   VARCHAR(255) NOT NULL,
    notes            TEXT,
    external_user_id UUID         NOT NULL,
    CONSTRAINT pk_external_users_properties PRIMARY KEY (id),
    CONSTRAINT FK_EXTERNAL_USERS_PROPERTIES_ON_USER FOREIGN KEY (external_user_id) REFERENCES external_users (id)
);

CREATE TABLE entidades_territoriales
(
    id     INTEGER GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    codigo VARCHAR(255),
    nombre VARCHAR(255),
    tipo   VARCHAR(255),
    CONSTRAINT pk_entidades_territoriales PRIMARY KEY (id)
);