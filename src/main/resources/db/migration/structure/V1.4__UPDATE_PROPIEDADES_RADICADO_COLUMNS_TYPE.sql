-- Cambiar tipos de columnas de PropiedadesRadicado de VARCHAR a INTEGER
-- Utilizamos NULLIF y COALESCE para manejar valores vacíos o no numéricos
ALTER TABLE radicados ALTER COLUMN prop_page TYPE INTEGER USING COALESCE(NULLIF(prop_page, '')::INTEGER, 0);
ALTER TABLE radicados ALTER COLUMN prop_x TYPE INTEGER USING COALESCE(NULLIF(prop_x, '')::INTEGER, 0);
ALTER TABLE radicados ALTER COLUMN prop_y TYPE INTEGER USING COALESCE(NULLIF(prop_y, '')::INTEGER, 0);
ALTER TABLE radicados ALTER COLUMN prop_height TYPE INTEGER USING COALESCE(NULLIF(prop_height, '')::INTEGER, 0);
ALTER TABLE radicados ALTER COLUMN prop_width TYPE INTEGER USING COALESCE(NULLIF(prop_width, '')::INTEGER, 0);

-- Ag<PERSON>gar columna prop_rotation_degrees que falta
ALTER TABLE radicados ADD COLUMN IF NOT EXISTS prop_rotation_degrees INTEGER DEFAULT 0;
