-- Agregar campos para correspondencia en radicados
-- Issues #8 y #10: Unificación de campos de correspondencia

-- Agregar columnas para la gestión completa de correspondencia
ALTER TABLE radicados 
ADD COLUMN IF NOT EXISTS remitente_id UUID,
ADD COLUMN IF NOT EXISTS destino_id UUID,
ADD COLUMN IF NOT EXISTS destinatario_interno VARCHAR(255),
ADD COLUMN IF NOT EXISTS remitente_interno VARCHAR(255);

-- Agregar foreign keys solo para referencias a otras tablas
ALTER TABLE radicados
ADD CONSTRAINT fk_radicados_remitente 
    FOREIGN KEY (remitente_id) REFERENCES external_users(id),
ADD CONSTRAINT fk_radicados_destino 
    FOREIGN KEY (destino_id) REFERENCES secciones(id);

-- Agregar índices para optimizar consultas de FK
CREATE INDEX IF NOT EXISTS idx_radicados_remitente_id ON radicados(remitente_id);
CREATE INDEX IF NOT EXISTS idx_radicados_destino_id ON radicados(destino_id);

-- Comentarios para documentación
COMMENT ON COLUMN radicados.remitente_id IS 'Usuario externo que envía el documento (para recepción)';
COMMENT ON COLUMN radicados.destino_id IS 'Oficina o sección a la cual se dirige el documento';
COMMENT ON COLUMN radicados.destinatario_interno IS 'Usuario interno destinatario específico (texto libre)';
COMMENT ON COLUMN radicados.remitente_interno IS 'Usuario interno que actúa como remitente (texto libre)'; 