package co.com.gedsys.base;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@ConfigurationPropertiesScan(basePackages = "co.com.gedsys.base.infrastructure.config")
@EnableConfigurationProperties
@EntityScan(basePackages = "co.com.gedsys.base.infrastructure.data_access")
@EnableJpaRepositories(basePackages = "co.com.gedsys.base.infrastructure.data_access.repository")
@SpringBootApplication(scanBasePackages = "co.com.gedsys.base.application, co.com.gedsys.base.adapter, co.com.gedsys.base.infrastructure, co.com.gedsys.base.domain")
public class Gedsys2CoreApplication {

    public static void main(String[] args) {
        SpringApplication.run(Gedsys2CoreApplication.class, args);
    }

}
