package co.com.gedsys.base.util;

import java.text.Normalizer;
import java.util.Locale;
import java.util.regex.Pattern;

public class CamelCaseUtil {
    private final String inputText;

    public CamelCaseUtil( String inputText) {
        if (inputText.isEmpty()) {
            throw new IllegalArgumentException("Input text cannot be null or empty");
        }
        this.inputText = inputText;
    }

    /***
     * Converts a string to camel case, handling spanish and non-ASCII characters.
     * @return the camel case string
     */
    public String camelize() {
        String textCleaned = clean(this.inputText);
        return convertToCamelCase(textCleaned);
    }

    private String convertToCamelCase(String text) {
        String[] words = text.split("[^a-zA-Z0-9]+");
        StringBuilder camelCaseString = new StringBuilder();
        for (int i = 0; i < words.length; i++) {
            String word = words[i];
            if (i == 0) {
                camelCaseString.append(word.toLowerCase(Locale.ROOT));
            } else {
                camelCaseString.append(word.substring(0, 1).toUpperCase(Locale.ROOT));
                camelCaseString.append(word.substring(1).toLowerCase(Locale.ROOT));
            }
        }
        return camelCaseString.toString();
    }

    /***
     * Normalize the string to remove accents and special characters
     * @param input the input string
     * @return the normalized string
     */
    private String clean(String input) {
        String normalized = Normalizer.normalize(input, Normalizer.Form.NFD);
        Pattern pattern = Pattern.compile("\\p{InCombiningDiacriticalMarks}+");
        return pattern.matcher(normalized).replaceAll("");
    }
}
