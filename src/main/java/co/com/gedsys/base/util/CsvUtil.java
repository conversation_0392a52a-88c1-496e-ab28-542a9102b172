package co.com.gedsys.base.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Utilidad para procesar archivos CSV que contienen datos con formato código y nombre.
 */
public class CsvUtil {

    private final InputStream csv;

    /**
     * Constructor que recibe el flujo de entrada del archivo CSV.
     *
     * @param csv El InputStream que contiene el archivo CSV a procesar
     */
    public CsvUtil(InputStream csv) {
        this.csv = csv;
    }

    /**
     * Lee el contenido del archivo CSV y lo convierte en una lista de mapas.
     * El archivo debe tener un encabezado con las columnas 'codigo' y 'nombre'.
     * Soporta delimitadores ',' o ';' pero no ambos en el mismo archivo.
     *
     * @return Lista de mapas donde cada mapa representa una fila con las claves 'codigo' y 'nombre'
     * @throws RuntimeException Si hay errores al leer el archivo
     * @throws IllegalArgumentException Si el formato del archivo no es válido
     */
    public List<Map<String, String>> read() {
        List<Map<String, String>> rows = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(csv, StandardCharsets.UTF_8))) {
            String line = reader.readLine();
            if (line == null) {
                return rows;
            }

            // Check for and remove BOM if present
            if (line.startsWith("\uFEFF")) {
                line = line.substring(1);
            }

            String delimiter = detectDelimiter(line);

            // Use Pattern.quote to ensure delimiter is treated as a literal in regex
            String[] headers = line.split(Pattern.quote(delimiter));

            // Trim whitespace from headers
            for (int i = 0; i < headers.length; i++) {
                headers[i] = headers[i].trim();
            }

            if (headers.length < 2 || !headers[0].equalsIgnoreCase("codigo") || !headers[1].equalsIgnoreCase("nombre")) {
                throw new IllegalArgumentException("El archivo CSV debe tener columnas 'codigo' y 'nombre' en ese orden.");
            }

            while ((line = reader.readLine()) != null) {
                if (line.trim().isEmpty()) {
                    continue; // Skip empty lines
                }
                String[] values = line.split(Pattern.quote(delimiter));
                if (values.length >= 2) {
                    Map<String, String> row = new HashMap<>();
                    row.put("codigo", values[0].trim());
                    row.put("nombre", values[1].trim());
                    rows.add(row);
                }
            }
        } catch (IOException e) {
            throw new RuntimeException("Error reading CSV file", e);
        }
        return rows;
    }

    private String detectDelimiter(String line) {
        if (line.contains(",") && line.contains(";")) {
            throw new IllegalArgumentException("El archivo CSV no puede contener ambos delimitadores: ',' y ';'");
        }
        return line.contains(";") ? ";" : ",";
    }
}
