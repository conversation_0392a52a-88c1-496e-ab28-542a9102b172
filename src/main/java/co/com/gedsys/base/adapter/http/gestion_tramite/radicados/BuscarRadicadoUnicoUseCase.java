package co.com.gedsys.base.adapter.http.gestion_tramite.radicados;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.mapper.RadicadoApplicationLayerMapper;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class BuscarRadicadoUnicoUseCase implements UseCase<UUID, RadicadoDTO> {

    private final RadicadoRepository radicadoRepository;
    private final RadicadoApplicationLayerMapper mapper;

    public BuscarRadicadoUnicoUseCase(RadicadoRepository radicadoRepository, RadicadoApplicationLayerMapper mapper) {
        this.radicadoRepository = radicadoRepository;
        this.mapper = mapper;
    }

    @Override
    public RadicadoDTO execute(UUID input) {
        return radicadoRepository.findById(input)
                .map(mapper::toDTO)
                .orElseThrow(() -> new EntityNotExistsException("Radicado con id " + input + " no encontrado"));
    }
}
