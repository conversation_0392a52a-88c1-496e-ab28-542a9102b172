package co.com.gedsys.base.adapter.http.gestion_tramite.documentos;

import co.com.gedsys.base.application.usecase.gestion_tramite.BuscarDocumentoUseCase;
import co.com.gedsys.base.application.usecase.gestion_tramite.BusquedaPorParametrosQuery;
import co.com.gedsys.base.application.usecase.gestion_tramite.BusquedaPorParametrosUseCase;
import co.com.gedsys.base.application.usecase.gestion_tramite.datastructure.BuscarDocumentoQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/gestion-tramite/busqueda/documentos")
public class BusquedaDocumentosControllers implements BusquedaDocumentosAPI {

    private final BuscarDocumentoUseCase buscarDocumentoUseCase;
    private final BusquedaPorParametrosUseCase busquedaPorParametrosUseCase;
    private final BusquedaDocumentosControllerMapper mapper;

    @Autowired
    public BusquedaDocumentosControllers(BuscarDocumentoUseCase buscarDocumentoUseCase,
                                         BusquedaPorParametrosUseCase busquedaPorParametrosUseCase,
                                         BusquedaDocumentosControllerMapper mapper) {
        this.buscarDocumentoUseCase = buscarDocumentoUseCase;
        this.busquedaPorParametrosUseCase = busquedaPorParametrosUseCase;
        this.mapper = mapper;
    }

    @Override
    public ResponseEntity<RespuestaDocumentoEncontrado> buscarPorIdentificador(UUID id) {
        var query = new BuscarDocumentoQuery(id);
        var useCaseOutput = buscarDocumentoUseCase.execute(query);
        var response = mapper.toResponse(useCaseOutput);
        return ResponseEntity.ok(response);
    }

    @Override
    public ResponseEntity<List<RespuestaDocumentoEncontrado>> buscarPorParametros(UUID unidadDocumentalId) {
        var query = new BusquedaPorParametrosQuery(unidadDocumentalId);
        var response = busquedaPorParametrosUseCase.execute(query).stream().map(mapper::toResponse).toList();
        return ResponseEntity.ok(response);
    }

}
