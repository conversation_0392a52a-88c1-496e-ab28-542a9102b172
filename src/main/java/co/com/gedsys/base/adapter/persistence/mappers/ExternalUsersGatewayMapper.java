package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.infrastructure.data_access.ExternalUserEntity;
import co.com.gedsys.base.infrastructure.data_access.ExternalUsersPropertyEntity;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserProperty;
import org.mapstruct.*;

@Mapper(componentModel = "spring",
        uses = {ExternalUsersPropertyGatewayMapper.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ExternalUsersGatewayMapper {

    ExternalUserEntity toEntity(ExternalUser externalUserEntity);

    ExternalUser toDomain(ExternalUserEntity save);

    @Mapping(target = "properties", ignore = true)
    ExternalUserEntity synchronize(ExternalUser domainEntity, @MappingTarget ExternalUserEntity entityToUpdate);

    @Mapping(target = "owner", ignore = true)
    @Mapping(target = "id", ignore = true)
    ExternalUsersPropertyEntity toEntity(ExternalUserProperty domainEntity, @MappingTarget ExternalUsersPropertyEntity entity);
}
