package co.com.gedsys.base.adapter.http.gestion_tramite.radicados;

import co.com.gedsys.base.application.dto.RadicadoDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
public class GestionTramiteRadicadosAPIImpl implements GestionTramiteRadicadosAPI {

    private final BuscarRadicadoUnicoUseCase buscarRadicadoUnicoUseCase;

    public GestionTramiteRadicadosAPIImpl(BuscarRadicadoUnicoUseCase buscarRadicadoUnicoUseCase) {
        this.buscarRadicadoUnicoUseCase = buscarRadicadoUnicoUseCase;
    }

    @Override
    public ResponseEntity<RadicadoDTO> buscarPorIdentificador(UUID id) {
        return ResponseEntity.ok(buscarRadicadoUnicoUseCase.execute(id));
    }
}
