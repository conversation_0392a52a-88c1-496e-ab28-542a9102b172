package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.infrastructure.data_access.AccessControlEntity;
import co.com.gedsys.base.adapter.persistence.mappers.AccessControlDataAccessMapper;
import co.com.gedsys.base.infrastructure.data_access.repository.AccessControlJpaRepository;
import co.com.gedsys.base.domain.control_acceso.AccessControlPolicy;
import co.com.gedsys.base.domain.control_acceso.AccessControlRepository;
import co.com.gedsys.base.domain.control_acceso.AccessLevel;
import co.com.gedsys.base.domain.control_acceso.InterestGroup;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
@RequiredArgsConstructor
public class AccessControlGateway implements AccessControlRepository {

    private final AccessControlDataAccessMapper mapper;

    private final AccessControlJpaRepository jpaRepository;

    @Override
    public Optional<AccessControlPolicy> find(AccessLevel accessLevel,
                                              String code,
                                              InterestGroup interestGroup,
                                              String detail) {
        var exampleMatcher = ExampleMatcher.matching()
                .withIgnorePaths("permissions, id")
                .withMatcher("accessLevel", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("code", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("interestGroup", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("detail", ExampleMatcher.GenericPropertyMatchers.exact());
        var prototype = new AccessControlEntity()
                .setAccessLevel(accessLevel)
                .setCode(code)
                .setInterestGroup(interestGroup)
                .setDetail(detail);
        var example = Example.of(prototype, exampleMatcher);
        var result = jpaRepository.findOne(example);
        return result.map(mapper::toDomain);
    }

    @Override
    public List<AccessControlPolicy> search(Searchable searchable) {
        var exampleMatcher = ExampleMatcher.matchingAll()
                .withIgnorePaths("permissions", "id")
                .withMatcher("accessLevel", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("code", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("interestGroup", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("detail", ExampleMatcher.GenericPropertyMatchers.exact())
                .withIgnoreNullValues();
        var prototype = new AccessControlEntity()
                .setAccessLevel(searchable.accessLevel())
                .setCode(searchable.code())
                .setInterestGroup(searchable.interestGroup())
                .setDetail(searchable.detail());
        var example = Example.of(prototype, exampleMatcher);
        return jpaRepository.findAll(example).stream().map(mapper::toDomain).toList();
    }


    @Override
    public AccessControlPolicy save(AccessControlPolicy domainEntity) {
        var entity = mapper.toEntity(domainEntity);
        return mapper.toDomain(jpaRepository.save(entity));
    }

    @Override
    public Optional<AccessControlPolicy> findById(UUID uuid) {
        return jpaRepository.findById(uuid).map(mapper::toDomain);
    }

    @Override
    public List<AccessControlPolicy> findAll() {
        throw new RuntimeException("Not implemented");
    }

    @Override
    public boolean checkStock(String uuid) {
        return jpaRepository.existsById(UUID.fromString(uuid));
    }

    @Override
    public boolean checkStock(AccessControlPolicy entity) {
        throw new RuntimeException("Not implemented");
    }

    @Override
    public void delete(UUID uuid) {
        jpaRepository.deleteById(uuid);
    }
}
