package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.adapter.persistence.mappers.DocumentoDataAccessMapper;
import co.com.gedsys.base.infrastructure.data_access.repository.DocumentJpaRepository;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.documento.ParametrosBusquedaDocumento;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
@RequiredArgsConstructor
public class DocumentGateway implements DocumentoRepository {
    private final DocumentJpaRepository jpaRepository;
    private final DocumentoDataAccessMapper map;

    @Override
    public Documento save(Documento domainEntity) {
        var entity = map.toEntity(domainEntity);
        return map.toDomain(jpaRepository.save(entity));
    }

    @Override
    public Optional<Documento> findById(UUID uuid) {
        return jpaRepository.findById(uuid)
                .map(map::toDomain);
    }

    @Override
    public List<Documento> findAll() {
        return jpaRepository.findAll().stream()
                .map(map::toDomain)
                .toList();
    }

    @Override
    public boolean checkStock(String name) {
        return false;
    }

    @Override
    public boolean checkStock(Documento entity) {
        return false;
    }

    @Override
    public void delete(UUID uuid) {
        jpaRepository.deleteById(uuid);
    }

    @Override
    public List<Documento> buscarPorParametros(ParametrosBusquedaDocumento parametros) {
        if (parametros == null) {
            return jpaRepository.findAll().stream().map(map::toDomain).toList();
        }
        var example = map.toEntity(parametros);
        ExampleMatcher matcher = ExampleMatcher
                .matchingAll()
                .withIgnoreNullValues()
                .withMatcher("unidadDocumentalId", ExampleMatcher.GenericPropertyMatchers.exact());
        return jpaRepository.findAll(Example.of(example, matcher)).stream().map(map::toDomain).toList();
    }
}
