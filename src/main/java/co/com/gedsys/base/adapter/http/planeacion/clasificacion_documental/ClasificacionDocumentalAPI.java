package co.com.gedsys.base.adapter.http.planeacion.clasificacion_documental;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.UUID;

public interface ClasificacionDocumentalAPI {
    @PostMapping(path = "", consumes = "application/json")
    void create(@RequestBody ClasificacionDocumentalRequest creationRequest);

    @PostMapping(path = "/{id}/tipos-documentales", consumes = "application/json")
    ClasificacionDocumentalDetail relacionarTiposDocumentales(
            @PathVariable UUID id,
            @RequestBody RelacionarTiposDocumentalesRequest request);

    @GetMapping(path = "", produces = "application/json")
    List<ClasificacionDocumentalItem> listarClasificacionesDocumentales();

    @GetMapping(path = "/{id}", produces = "application/json")
    ClasificacionDocumentalDetail buscarPorId(@PathVariable UUID id);

    record RelacionarTiposDocumentalesRequest(List<UUID> tiposDocumentales) {
    }

    record ClasificacionDocumentalRequest(String unidadProductora, String serieDocumental) {
    }

    record ClasificacionDocumentalItem(UUID id, String codigo, String nombreSeccion, String nombreSerie,
                                       List<TipoDocumentalDS> tiposDocumentales) {
    }

    record ClasificacionDocumentalDetail(UUID id, String codigo, SeccionDS seccion, SerieDS serie,
                                         List<TipoDocumentalDS> tiposDocumentales) {
    }

    record SeccionDS(UUID id, String nombre, String codigo) {
    }

    record SerieDS(UUID id, String nombre, String codigo) {
    }

    record TipoDocumentalDS(UUID id, String nombre) {
    }
}
