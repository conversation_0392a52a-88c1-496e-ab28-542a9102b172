package co.com.gedsys.base.adapter.http.planeacion.usuarios_externos;

import co.com.gedsys.base.application.dto.ExternalUserDto;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.AddPropertiesExternalUserCommand;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.CreateExternalUserCommand;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.UpdateExternalUserCommand;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;
import java.util.UUID;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface ExternalUsersHttpMapper {
    CreateExternalUserCommand toCommand(ExternalUserCreateRequest request);

    ExternalUserInfo toResponse(ExternalUserDto dto);

    @Mapping(target = "identification.identificationNumber", source = "identificationNumber")
    @Mapping(target = "identification.identificationType", source = "identificationType")
    ExternalUserItem toItem(ExternalUserDto dto);

    UpdateExternalUserCommand toCommand(UUID id, ExternalUserUpdateRequest request);

    AddPropertiesExternalUserCommand.PropertyDS toCommand(ExternalUserPropertyRegistration request);

    AddPropertiesExternalUserCommand toCommand(UUID externalUserId, List<ExternalUserPropertyRegistration> properties);
}
