package co.com.gedsys.base.adapter.http.planeacion.unidad_documental;

import co.com.gedsys.base.application.usecase.planeacion.unidades_documentales.BuscarUnidadDocumentalQuery;
import co.com.gedsys.base.application.usecase.planeacion.unidades_documentales.ListarUnidadDocumentalQuery;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

public interface UnidadDocumentalAPI {
    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping(path = "", consumes = "application/json")
    void create(@Valid @RequestBody CreateUnidadDocumentalRequest request);

    @GetMapping(path = "", produces = "application/json")
    List<ListarUnidadDocumentalQuery> getAll();

    @GetMapping(path = "/{id}", produces = "application/json")
    BuscarUnidadDocumentalQuery getById(@PathVariable UUID id);
}
