package co.com.gedsys.base.adapter.http.planeacion.control_acceso;

import org.mapstruct.Mapper;

import co.com.gedsys.base.application.usecase.planeacion.control_acceso.AccessControlPolicyDto;
import co.com.gedsys.base.application.usecase.planeacion.control_acceso.FilterAccessControlPoliciesQuery;
import co.com.gedsys.base.application.usecase.planeacion.control_acceso.RegisterAccessControlCommand;

@Mapper(componentModel = "spring", unmappedTargetPolicy = org.mapstruct.ReportingPolicy.ERROR)
public interface AccessControlPlanningControllerMapper {

    AccessControlPolicyRepresentation toResponse(AccessControlPolicyDto dto);

    RegisterAccessControlCommand toCommand(AccessControlPolicyRepresentation request);

    FilterAccessControlPoliciesQuery toQuery(AccessControlPolicyRepresentation request);
}
