package co.com.gedsys.base.adapter.http.produccion.documentos;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.ArrayList;
import java.util.List;

public record SolicitudRecepcionDocumento(
        @NotBlank(message = "El título no puede estar vacío")
        String titulo,
        @NotBlank(message = "El fileId no puede estar vacío")
        String fileId,
        @NotBlank(message = "El tipoDocumentalId no puede estar vacío")
        String tipoDocumentalId,
        @NotBlank(message = "El unidadDocumentalId no puede estar vacío")
        String unidadDocumentalId,
        @NotBlank(message = "El autor no puede estar vacío")
        String autor,
        List<MetadadoProduccionDocumental> metadatos,
        @NotNull(message = "Las propiedadesRadicado no pueden ser nulas")
        EstructuraPropiedadesRadicado propiedadesRadicado,
        List<EstructuraAnexoDocumento> anexos,
        @NotBlank(message = "El remitenteId es requerido")
        String remitenteId,
        String destinatarioInterno
) {
    public SolicitudRecepcionDocumento {
        // Establecer valores predeterminados para campos opcionales
        metadatos = metadatos != null ? metadatos : new ArrayList<>();
        anexos = anexos != null ? anexos : new ArrayList<>();
    }
}
