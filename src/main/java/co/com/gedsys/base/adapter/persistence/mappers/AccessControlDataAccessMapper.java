package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.infrastructure.data_access.AccessControlEntity;
import co.com.gedsys.base.domain.control_acceso.AccessControlPermission;
import co.com.gedsys.base.domain.control_acceso.AccessControlPolicy;
import org.mapstruct.Mapper;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring",
        uses = {DataAccessClasificacionDocumentalMapper.class},
        unmappedTargetPolicy = org.mapstruct.ReportingPolicy.ERROR)
public interface AccessControlDataAccessMapper {

    AccessControlPolicy toDomain(AccessControlEntity entity);

    AccessControlEntity toEntity(AccessControlPolicy policy);

    default Set<AccessControlPermission> toSet(String permissions) {
        return permissions == null ? Set.of() : Arrays.stream(permissions.split(","))
                .map(AccessControlPermission::valueOf)
                .collect(Collectors.toSet());
    }

    default String toString(Set<AccessControlPermission> permissions) {
        return permissions.stream()
                .map(AccessControlPermission::name)
                .collect(Collectors.joining(","));
    }
}