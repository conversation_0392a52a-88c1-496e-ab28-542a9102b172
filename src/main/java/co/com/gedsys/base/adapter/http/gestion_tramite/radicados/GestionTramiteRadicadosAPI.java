package co.com.gedsys.base.adapter.http.gestion_tramite.radicados;

import co.com.gedsys.base.application.dto.RadicadoDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.UUID;

@RequestMapping("/api/v1/gestion-tramite/busqueda/radicados"  )
public interface GestionTramiteRadicadosAPI {
    @GetMapping(path = "/{id}", produces = "application/json")
    ResponseEntity<RadicadoDTO> buscarPorIdentificador(@PathVariable("id") UUID id);
}
