package co.com.gedsys.base.adapter.http.planeacion.control_acceso;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;
import java.util.UUID;

import co.com.gedsys.base.domain.control_acceso.AccessControlPermission;
import co.com.gedsys.base.domain.control_acceso.AccessLevel;
import co.com.gedsys.base.domain.control_acceso.InterestGroup;

public record AccessControlPolicyRepresentation(
        UUID id,
        @NotNull(message = "Access Level is required")
        AccessLevel accessLevel,
        @NotNull(message = "Code is required")
        @NotBlank(message = "The code field cannot be empty")
        String code,
        @NotNull(message = "Interest Group is required")
        InterestGroup interestGroup,
        @NotNull(message = "detail is required") @NotBlank(message = "The detail field cannot be empty")
        String detail,
        String notes,
        @Size(min = 1, message = "At least one permission is required")
        List<AccessControlPermission> permissions
) {
}
