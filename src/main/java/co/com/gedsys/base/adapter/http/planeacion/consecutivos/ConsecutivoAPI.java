package co.com.gedsys.base.adapter.http.planeacion.consecutivos;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.UUID;

public interface ConsecutivoAPI {

    @ResponseStatus(HttpStatus.CREATED)
    @PostMapping(path = "", consumes = "application/json")
    ResponseEntity<ConsecutivoRepresentation> create(@RequestBody CreateConsecutivoRequest request, UriComponentsBuilder uriBuilder);

    @GetMapping(path = "/{id}", produces = "application/json")
    ConsecutivoRepresentation find(@PathVariable UUID id);

    @GetMapping(path = "", produces = "application/json")
    List<ConsecutivoRepresentation> findAll();
}
