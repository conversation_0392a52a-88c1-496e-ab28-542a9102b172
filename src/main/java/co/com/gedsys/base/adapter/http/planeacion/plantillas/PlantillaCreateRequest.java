package co.com.gedsys.base.adapter.http.planeacion.plantillas;

import co.com.gedsys.base.domain.plantillas.TipoPlantilla;
import com.fasterxml.jackson.annotation.JsonCreator;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

public record PlantillaCreateRequest(
        @NotNull(message = "Debe especificar un nuevoTitulo para la plantilla") String titulo,
        @Pattern(regexp = "RECEPCION|PRODUCCION|ENVIO|CARGA", message = "El tipo de plantilla debe ser RECEPCION, PRODUCCION, ENVIO o CARGA")
        String tipo,
        Boolean producidoPorGedsys
) {
    @JsonCreator
    public PlantillaCreateRequest(String titulo, String tipo, Boolean producidoPorGedsys) {
        this.titulo = titulo;
        this.tipo = tipo == null || !tipo.matches("RECEPCION|PRODUCCION|ENVIO|CARGA") ? TipoPlantilla.PRODUCCION.name() : tipo;
        this.producidoPorGedsys = Boolean.TRUE.equals(producidoPorGedsys);
    }
}
