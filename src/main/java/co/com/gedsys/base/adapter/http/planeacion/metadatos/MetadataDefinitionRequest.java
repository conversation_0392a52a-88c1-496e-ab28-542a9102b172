package co.com.gedsys.base.adapter.http.planeacion.metadatos;

import co.com.gedsys.base.domain.metadato.enums.FormatoMetadatoEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

public record MetadataDefinitionRequest(@NotEmpty(message = "Debe asignar un nombre al metadato") String nombre,
                                        String descripcion,
                                        @NotNull FormatoMetadatoEnum formato,
                                        @Pattern(regexp = "CONTENIDO|GESTION", message = "El tipo debe ser CONTENIDO o GESTION")
                                        String tipo,
                                        String rules) {
}
