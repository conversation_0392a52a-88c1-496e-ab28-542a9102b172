package co.com.gedsys.base.adapter.http.gestion_tramite.documentos;

import java.time.LocalDateTime;
import java.util.List;

public record RespuestaDocumentoEncontrado(
        String id,
        String autor,
        String codigoClasificatorio,
        EstadoDocumento estado,
        String fileId,
        String tipoDocumentalId,
        String tipoDocumentalNombre,
        String titulo,
        String unidadDocumentalId,
        String unidadDocumentalNombre,
        List<MetadataDS> metadatos,
        List<FirmaDS> firmas,
        List<AprobacionDS> aprobaciones,
        List<AnexoDS> anexos
) {
    public record MetadataDS(
            String id,
            String patron,
            String nombre,
            String tipo,
            String formato,
            String valor
    ) {
    }

    public record FirmaDS(
            String id,
            String owner,
            String estado,
            LocalDateTime firmadoEn,
            int height,
            String observaciones,
            int page,
            int width,
            int x,
            int y
    ) {
    }

    public record AprobacionDS(
            String id,
            String aprobador,
            String observaciones,
            LocalDateTime aprobadoEn,
            String estado
    ) {
    }

    public enum EstadoDocumento {
        BORRADOR,
        DESCARTADO,
        EN_TRAMITE,
        FINALIZADO,
    }

    public record AnexoDS(
            String id,
            String nombre,
            String descripcion,
            String fileId,
            String hash,
            Long bytes,
            String extension
    ) {
    }
}
