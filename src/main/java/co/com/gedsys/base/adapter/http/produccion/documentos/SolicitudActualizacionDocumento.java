package co.com.gedsys.base.adapter.http.produccion.documentos;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import java.util.LinkedHashMap;
import java.util.List;

public record SolicitudActualizacionDocumento(
        @NotBlank(message = "El título del documento no puede estar vacío")
        String titulo,
        @NotEmpty(message = "Debe especificar al menos un metadato")
        LinkedHashMap<String, String> metadatos,
        @Valid
        List<RegistroAnexo> anexos
) {
    public record RegistroAnexo(
            @NotBlank(message = "El nombre del anexo no puede estar vacío")
            String nombre,
            String descripcion,
            @NotBlank(message = "El archivo del anexo no puede estar vacío")
            String fileId,
            String hash,
            @Min(value = 1, message = "El tamaño del anexo no puede ser negativo")
            Long bytes,
            @NotBlank(message = "La extensión del anexo no puede estar vacío")
            String extension
    ) {
    }
}
