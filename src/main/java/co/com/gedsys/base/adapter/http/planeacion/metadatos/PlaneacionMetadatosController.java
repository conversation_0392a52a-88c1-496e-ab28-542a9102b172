package co.com.gedsys.base.adapter.http.planeacion.metadatos;

import co.com.gedsys.base.application.usecase.planeacion.metadatos.ActualizarDefinicionUseCase;
import co.com.gedsys.base.application.usecase.planeacion.metadatos.AgregarDefinicionMetadatoUseCase;
import co.com.gedsys.base.application.usecase.planeacion.metadatos.BuscarDefinicionMetadatoUseCase;
import co.com.gedsys.base.application.usecase.planeacion.metadatos.ListarDefinicionesMetadatosUseCase;
import co.com.gedsys.base.domain.metadato.enums.FormatoMetadatoEnum;
import co.com.gedsys.base.domain.metadato.enums.MetadataRulesEnum;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;


@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/planeacion/metadatos")
public class PlaneacionMetadatosController implements MetadatoAPI {

    private final AgregarDefinicionMetadatoUseCase createUseCase;
    private final ActualizarDefinicionUseCase updateUseCase;
    private final ListarDefinicionesMetadatosUseCase listUseCase;
    private final BuscarDefinicionMetadatoUseCase findByPatternUseCase;

    private final MetadatosPlaneacionHttpMapper mapper;

    @Override
    public ResponseEntity<Void> create(MetadataDefinitionRequest request) {
        createUseCase.execute(mapper.toCommand(request));
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<List<TiposPermitidosResponse>> getAllowedTypes() {
        return ResponseEntity.ok(
                Arrays.stream(TipoMetadatoEnum.values())
                        .map(TiposPermitidosResponse::new)
                        .toList());
    }

    @Override
    public ResponseEntity<List<FormatoPermitidosResponse>> getAllowedFormats() {
        final List<FormatoPermitidosResponse> formatosPermitidos = new ArrayList<>();
        Arrays.stream(FormatoMetadatoEnum.values())
                .forEach(f -> formatosPermitidos
                        .add(new FormatoPermitidosResponse(f, f.getAllowedRules())));
        return ResponseEntity.ok(formatosPermitidos);
    }

    @Override
    public ResponseEntity<List<ReglasDisponiblesResponse>> getDefaultRules() {
        final List<ReglasDisponiblesResponse> reglasDisponibles = new ArrayList<>();
        Arrays.stream(MetadataRulesEnum.values())
                .forEach(r -> reglasDisponibles
                        .add(new ReglasDisponiblesResponse(r, r.getAllowedValueType().getSimpleName())));
        return ResponseEntity.ok(reglasDisponibles);
    }

    @Override
    public Set<DefinicionMetadatoResponse> getDefiniciones() {
        var definiciones = listUseCase.execute(null);
        return mapper.toResponse(definiciones);
    }

    @Override
    public DefinicionMetadatoResponse getDefinicion(String patron) {
        return mapper.toResponse(findByPatternUseCase.execute(patron));
    }

    @Override
    public ResponseEntity<DefinicionMetadatoResponse> update(UUID id, UpdateDefinicionRequest request) {

        final var response = mapper.toResponse(
                updateUseCase.execute(
                        mapper.toCommand(id, request)
                ));
        return ResponseEntity.ok(response);
    }
}
