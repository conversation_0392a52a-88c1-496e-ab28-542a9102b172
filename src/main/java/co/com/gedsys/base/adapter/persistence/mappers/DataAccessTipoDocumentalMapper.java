package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.infrastructure.data_access.TipoDocumentalEntity;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface DataAccessTipoDocumentalMapper {


    @Mapping(target = "renombrar", ignore = true)
    TipoDocumental toDomain(TipoDocumentalEntity entity);

    @Mapping(target = "nombre", expression = "java(tipoDocumental.nombre())")
    TipoDocumentalEntity toEntity(TipoDocumental tipoDocumental);
}
