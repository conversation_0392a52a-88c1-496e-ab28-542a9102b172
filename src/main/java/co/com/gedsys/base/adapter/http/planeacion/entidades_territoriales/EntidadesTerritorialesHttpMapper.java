package co.com.gedsys.base.adapter.http.planeacion.entidades_territoriales;

import java.util.List;
import java.util.Map;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import co.com.gedsys.base.application.dto.EntidadTerritorialDto;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.CrearEntidadTerritorialCommand;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface EntidadesTerritorialesHttpMapper {
    EntidadTerritorialResponse toResponse(EntidadTerritorialDto entidadTerritorialDto);

    List<EntidadTerritorialResponse> toResponse(List<EntidadTerritorialDto> entidadTerritorialDtos);

    CrearEntidadTerritorialCommand toCommand(Map<String, String> map);
}
