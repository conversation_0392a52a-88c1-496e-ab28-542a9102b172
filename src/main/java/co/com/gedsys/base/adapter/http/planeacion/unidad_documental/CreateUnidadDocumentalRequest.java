package co.com.gedsys.base.adapter.http.planeacion.unidad_documental;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.UUID;

public record CreateUnidadDocumentalRequest(
        @NotBlank(message = "La unidad documental debe tener un nombre")
        String nombre,
        String descripcion,
        @NotNull(message = "La unidad documental debe tener una clasificación")
        UUID clasificacion) {
}
