package co.com.gedsys.base.adapter.http.planeacion.metadatos;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;
import java.util.UUID;

public interface MetadatoAPI {

    @PostMapping(consumes = "application/json")
    ResponseEntity<Void> create(@RequestBody @Valid MetadataDefinitionRequest request);

    @GetMapping(path = "tipos-permitidos")
    ResponseEntity<List<TiposPermitidosResponse>> getAllowedTypes();

    @GetMapping(path = "formatos-permitidos")
    ResponseEntity<List<FormatoPermitidosResponse>> getAllowedFormats();

    @GetMapping(path = "reglas-disponibles")
    ResponseEntity<List<ReglasDisponiblesResponse>> getDefaultRules();

    @GetMapping(produces = "application/json")
    Set<DefinicionMetadatoResponse> getDefiniciones();

    @GetMapping(path = "/{patron}")
    DefinicionMetadatoResponse getDefinicion(@PathVariable String patron);

    @PatchMapping(path = "/{id}")
    ResponseEntity<DefinicionMetadatoResponse> update(@PathVariable @Valid @NotNull UUID id,
                                                      @RequestBody UpdateDefinicionRequest request);

}
