package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.adapter.persistence.mappers.PlantillaEntityMapper;
import co.com.gedsys.base.domain.plantillas.EstadoPlantilla;
import co.com.gedsys.base.domain.plantillas.Plantilla;
import co.com.gedsys.base.domain.plantillas.PlantillaRepository;
import co.com.gedsys.base.domain.plantillas.TipoPlantilla;
import co.com.gedsys.base.infrastructure.data_access.repository.PlantillaJpaRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


@Repository
@RequiredArgsConstructor
public class PlantillaGateway implements PlantillaRepository {

    private final PlantillaJpaRepository jpaRepository;
    private final PlantillaEntityMapper mapper;


    @Override
    public Plantilla save(Plantilla plantilla) {
        return mapper.toDomain(jpaRepository.save(mapper.toEntity(plantilla)));
    }

    @Override
    public Optional<Plantilla> findById(UUID id) {
        return jpaRepository.findById(id).map(mapper::toDomain);
    }

    @Override
    public List<Plantilla> findAll() {
        return mapper.toDomainList(jpaRepository.findAll());
    }

    @Override
    public boolean checkStock(String name) {
        return jpaRepository.existsByTituloIgnoreCase(name);
    }

    @Override
    public boolean checkStock(Plantilla entity) {
        return false;
    }

    @Override
    public void delete(UUID uuid) {

    }

    @Override
    public List<Plantilla> findByTipoDocumental(List<String> tiposDocumentales) {
        return jpaRepository.findByTipoDocumentalNombreInIgnoreCaseAndEstado(tiposDocumentales, EstadoPlantilla.ACTIVA)
                .stream()
                .map(mapper::toDomain)
                .toList();
    }

    @Override
    public List<Plantilla> filtrarPorTipo(TipoPlantilla tipoPlantilla) {
        return jpaRepository.findByTipoPlantilla(tipoPlantilla)
                .stream()
                .map(mapper::toDomain)
                .toList();
    }
}
