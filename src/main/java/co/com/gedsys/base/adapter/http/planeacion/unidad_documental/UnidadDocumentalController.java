package co.com.gedsys.base.adapter.http.planeacion.unidad_documental;

import co.com.gedsys.base.application.usecase.planeacion.unidades_documentales.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(path = "/api/v1/planeacion/unidades-documentales")
@RequiredArgsConstructor
public class UnidadDocumentalController implements UnidadDocumentalAPI {
    private final CrearUnidadDocumentalUseCase crearUnidadUseCase;
    private final ListarUnidadesDocumentalesUseCase listarUnidadesUseCase;
    private final BuscarUnidadDocumentalUseCase buscarUnidadUseCase;

    @Override
    public void create(CreateUnidadDocumentalRequest request) {
        var command = new CrearUnidadDocumentalCommand(request.nombre(), request.descripcion(), request.clasificacion());
        crearUnidadUseCase.execute(command);
    }

    @Override
    public List<ListarUnidadDocumentalQuery> getAll() {
        return listarUnidadesUseCase.execute(null);
    }

    @Override
    public BuscarUnidadDocumentalQuery getById(UUID id) {
        return buscarUnidadUseCase.execute(id);
    }
}