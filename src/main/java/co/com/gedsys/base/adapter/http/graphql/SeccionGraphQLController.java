package co.com.gedsys.base.adapter.http.graphql;

import java.util.List;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.graphql.client.HttpGraphQlClient;
import org.springframework.web.bind.annotation.*;

import reactor.core.publisher.Mono;

@RestController
@RequestMapping(path = "/api/v1/graphql/secciones")
public class SeccionGraphQLController {

    private final HttpGraphQlClient graphQLClient;

    public SeccionGraphQLController(HttpGraphQlClient graphQLClient) {
        this.graphQLClient = graphQLClient;
    }


    @Cacheable("secciones")
    @PostMapping
    public Mono<List<Object>> listar(@RequestBody GraphQLClientRequest request) {
        var response = graphQLClient.document(request.query())
        .operationName(request.operationName())
        .variables(request.variables())
        .retrieve("secciones")
        .toEntityList(Object.class);
        return response;
    }

    

}
