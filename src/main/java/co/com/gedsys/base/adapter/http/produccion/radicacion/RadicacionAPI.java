package co.com.gedsys.base.adapter.http.produccion.radicacion;

import co.com.gedsys.base.application.dto.RadicadoDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.UUID;

@RequestMapping("/api/v1/produccion/radicados")
public interface RadicacionAPI {

    @Operation(summary = "Buscar radicado por ID", description = "Busca un documento radicado en el sistema por su identificador único")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Radicado encontrado exitosamente",
                    content = @Content(schema = @Schema(implementation = RadicadoDTO.class))),
            @ApiResponse(responseCode = "404", description = "Radicado no encontrado"),
            @ApiResponse(responseCode = "500", description = "Error interno del servidor")
    })
    @GetMapping("/{id}")
    RadicadoDTO buscarRadicadoPorId(@PathVariable UUID id);
}
