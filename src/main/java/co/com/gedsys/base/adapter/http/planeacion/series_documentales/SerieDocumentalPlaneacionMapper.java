package co.com.gedsys.base.adapter.http.planeacion.series_documentales;

import co.com.gedsys.base.application.usecase.planeacion.series_documentales.RegistrarSerieDocumentalCommand;
import co.com.gedsys.base.application.usecase.planeacion.series_documentales.SerieDocumentalDto;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface SerieDocumentalPlaneacionMapper {
    RegistrarSerieDocumentalCommand toCommand(RegistrarSerieDocumentalRequest request);

    SerieDocumentalItem toItem(SerieDocumentalDto dto);
}
