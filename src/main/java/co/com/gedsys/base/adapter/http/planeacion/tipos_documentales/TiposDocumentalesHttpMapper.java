package co.com.gedsys.base.adapter.http.planeacion.tipos_documentales;

import co.com.gedsys.base.application.dto.TipoDocumentalDTO;
import co.com.gedsys.base.application.dto.UnidadDocumentalDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface TiposDocumentalesHttpMapper {
    @Mapping(target = "codigo", source = "codigoClasificatorio")
    UnidadesDisponiblesPorTipoDS toDS(UnidadDocumentalDto unidad);

    TipoDocumentalItem toItem(TipoDocumentalDTO dto);

    TipoDocumentalDetail toDetail(TipoDocumentalDTO dto);
}
