package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.adapter.persistence.mappers.DefinicionMetadatosDataAccessMapper;
import co.com.gedsys.base.domain.metadato.DefinicionMetadato;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.infrastructure.data_access.repository.DefinicionMetadatoJpaRepository;
import co.com.gedsys.base.util.CamelCaseUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class DefinicionMetadatosGateway implements DefinicionMetadatosRepository {

    private final DefinicionMetadatoJpaRepository repository;
    private final DefinicionMetadatosDataAccessMapper mapper;

    @Override
    public Set<DefinicionMetadato> buscarPorPatrones(List<String> patrones) {
        return repository.findByPatronIn(patrones).stream()
                .map(mapper::toDomain)
                .collect(Collectors.toUnmodifiableSet());
    }

    @Override
    public Optional<DefinicionMetadato> buscarPatron(String input) {
        return repository.findByPatron(input).map(mapper::toDomain);
    }

    @Override
    public DefinicionMetadato save(DefinicionMetadato domainEntity) {
        return Optional.ofNullable(domainEntity)
                .map(mapper::toEntity)
                .map(repository::save)
                .map(mapper::toDomain)
                .orElse(null);
    }

    @Override
    public Optional<DefinicionMetadato> findById(UUID uuid) {
        return repository.findById(uuid).map(mapper::toDomain);
    }

    @Override
    public List<DefinicionMetadato> findAll() {
        return repository.findAll().stream().map(mapper::toDomain).toList();
    }

    @Override
    public boolean checkStock(String name) {
        if (name == null) return false;
        final var normalizedName = new CamelCaseUtil(name).camelize();
        return repository.existsByPatron(normalizedName);
    }

    @Override
    public boolean checkStock(DefinicionMetadato entity) {
        return false;
    }

    @Override
    public boolean checkStockByPatronAndTipo(String name, TipoMetadatoEnum tipo) {
        if (name == null) return false;
        final var normalizedName = new CamelCaseUtil(name).camelize();
        return repository.existsByPatronAndTipo(normalizedName, tipo);
    }

    @Override
    public void delete(UUID uuid) {

    }
}
