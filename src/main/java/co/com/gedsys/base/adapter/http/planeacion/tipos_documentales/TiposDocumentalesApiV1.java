package co.com.gedsys.base.adapter.http.planeacion.tipos_documentales;

import co.com.gedsys.base.domain.tipologia.EstadoTipoDocumental;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

import static org.springframework.http.HttpStatus.CREATED;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

public interface TiposDocumentalesApiV1 {

    @GetMapping(path = "", produces = APPLICATION_JSON_VALUE, params = {"estado"})
    List<TipoDocumentalItem> findByStatus(
            @RequestParam(value = "estado")
            EstadoTipoDocumental estado
    );

    @GetMapping(path = "/{id}",
            produces = "application/json")
    TipoDocumentalDetail getDetails(@PathVariable UUID id);

    @ResponseStatus(code = CREATED)
    @PostMapping(path = "", consumes = APPLICATION_JSON_VALUE)
    void create(@RequestBody CreateTipoDocumentalRequest request);

    @GetMapping(path = "/{tipoDocumentalId}/unidades-documentales", produces = "application/json")
    List<UnidadesDisponiblesPorTipoDS> getRelatedUnidadesDocumentales(@PathVariable UUID tipoDocumentalId);

}