package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.infrastructure.data_access.ExternalUsersPropertyEntity;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserProperty;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ExternalUsersPropertyGatewayMapper {

    @Mapping(target = "owner.properties", ignore = true)
    ExternalUsersPropertyEntity toEntity(ExternalUserProperty domainEntity);

    @Mapping(target = "owner.properties", ignore = true)
    ExternalUserProperty toDomain(ExternalUsersPropertyEntity entity);
}
