package co.com.gedsys.base.adapter.http.planeacion.consecutivos;

import co.com.gedsys.base.application.usecase.planeacion.consecutivos.BuscarConsecutivoUseCase;
import co.com.gedsys.base.application.usecase.planeacion.consecutivos.CrearConsecutivoUseCase;
import co.com.gedsys.base.application.usecase.planeacion.consecutivos.ListarConsecutivosUseCase;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.List;
import java.util.UUID;

@RequestMapping(path = "/api/v1/planeacion/consecutivos")
@RestController
@RequiredArgsConstructor
public class ConsecutivoController implements ConsecutivoAPI {
    private final CrearConsecutivoUseCase crearConsecutivoUseCase;
    private final BuscarConsecutivoUseCase buscarConsecutivoUseCase;
    private final ListarConsecutivosUseCase listarConsecutivosUseCase;
    private final ConsecutivoControllerMapper mapper;

    @Override
    public ResponseEntity<ConsecutivoRepresentation> create(CreateConsecutivoRequest request, UriComponentsBuilder uriBuilder) {
        var command = mapper.toCommand(request);
        var createdConsecutivo = mapper.toDS(crearConsecutivoUseCase.execute(command));
        URI location = uriBuilder.path("/api/v1/planeacion/consecutivos/{id}").buildAndExpand(createdConsecutivo.id()).toUri();
        return ResponseEntity.created(location).body(createdConsecutivo);
    }

    @Override
    public ConsecutivoRepresentation find(UUID id) {
        return mapper.toDS(buscarConsecutivoUseCase.execute(id));

    }

    @Override
    public List<ConsecutivoRepresentation> findAll() {
        return listarConsecutivosUseCase.execute(null)
                .stream()
                .map(mapper::toDS)
                .toList();
    }
}
