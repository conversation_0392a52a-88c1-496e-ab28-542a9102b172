package co.com.gedsys.base.adapter.http.planeacion.entidades_territoriales;

import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface EntidadesTerritorialesApiV1 {

    @PostMapping(path = "", consumes = "application/json", produces = "application/json")
    ResponseEntity<EntidadTerritorialResponse> create(@RequestBody @Valid EntidadesTerritorialesRequest request);

    @GetMapping(path = "", produces = "application/json")
    ResponseEntity<List<EntidadTerritorialResponse>> findAll();

    @GetMapping(path = "/{id}", produces = "application/json")
    ResponseEntity<EntidadTerritorialResponse> findById(@PathVariable Integer id);

    @PatchMapping(path = "/{id}", consumes = "application/json", produces = "application/json")
    ResponseEntity<EntidadTerritorialResponse> update(@PathVariable Integer id, @RequestBody EntidadesTerritorialesRequest request);

    @DeleteMapping(path = "/{id}", produces = "application/json")
    ResponseEntity<Void> delete(@PathVariable Integer id);

    @GetMapping(path = "/template", produces = "text/csv")
    ResponseEntity<byte[]> template(@RequestParam(name = "semicolon", required = false, defaultValue = "false") boolean semicolon);

    @PostMapping(path = "/batch", consumes = "multipart/form-data")
    ResponseEntity<Void> uploadCsv(@RequestParam("file") MultipartFile file);
}
