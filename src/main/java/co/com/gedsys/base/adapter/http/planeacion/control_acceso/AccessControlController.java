package co.com.gedsys.base.adapter.http.planeacion.control_acceso;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

import co.com.gedsys.base.application.usecase.planeacion.control_acceso.DeleteAccessControlUseCase;
import co.com.gedsys.base.application.usecase.planeacion.control_acceso.FilterAccessControlPoliciesUseCase;
import co.com.gedsys.base.application.usecase.planeacion.control_acceso.RegisterAccessControlUseCase;

import java.net.URI;
import java.util.List;
import java.util.UUID;

@RequestMapping("/api/v1/planeacion/access-control")
@RequiredArgsConstructor
@RestController
public class AccessControlController implements AccessControlAPI {
    private final AccessControlPlanningControllerMapper map;

    private final DeleteAccessControlUseCase deleteAccessControlUseCase;

    private final FilterAccessControlPoliciesUseCase filterAccessControlPoliciesUseCase;

    private final RegisterAccessControlUseCase registerAccessControlUseCase;

    @Override
    public ResponseEntity<AccessControlPolicyRepresentation> createAccessControlPolicy(AccessControlPolicyRepresentation request,
                                                                                       UriComponentsBuilder uriBuilder) {
        var command = map.toCommand(request);
        var result = registerAccessControlUseCase.execute(command);
        URI location = uriBuilder.path("/api/v1/planeacion/access-control/{id}").buildAndExpand(result.id()).toUri();
        return ResponseEntity.created(location).body(map.toResponse(result));
    }

    @Override
    public ResponseEntity<Void> deleteAccessControlPolicy(UUID id) {
        deleteAccessControlUseCase.execute(id);
        return ResponseEntity.noContent().build();
    }

    @Override
    public List<AccessControlPolicyRepresentation> filterAccessControlPolicies(AccessControlPolicyRepresentation request) {
        var query = map.toQuery(request);
        return filterAccessControlPoliciesUseCase.execute(query)
                .stream()
                .map(map::toResponse)
                .toList();
    }

}
