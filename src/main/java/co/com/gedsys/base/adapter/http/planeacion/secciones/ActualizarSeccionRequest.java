package co.com.gedsys.base.adapter.http.planeacion.secciones;

import jakarta.validation.constraints.AssertTrue;

public record ActualizarSeccionRequest(
    String nombre,
    String responsable
) {
    @AssertTrue(message = "Al menos un campo debe ser proporcionado para actualizar")
    public boolean isValid() {
        return (nombre != null && !nombre.trim().isEmpty()) || 
               (responsable != null && !responsable.trim().isEmpty());
    }
}