package co.com.gedsys.base.adapter.http.planeacion.plantillas;

import co.com.gedsys.base.application.usecase.planeacion.plantillas.CambiarEstadoCommand;
import co.com.gedsys.base.application.usecase.planeacion.plantillas.CreatePlantillaCommand;
import co.com.gedsys.base.application.usecase.planeacion.plantillas.*;
import co.com.gedsys.base.domain.plantillas.TipoPlantilla;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.List;
import java.util.UUID;

import static co.com.gedsys.base.domain.plantillas.EstadoPlantilla.ACTIVA;
import static co.com.gedsys.base.domain.plantillas.EstadoPlantilla.INACTIVA;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/planeacion/plantillas")
public class PlantillaController implements PlantillasDocumentalesAPIV1 {

    private final ActualizarPlantillaUseCase actualizarUseCase;
    private final CambiarEstadoPlantillaUseCase cambiarEstadoUseCase;
    private final RegistrarPlantillaUseCase registrarUseCase;
    private final BuscarPlantillaUseCase buscarUseCase;
    private final ListarPlantillasUseCase listarUseCase;
    private final ListarPlantillasPorTipoDocumentalUseCase listarPorTipoDocumentalUseCase;

    private final RequestHttpMapper mapper;

    private final PlantillasHttpMapper plantillasMapper;

    @Override
    public ResponseEntity<Void> create(PlantillaCreateRequest request, UriComponentsBuilder uriBuilder) {
        var command = new CreatePlantillaCommand(
            request.titulo(), 
            TipoPlantilla.valueOf(request.tipo()),
            request.producidoPorGedsys()
        );
        var id = registrarUseCase.execute(command);
        URI location = uriBuilder.path("/api/v1/planeacion/plantillas/{id}").buildAndExpand(id).toUri();
        return ResponseEntity.created(location).build();
    }

    @Override
    public List<PlantillaItemDS> getAll(TipoPlantilla tipo) {
        var plantillas = listarUseCase.execute(new ListarPlantillasQuery(tipo));
        return plantillasMapper.toItems(plantillas);
    }

    @Override
    public ResponseEntity<PlantillaDetalladaDS> get(UUID id) {
        var plantillaEncontrada = buscarUseCase.execute(id);
        return ResponseEntity.ok(plantillasMapper.toDS(plantillaEncontrada));
    }

    @Override
    public ResponseEntity<Void> update(UUID id, ActualizarPlantillaRequest request) {
        final var command = mapper.toCommand(id, request);
        actualizarUseCase.execute(command);
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> activate(UUID plantillaId) {
        cambiarEstadoUseCase.execute(new CambiarEstadoCommand(plantillaId, ACTIVA));
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> deactivate(UUID plantillaId) {
        cambiarEstadoUseCase.execute(new CambiarEstadoCommand(plantillaId, INACTIVA));
        return ResponseEntity.noContent().build();
    }

    @Override
    public List<PlantillaItemDS> porTiposDocumentalesPermitidos(List<String> tiposDocumentales) {
        var plantillas = listarPorTipoDocumentalUseCase.execute(tiposDocumentales);
        return plantillasMapper.toItems(plantillas);
    }
}