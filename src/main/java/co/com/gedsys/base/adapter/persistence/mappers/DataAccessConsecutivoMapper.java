package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.infrastructure.data_access.ConsecutivoEntity;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", uses = { DataAccessTipoDocumentalMapper.class,
        DataAccessClasificacionDocumentalMapper.class }, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface DataAccessConsecutivoMapper {

    Consecutivo toDomain(ConsecutivoEntity consecutivoEntity);

    @Mapping(target = "tipoDocumentalId", source = "tipoDocumentalId")
    @Mapping(target = "clasificacionDocumentalId", source = "clasificacionDocumentalId")
    ConsecutivoEntity toEntity(Consecutivo domain);
}
