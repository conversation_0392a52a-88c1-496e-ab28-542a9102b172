package co.com.gedsys.base.adapter.http.planeacion.control_acceso;

import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.UUID;

public interface AccessControlAPI {

    @PostMapping(path = "", produces = "application/json", consumes = "application/json")
    ResponseEntity<AccessControlPolicyRepresentation> createAccessControlPolicy(@RequestBody @Valid AccessControlPolicyRepresentation request,
                                                                                UriComponentsBuilder uriBuilder);

    @DeleteMapping(path = "/{id}", produces = "application/json")
    ResponseEntity<Void> deleteAccessControlPolicy(@PathVariable UUID id);

    @GetMapping(path = "", produces = "application/json")
    List<AccessControlPolicyRepresentation> filterAccessControlPolicies(@ModelAttribute AccessControlPolicyRepresentation query);
}
