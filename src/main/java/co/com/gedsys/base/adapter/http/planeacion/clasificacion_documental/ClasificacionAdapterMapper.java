package co.com.gedsys.base.adapter.http.planeacion.clasificacion_documental;

import co.com.gedsys.base.application.dto.ClasificacionDS;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ClasificacionAdapterMapper {
    ClasificacionDocumentalAPI.ClasificacionDocumentalDetail toDS(ClasificacionDS useCaseOutput);

    @Mapping(target = "nombreSerie", source = "serie.nombre")
    @Mapping(target = "nombreSeccion", source = "seccion.nombre")
    ClasificacionDocumentalAPI.ClasificacionDocumentalItem toItem(ClasificacionDS useCaseOutput);

    List<ClasificacionDocumentalAPI.ClasificacionDocumentalItem> toItems(List<ClasificacionDS> useCaseOutputs);
}
