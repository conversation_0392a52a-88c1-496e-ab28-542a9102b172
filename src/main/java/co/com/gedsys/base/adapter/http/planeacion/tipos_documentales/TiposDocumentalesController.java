package co.com.gedsys.base.adapter.http.planeacion.tipos_documentales;

import co.com.gedsys.base.application.usecase.planeacion.tipologia.*;
import co.com.gedsys.base.domain.tipologia.EstadoTipoDocumental;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/planeacion/tipos-documentales")
public class TiposDocumentalesController implements TiposDocumentalesApiV1 {
    private final RegistrarTipoDocumentalUseCase createUseCase;
    private final ListarUnidadesPorTipoDocumentalUseCase listarUnidadesPorTipoDocumentalUseCase;
    private final BuscarTiposDocumentalesUseCase buscarTipoDocumentalUseCase;
    private final IdentificarTipoDocumentalUseCase identificarTipoDocumentalUseCase;

    private final TiposDocumentalesHttpMapper mapper;


    @Override
    public List<TipoDocumentalItem> findByStatus(EstadoTipoDocumental estado) {
        var query = new BuscarTiposDocumentalesQuery(estado);
        return buscarTipoDocumentalUseCase.execute(query)
                .stream().map(mapper::toItem).toList();
    }

    @Override
    public TipoDocumentalDetail getDetails(UUID id) {
        return mapper.toDetail(identificarTipoDocumentalUseCase.execute(id));
    }

    @Override
    public void create(CreateTipoDocumentalRequest request) {
        createUseCase.execute(new RegistrarTipoDocumentalCommand(request.nombre()));
    }

    @Override
    public List<UnidadesDisponiblesPorTipoDS> getRelatedUnidadesDocumentales(UUID tipoDocumentalId) {
        var unidadesDocumentales = listarUnidadesPorTipoDocumentalUseCase
                .execute(new ListarUnidadesPorTipoDocumentalUseCase.Query(tipoDocumentalId));
        return unidadesDocumentales.stream().map(mapper::toDS).toList();
    }

}
