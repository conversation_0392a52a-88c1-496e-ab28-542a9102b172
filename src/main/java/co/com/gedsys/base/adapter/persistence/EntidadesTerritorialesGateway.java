package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.infrastructure.data_access.EntidadTerritorialEntity;
import co.com.gedsys.base.adapter.persistence.mappers.EntidadesTerritorialesGatewayMapper;
import co.com.gedsys.base.infrastructure.data_access.repository.EntidadTerritorialJpaRepository;
import co.com.gedsys.base.domain.entidad_territorial.EntidadTerritorial;
import co.com.gedsys.base.domain.entidad_territorial.EntidadesTerritorialesRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Repository
public class EntidadesTerritorialesGateway implements EntidadesTerritorialesRepository {
    private final EntidadesTerritorialesGatewayMapper mapper;
    private final EntidadTerritorialJpaRepository jpaRepository;

    @Override
    public EntidadTerritorial save(EntidadTerritorial domainEntity) {
        var saved = jpaRepository.save(mapper.toEntity(domainEntity));
        return mapper.toDomain(saved);
    }

    @Override
    public Optional<EntidadTerritorial> findById(Integer id) {
        return jpaRepository.findById(id)
                .map(mapper::toDomain);
    }

    @Override
    public List<EntidadTerritorial> findAll() {
        return jpaRepository.findAll()
                .stream()
                .map(mapper::toDomain)
                .toList();
    }

    @Override
    public boolean checkStock(String name) {
        return false;
    }

    @Override
    public boolean checkStock(EntidadTerritorial domainEntity) {
        var entity = mapper.toEntity(domainEntity);
        var matcher = ExampleMatcher.matchingAny()
                .withIgnorePaths("tipo", "id")
                .withMatcher("codigo", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("nombre", ExampleMatcher.GenericPropertyMatchers.ignoreCase());

        Example<EntidadTerritorialEntity> example = Example.of(entity, matcher);

        return jpaRepository.exists(example);
    }

    @Override
    public void delete(Integer id) {
        jpaRepository.deleteById(id);
    }

    @Override
    public List<EntidadTerritorial> checkStock(List<EntidadTerritorial> entitiesForChecking) {
        final var matcher = ExampleMatcher.matching()
                .withIgnorePaths("tipo", "id")
                .withMatcher("codigo", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("nombre", ExampleMatcher.GenericPropertyMatchers.ignoreCase());
        return entitiesForChecking.stream()
                .filter(e -> jpaRepository.exists(Example.of(mapper.toEntity(e), matcher)))
                .toList();
    }

    @Override
    public List<EntidadTerritorial> saveAll(List<EntidadTerritorial> toCreateList) {
        var entities = toCreateList.stream().map(mapper::toEntity).toList();
        return jpaRepository.saveAll(entities)
                .stream()
                .map(mapper::toDomain)
                .toList();
    }

    @Override
    public boolean checkStock(String codigo, String nombre) {
        var entity = EntidadTerritorialEntity.builder().codigo(codigo).nombre(nombre).build();
        var matcher = ExampleMatcher.matchingAny()
                .withMatcher("codigo", ExampleMatcher.GenericPropertyMatchers.exact())
                .withMatcher("nombre", ExampleMatcher.GenericPropertyMatchers.ignoreCase());

        Example<EntidadTerritorialEntity> example = Example.of(entity, matcher);

        return jpaRepository.exists(example);
    }
}
