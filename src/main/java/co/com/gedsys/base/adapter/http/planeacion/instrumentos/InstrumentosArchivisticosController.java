package co.com.gedsys.base.adapter.http.planeacion.instrumentos;

import co.com.gedsys.base.adapter.http.planeacion.secciones.OrganigramaTreeResponse;
import co.com.gedsys.base.adapter.http.planeacion.secciones.SeccionControllerMapper;
import co.com.gedsys.base.application.dto.CCDDto;
import co.com.gedsys.base.application.usecase.planeacion.clasificacion_documental.GenerarCCDUseCase;
import co.com.gedsys.base.application.usecase.planeacion.instrumentos.BuscarOrganigramaQuery;
import co.com.gedsys.base.application.usecase.planeacion.seccion.BuscarOrganigramaUseCase;
import co.com.gedsys.base.domain.organizacion.EstadoSeccion;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(path = "/api/v1/planeacion/instrumentos")
public class InstrumentosArchivisticosController implements InstrumentosArchivisticosAPI {
    private final GenerarCCDUseCase ccdUseCase;
    private final BuscarOrganigramaUseCase buscarOrganigramaUseCase;
    private final SeccionControllerMapper mapper;

    public InstrumentosArchivisticosController(
            GenerarCCDUseCase ccdUseCase,
            BuscarOrganigramaUseCase buscarOrganigramaUseCase,
            SeccionControllerMapper mapper) {
        this.ccdUseCase = ccdUseCase;
        this.buscarOrganigramaUseCase = buscarOrganigramaUseCase;
        this.mapper = mapper;
    }

    @Override
    public List<CCDDto> getCCD() {
        return ccdUseCase.execute(null);
    }

    @Override
    public ResponseEntity<OrganigramaTreeResponse> getOrganizationChart() {
        var query = new BuscarOrganigramaQuery(EstadoSeccion.ACTIVA);
        var response = mapper.toResponse(buscarOrganigramaUseCase.execute(query));

        return ResponseEntity.ok(response);
    }
}
