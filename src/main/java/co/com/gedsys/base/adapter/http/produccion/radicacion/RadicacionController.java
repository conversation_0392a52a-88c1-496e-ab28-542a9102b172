package co.com.gedsys.base.adapter.http.produccion.radicacion;

import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.usecase.produccion.radicacion.BuscarRadicadoPorIdUseCase;
import co.com.gedsys.base.application.usecase.produccion.radicacion.RadicarDocumentoProducidoUseCase;
import co.com.gedsys.base.application.usecase.produccion.radicacion.command.RadicarDocumentoProducidoCommand;
import lombok.RequiredArgsConstructor;

import java.util.UUID;

import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class RadicacionController implements RadicacionAPI {

    private final BuscarRadicadoPorIdUseCase buscarRadicadoPorIdUseCase;

    @Override
    public RadicadoDTO buscarRadicadoPorId(UUID id) {
        return buscarRadicadoPorIdUseCase.execute(id);
    }
}