package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.domain.plantillas.EstadoPlantilla;
import co.com.gedsys.base.domain.plantillas.Plantilla;
import co.com.gedsys.base.infrastructure.data_access.PlantillaEntity;
import org.mapstruct.*;

import java.util.List;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        uses = {DataAccessTipoDocumentalMapper.class,
                DataAccessConsecutivoMapper.class,
                DefinicionMetadatosDataAccessMapper.class})
@MapperConfig(nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PlantillaEntityMapper {

    @Mapping(target = "estado", expression = "java(map(plantilla.estado()))")
    @Mapping(target = "titulo", expression = "java(plantilla.titulo())")
    // Solo mapear como true si el tipo es PRODUCCION y el valor es true
    @Mapping(target = "producidoPorGedsys", expression = "java(plantilla.isProducidoPorGedsys())")
    PlantillaEntity toEntity(Plantilla plantilla);

    @Mapping(target = "producidoPorGedsys", source = "producidoPorGedsys")
    Plantilla toDomain(PlantillaEntity plantillaEntity);

    List<Plantilla> toDomainList(List<PlantillaEntity> entities);

    default EstadoPlantilla map(String estado) {
        return EstadoPlantilla.valueOf(estado);
    }

}
