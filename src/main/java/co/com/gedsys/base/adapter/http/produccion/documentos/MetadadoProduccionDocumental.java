package co.com.gedsys.base.adapter.http.produccion.documentos;

import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
public record MetadadoProduccionDocumental(
        @NotBlank(message = "El nombre del metadato no puede estar vacío")
        String nombre,
        @NotBlank(message = "El valor del metadato no puede estar vacío")
        String valor,
        @NotNull(message = "El tipo de metadato no puede estar vacío")
        TipoMetadatoEnum tipo
) {
}