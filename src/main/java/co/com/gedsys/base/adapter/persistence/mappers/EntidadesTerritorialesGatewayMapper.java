package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.infrastructure.data_access.EntidadTerritorialEntity;
import co.com.gedsys.base.domain.entidad_territorial.EntidadTerritorial;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface EntidadesTerritorialesGatewayMapper {
    EntidadTerritorialEntity toEntity(EntidadTerritorial entidadTerritorial);

    EntidadTerritorial toDomain(EntidadTerritorialEntity entidadTerritorialEntity);
}
