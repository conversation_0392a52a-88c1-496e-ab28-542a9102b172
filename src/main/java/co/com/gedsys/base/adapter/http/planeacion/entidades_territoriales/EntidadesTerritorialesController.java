package co.com.gedsys.base.adapter.http.planeacion.entidades_territoriales;

import java.io.IOException;
import java.util.List;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.ActualizarEntidadTerritorialCommand;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.ActualizarEntidadTerritorialUseCase;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.BuscarUnaEntidadTerritorialUseCase;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.CrearEntidadTerritorialCommand;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.CrearEntidadTerritorialUseCase;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.CrearMasivamenteEntidadesUseCase;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.EliminarEntidadTerritorialUseCase;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.ListarTodasLasEntidadesTerritorialesUseCase;
import co.com.gedsys.base.util.CsvUtil;
import lombok.RequiredArgsConstructor;

@RequestMapping("/api/v1/planeacion/entidades-territoriales")
@RequiredArgsConstructor
@RestController
public class EntidadesTerritorialesController implements EntidadesTerritorialesApiV1 {

    private final CrearEntidadTerritorialUseCase createUseCase;
    private final ListarTodasLasEntidadesTerritorialesUseCase findAllUseCase;
    private final BuscarUnaEntidadTerritorialUseCase findByIdUseCase;
    private final ActualizarEntidadTerritorialUseCase updateUseCase;
    private final EliminarEntidadTerritorialUseCase deleteUseCase;
    private final CrearMasivamenteEntidadesUseCase batchCreateUseCase;
    private final EntidadesTerritorialesHttpMapper mapper;

    @Override
    public ResponseEntity<EntidadTerritorialResponse> create(EntidadesTerritorialesRequest request) {
        var command = new CrearEntidadTerritorialCommand(request.codigo(), request.nombre());
        EntidadTerritorialResponse response = mapper.toResponse(createUseCase.execute(command));
        return ResponseEntity.created(null).body(response);
    }

    @Override
    public ResponseEntity<List<EntidadTerritorialResponse>> findAll() {
        return ResponseEntity.ok(mapper.toResponse(findAllUseCase.execute(null)));
    }

    @Override
    public ResponseEntity<EntidadTerritorialResponse> findById(Integer id) {
        var entidadTerritorialEncontrada = findByIdUseCase.execute(id);
        return ResponseEntity.ok(mapper.toResponse(entidadTerritorialEncontrada));
    }

    @Override
    public ResponseEntity<EntidadTerritorialResponse> update(Integer id, EntidadesTerritorialesRequest request) {
        var command = new ActualizarEntidadTerritorialCommand(id, request.codigo(), request.nombre());
        var updatedEntity = updateUseCase.execute(command);
        return ResponseEntity.ok(mapper.toResponse(updatedEntity));
    }

    @Override
    public ResponseEntity<Void> delete(Integer id) {
        deleteUseCase.execute(id);
        return ResponseEntity.noContent().header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE).build();
    }

    @Override
    public ResponseEntity<byte[]> template(boolean semicolon) {
        String csvContent = "codigo,nombre\r\n01,Amazonas\r\n05,Antioquia\r\n05.01,Medellin";
        if (semicolon) {
            csvContent = "codigo;nombre\r\n01;Amazonas\r\n05;Antioquia\r\n05.01;Medellin";
        }

        byte[] csvBytes = csvContent.getBytes();

        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=entidades-territoriales.csv");
        headers.add(HttpHeaders.CONTENT_TYPE, "text/csv; charset=UTF-8");
        headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(csvBytes.length));

        return ResponseEntity.ok()
                .headers(headers)
                .body(csvBytes);
    }

    @Override
    public ResponseEntity<Void> uploadCsv(MultipartFile file) {
        List<CrearEntidadTerritorialCommand> entidades;
        if (file.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }

        try (var csv = file.getInputStream()) {
            var csvUtil = new CsvUtil(csv);
            var rows = csvUtil.read();
            entidades = rows.stream().map(mapper::toCommand).toList();
        } catch (IOException e) {
            throw new RuntimeException("No se pudo leer el archivo CSV");
        }

        batchCreateUseCase.execute(entidades);

        return ResponseEntity.created(null).build();
    }
}
