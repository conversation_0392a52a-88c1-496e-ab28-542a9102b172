package co.com.gedsys.base.adapter.http.planeacion.instrumentos;

import co.com.gedsys.base.adapter.http.planeacion.secciones.OrganigramaTreeResponse;
import co.com.gedsys.base.application.dto.CCDDto;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

public interface InstrumentosArchivisticosAPI {
    @GetMapping(path = "/ccd")
    List<CCDDto> getCCD();

    @GetMapping(path = "/organigrama", produces = "application/json")
    ResponseEntity<OrganigramaTreeResponse> getOrganizationChart();
}
