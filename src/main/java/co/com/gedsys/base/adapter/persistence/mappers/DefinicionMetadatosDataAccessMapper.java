package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.infrastructure.data_access.DefinicionMetadatoEntity;
import co.com.gedsys.base.domain.metadato.DefinicionMetadato;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.Collection;
import java.util.Set;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface DefinicionMetadatosDataAccessMapper {

    DefinicionMetadato toDomain(DefinicionMetadatoEntity entity);

    DefinicionMetadatoEntity toEntity(DefinicionMetadato definicion);

    Set<DefinicionMetadato> toMetadataSchema(Collection<DefinicionMetadatoEntity> entities);

}
