package co.com.gedsys.base.adapter.http.produccion.documentos;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;

public record SolicitudAgregarAnexo(
        @NotBlank(message = "El nombre del anexo no puede estar vacío")
        String nombre,
        String descripcion,
        @NotBlank(message = "El archivo del anexo no puede estar vacío")
        String fileId,
        String hash,
        @Min(value = 1, message = "El tamaño del anexo no puede ser negativo")
        Long bytes,
        @NotBlank(message = "La extensión del anexo no puede estar vacío")
        String extension
) {
}
