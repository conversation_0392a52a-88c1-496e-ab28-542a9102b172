package co.com.gedsys.base.adapter.http.planeacion.plantillas;

import co.com.gedsys.base.application.usecase.planeacion.plantillas.ActualizarPlantillaCommand;

import java.util.UUID;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface RequestHttpMapper {

    @Mapping(target = "tipoDocumentalId", source = "request.tipoDocumental")
    ActualizarPlantillaCommand toCommand(UUID plantillaId, ActualizarPlantillaRequest request);
}
