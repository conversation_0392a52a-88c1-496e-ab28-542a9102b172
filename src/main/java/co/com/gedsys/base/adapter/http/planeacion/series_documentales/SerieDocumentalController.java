package co.com.gedsys.base.adapter.http.planeacion.series_documentales;

import co.com.gedsys.base.application.dto.SerieDocumentalDto;
import co.com.gedsys.base.application.usecase.planeacion.series_documentales.*;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

import static co.com.gedsys.base.domain.serie_documental.EstadoSerie.ACTIVA;

@RequiredArgsConstructor
@RestController
@RequestMapping(path = "/api/v1/planeacion/series-documentales")
public class SerieDocumentalController implements SerieDocumentalApi {

    private final RegistrarSerieDocumentalUseCase registrarSerieUseCase;
    private final BuscarSeriesUseCase buscarSeriesUseCase;
    private final ActivarSerieDocumentalUseCase activarSerieDocumentalUseCase;
    private final InactivarSerieDocumentalUseCase inactivarSerieDocumentalUseCase;
    private final SerieDocumentalPlaneacionMapper mapper;

    @Override
    public ResponseEntity<Void> create(RegistrarSerieDocumentalRequest request) {
        var command = mapper.toCommand(request);
        registrarSerieUseCase.execute(command);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<List<SerieDocumentalItem>> getList() {
        var query = new BuscarSeriesQuery(ACTIVA);
        var result = buscarSeriesUseCase.execute(query).stream()
                .map(mapper::toItem)
                .toList();
        return ResponseEntity.ok(result);
    }

    @PatchMapping("/{id}/activar")
    public ResponseEntity<SerieDocumentalDto> activar(@PathVariable UUID id) {
        var command = new ActivarSerieDocumentalCommand(id);
        var result = activarSerieDocumentalUseCase.ejecutar(command);
        return ResponseEntity.ok(result);
    }

    @PatchMapping("/{id}/inactivar")
    public ResponseEntity<SerieDocumentalDto> inactivar(@PathVariable UUID id) {
        var command = new InactivarSerieDocumentalCommand(id);
        var result = inactivarSerieDocumentalUseCase.ejecutar(command);
        return ResponseEntity.ok(result);
    }
}
