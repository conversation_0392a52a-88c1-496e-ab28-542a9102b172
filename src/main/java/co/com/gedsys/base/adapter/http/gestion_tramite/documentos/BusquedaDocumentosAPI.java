package co.com.gedsys.base.adapter.http.gestion_tramite.documentos;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.UUID;

public interface BusquedaDocumentosAPI {
    @Operation(summary = "Buscar documento por identificador", description = "Busca un documento por su identificador único")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Documento encontrado",
                    content = @Content(schema = @Schema(implementation = RespuestaDocumentoEncontrado.class))),
            @ApiResponse(responseCode = "404", description = "Documento no encontrado")
    })
    @GetMapping(path = "/{id}", produces = "application/json")
    ResponseEntity<RespuestaDocumentoEncontrado> buscarPorIdentificador(
            @Parameter(description = "ID del documento") @PathVariable UUID id);

    @GetMapping(path = "", produces = "application/json")
    ResponseEntity<List<RespuestaDocumentoEncontrado>> buscarPorParametros(
            @RequestParam(required = false) UUID unidadDocumentalId
    );
}
