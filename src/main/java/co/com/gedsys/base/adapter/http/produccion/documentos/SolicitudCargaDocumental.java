package co.com.gedsys.base.adapter.http.produccion.documentos;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;
import java.util.UUID;

public record SolicitudCargaDocumental(
        @NotBlank(message = "El título del documento no puede estar vacío")
        String titulo,
        @NotBlank(message = "La referencia al archivo debe ser requerida")
        String fileId,
        @NotNull(message = "El tipo de documento no puede estar vacío")
        UUID tipoDocumentalId,
        @NotNull(message = "Debe especificar la unidad documental")
        UUID unidadDocumentalId,
        @NotBlank(message = "El autor no puede estar vacío")
        String autor,
        @Valid
        List<MetadadoProduccionDocumental> metadatos,
        @Valid
        List<AnexoProduccionDocumental> anexos) {

}
