package co.com.gedsys.base.adapter.http.planeacion.clasificacion_documental;

import co.com.gedsys.base.application.usecase.planeacion.clasificacion_documental.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/planeacion/clasificacion-documental")
public class ClasificacionDocumentalController implements ClasificacionDocumentalAPI {
    private final AgregarClasificacionDocumentalUseCase agregarClasificacionDocumentalUseCase;
    private final ClasificarTipologiaUseCase clasificarTipologiaUseCase;
    private final ListarClasificacionesUseCase listarClasificacionesUseCase;
    private final BuscarClasificacionDocumentalUseCase buscarClasificacionDocumentalUseCase;
    private final ClasificacionAdapterMapper map;

    @Override
    public void create(ClasificacionDocumentalRequest request) {
        var command = new AgregarClasificacionDocumentalCommand(request.unidadProductora(), request.serieDocumental());
        agregarClasificacionDocumentalUseCase.execute(command);
    }

    @Override
    public ClasificacionDocumentalAPI.ClasificacionDocumentalDetail relacionarTiposDocumentales(
            UUID id,
            RelacionarTiposDocumentalesRequest request) {
        var command = new ClasificarTipologiaUseCase.Command(id, request.tiposDocumentales());
        var clasificacionDS = clasificarTipologiaUseCase.execute(command);
        return map.toDS(clasificacionDS);
    }

    @Override
    public List<ClasificacionDocumentalItem> listarClasificacionesDocumentales() {
        var searchResult = listarClasificacionesUseCase.execute(null);
        return map.toItems(searchResult);
    }

    @Override
    public ClasificacionDocumentalDetail buscarPorId(UUID id) {
        return map.toDS(buscarClasificacionDocumentalUseCase.execute(id));
    }


}
