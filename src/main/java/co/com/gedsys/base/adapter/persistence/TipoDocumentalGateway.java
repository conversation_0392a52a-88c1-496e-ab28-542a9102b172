package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.infrastructure.data_access.TipoDocumentalEntity;
import co.com.gedsys.base.adapter.persistence.mappers.DataAccessTipoDocumentalMapper;
import co.com.gedsys.base.infrastructure.data_access.repository.TipoDocumentalJpaRepository;
import co.com.gedsys.base.domain.tipologia.EstadoTipoDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static co.com.gedsys.base.domain.tipologia.EstadoTipoDocumental.ELIMINADO;


@RequiredArgsConstructor
@Repository
public class TipoDocumentalGateway implements TipoDocumentalRepository {
    private final TipoDocumentalJpaRepository jpaRepository;
    private final DataAccessTipoDocumentalMapper mapper;

    @Override
    public TipoDocumental save(TipoDocumental tipoDocumental) {
        return mapper.toDomain(jpaRepository.save(mapper.toEntity(tipoDocumental)));
    }

    @Override
    public Optional<TipoDocumental> findById(UUID uuid) {
        return Optional.ofNullable(uuid)
                .flatMap(jpaRepository::findById)
                .map(mapper::toDomain);
    }

    @Override
    public List<TipoDocumental> findAll() {
        return jpaRepository.findAll().stream()
                .map(mapper::toDomain).toList();
    }

    @Override
    public boolean checkStock(String name) {
        return jpaRepository.existsByNombreIgnoreCaseAndEstadoIsNot(name, ELIMINADO);
    }

    @Override
    public boolean checkStock(TipoDocumental entity) {
        return false;
    }

    @Override
    public void delete(UUID uuid) {

    }

    @Override
    public List<TipoDocumental> findByIdIn(List<UUID> ids) {
        return jpaRepository.findByIdIn(ids)
                .stream()
                .map(mapper::toDomain)
                .toList();
    }

    @Override
    public List<TipoDocumental> filtrarPorEstado(EstadoTipoDocumental estado) {
        var exampleMatcher = ExampleMatcher.matchingAll();
        var example = Example.of(TipoDocumentalEntity.builder().estado(estado).build(), exampleMatcher);
        var result =jpaRepository.findAll(example);
        return result.stream().map(mapper::toDomain).toList();
    }
}
