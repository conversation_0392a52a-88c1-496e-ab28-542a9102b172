package co.com.gedsys.base.adapter.http.gestion_tramite.documentos;

import co.com.gedsys.base.application.dto.DocumentoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface BusquedaDocumentosControllerMapper {

    @Mapping(target = "unidadDocumentalNombre", source = "unidadDocumental.nombre")
    @Mapping(target = "unidadDocumentalId", source = "unidadDocumental.id")
    @Mapping(target = "tipoDocumentalNombre", source = "tipoDocumental.nombre")
    @Mapping(target = "tipoDocumentalId", source = "tipoDocumental.id")
    @Mapping(target = "codigoClasificatorio", source = "unidadDocumental.codigoClasificacion")
    @Mapping(target = "estado", source = "estado", qualifiedByName = "mapEstadoDocumento")
    RespuestaDocumentoEncontrado toResponse(DocumentoDTO dto);

    @Mapping(target = "owner", source = "firmante")
    RespuestaDocumentoEncontrado.FirmaDS toFirma(DocumentoDTO.FirmaDS ds);

    @Named("mapEstadoDocumento")
    default RespuestaDocumentoEncontrado.EstadoDocumento map(RespuestaDocumentoEncontrado.EstadoDocumento estado) {
        return RespuestaDocumentoEncontrado.EstadoDocumento.valueOf(estado.name());
    }

}
