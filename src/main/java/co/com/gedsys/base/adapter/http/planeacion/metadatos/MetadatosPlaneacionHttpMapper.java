package co.com.gedsys.base.adapter.http.planeacion.metadatos;

import java.util.Set;
import java.util.UUID;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import co.com.gedsys.base.application.dto.DefinicionMetadatoDTO;
import co.com.gedsys.base.application.usecase.planeacion.metadatos.ActualizarDefinicionCommand;
import co.com.gedsys.base.application.usecase.planeacion.metadatos.AgregarDefinicionMetadatoCommand;

@Mapper(componentModel = "spring")
public interface MetadatosPlaneacionHttpMapper {
    AgregarDefinicionMetadatoCommand toCommand(MetadataDefinitionRequest request);

    ActualizarDefinicionCommand toCommand(UUID id, UpdateDefinicionRequest request);

    DefinicionMetadatoResponse toResponse(DefinicionMetadatoDTO dto);
    Set<DefinicionMetadatoResponse> toResponse(Set<DefinicionMetadatoDTO> set);
}