package co.com.gedsys.base.adapter.http.planeacion.series_documentales;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface SerieDocumentalApi {

    @PostMapping(consumes = "application/json", produces = "application/json")
    ResponseEntity<Void> create(@RequestBody RegistrarSerieDocumentalRequest request);

    @GetMapping(produces = "application/json")
    ResponseEntity<List<SerieDocumentalItem>> getList();
}
