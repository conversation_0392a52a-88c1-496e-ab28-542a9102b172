package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.infrastructure.data_access.UnidadDocumentalEntity;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
        uses = {DataAccessClasificacionDocumentalMapper.class},
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface DataAccessUnidadDocumentalMapper {

    UnidadDocumental toDomain(UnidadDocumentalEntity e);

    @Mapping(target = "nombre", expression = "java(ud.nombre())")
    UnidadDocumentalEntity toEntity(UnidadDocumental ud);
}
