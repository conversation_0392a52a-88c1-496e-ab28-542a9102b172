package co.com.gedsys.base.adapter.http.planeacion.consecutivos;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;

import co.com.gedsys.base.application.usecase.planeacion.consecutivos.CrearConsecutivoCommand;
import co.com.gedsys.base.application.dto.ConsecutivoDTO;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConsecutivoControllerMapper {

    CrearConsecutivoCommand toCommand(CreateConsecutivoRequest request);


    @Mapping(target = "tipoDocumentalNombre", source = "tipoDocumental.nombre")
    @Mapping(target = "tipoDocumentalId", source = "tipoDocumental.id")
    @Mapping(target = "clasificacionDocumentalNombreSerie", source = "clasificacionDocumental.nombreSerie")
    @Mapping(target = "clasificacionDocumentalNombreSeccion", source = "clasificacionDocumental.nombreSeccion")
    @Mapping(target = "clasificacionDocumentalId", source = "clasificacionDocumental.id")
    @Mapping(target = "clasificacionDocumentalCodigo", source = "clasificacionDocumental.codigo")
    @Mapping(target = "tipo", source = "tipoConsecutivo")
    ConsecutivoRepresentation toDS(ConsecutivoDTO dto);
}
