package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.infrastructure.data_access.ClasificacionDocumentalEntity;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring",
        uses = {SeccionPersistenceMapper.class,
                SerieDocumentalPersistenceMapper.class,
                DataAccessTipoDocumentalMapper.class},
        unmappedTargetPolicy = ReportingPolicy.ERROR
)
public interface DataAccessClasificacionDocumentalMapper {

    @Mapping(target = "subSerie", source = "subserie")
    @Mapping(target = "seccion", source = "seccion")
    ClasificacionDocumental toDomain(ClasificacionDocumentalEntity clasificacionDocumentalEntity);

    List<ClasificacionDocumental> toDomainList(List<ClasificacionDocumentalEntity> entities);

    @Mapping(target = "subserie", source = "subSerie")
    ClasificacionDocumentalEntity toEntity(ClasificacionDocumental domainEntity);
}
