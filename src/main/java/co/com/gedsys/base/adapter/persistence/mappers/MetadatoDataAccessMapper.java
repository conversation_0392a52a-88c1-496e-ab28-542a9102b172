package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.infrastructure.data_access.MetadatoEntity;
import co.com.gedsys.base.domain.metadato.Metadato;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = org.mapstruct.ReportingPolicy.ERROR)
public interface MetadatoDataAccessMapper {
    @Mapping(target = "documento", ignore = true)
    @Mapping(target = "valor", expression = "java(metadato.valor())")
    @Mapping(target = "tipo", expression = "java(metadato.tipo())")
    @Mapping(target = "patron", expression = "java(metadato.patron())")
    @Mapping(target = "nombre", expression = "java(metadato.nombre())")
    @Mapping(target = "id", expression = "java(metadato.id())")
    @Mapping(target = "formato", expression = "java(metadato.formato())")
    MetadatoEntity toEntity(Metadato metadato);

    List<MetadatoEntity> toEntity(List<Metadato> metadatos);

    Metadato toDomain(MetadatoEntity entity);

    List<Metadato> toDomain(List<MetadatoEntity> entities);
}
