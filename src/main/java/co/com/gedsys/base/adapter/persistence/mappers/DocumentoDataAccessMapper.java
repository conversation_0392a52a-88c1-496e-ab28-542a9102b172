package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.infrastructure.data_access.*;
import co.com.gedsys.base.domain.documento.*;
import co.com.gedsys.base.domain.radicado.PropiedadesRadicado;
import co.com.gedsys.base.domain.radicado.Radicado;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", uses = { DataAccessTipoDocumentalMapper.class,
                MetadatoDataAccessMapper.class,
                DataAccessUnidadDocumentalMapper.class,
                ExternalUsersGatewayMapper.class }, unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface DocumentoDataAccessMapper {

        @Mapping(target = "unidadDocumentalId", ignore = true)
        @Mapping(target = "tipoDocumentalId", ignore = true)
        @Mapping(target = "updatedAt", ignore = true)
        @Mapping(target = "createdAt", ignore = true)
        @Mapping(target = "radicados", ignore = true)
        DocumentoEntity toEntity(Documento documento);

        Documento toDomain(DocumentoEntity entity);

        @Mapping(target = "propiedadesRadicado", source = "entity")
        @Mapping(target = "expedir", ignore = true)
        @Mapping(target = "documento", ignore = true)
        @Mapping(target = "consecutivo", ignore = true)
        @Mapping(target = "destino.hijos", ignore = true)
        @Mapping(target = "destino.usuarios", ignore = true)
        @Mapping(target = "destino.padre", ignore = true)
        Radicado toRadicado(RadicadoEntity entity);

        @Mapping(target = "documento", ignore = true)
        FirmaUsuarioEntity toEntity(FirmaUsuario firmaUsuario);

        @Mapping(target = "documento", ignore = true)
        AprobacionEntity toEntity(Aprobacion aprobacion);

        @Mapping(target = "documento", ignore = true)
        AnexoEntity toEntity(Anexo anexo);

        @Mapping(target = "updatedAt", ignore = true)
        @Mapping(target = "unidadDocumental", ignore = true)
        @Mapping(target = "titulo", ignore = true)
        @Mapping(target = "tipoDocumentalId", ignore = true)
        @Mapping(target = "tipoDocumental", ignore = true)
        @Mapping(target = "radicados", ignore = true)
        @Mapping(target = "metadatos", ignore = true)
        @Mapping(target = "id", ignore = true)
        @Mapping(target = "firmas", ignore = true)
        @Mapping(target = "fileId", ignore = true)
        @Mapping(target = "estado", ignore = true)
        @Mapping(target = "createdAt", ignore = true)
        @Mapping(target = "autor", ignore = true)
        @Mapping(target = "aprobaciones", ignore = true)
        @Mapping(target = "anexos", ignore = true)
        DocumentoEntity toEntity(ParametrosBusquedaDocumento parametrosBusquedaDocumento);

        @Mapping(target = "page", source = "propPage")
        @Mapping(target = "x", source = "propX")
        @Mapping(target = "y", source = "propY")
        @Mapping(target = "height", source = "propHeight")
        @Mapping(target = "width", source = "propWidth")
        @Mapping(target = "rotationDegrees", source = "propRotationDegrees")
        PropiedadesRadicado toDomain(RadicadoEntity entity);

}
