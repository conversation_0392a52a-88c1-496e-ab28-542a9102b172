package co.com.gedsys.base.adapter.http.planeacion.usuarios_externos;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

public interface ExternalUserAPIV1 {

    @ResponseStatus(code = HttpStatus.CREATED)
    @PostMapping(path = "", consumes = "application/json", produces = "application/json")
    ExternalUserInfo create(@RequestBody ExternalUserCreateRequest request);

    @ResponseStatus(code = HttpStatus.CREATED)
    @PostMapping(path = "/{id}/properties", consumes = "application/json", produces = "application/json")
    ExternalUserInfo addNewProperties(@RequestBody List<ExternalUserPropertyRegistration> request, @PathVariable UUID id);

    @PatchMapping(path = "/{id}", consumes = "application/json", produces = "application/json")
    ExternalUserInfo update(@PathVariable("id") UUID id, @RequestBody ExternalUserUpdateRequest request);

    @GetMapping(path = "/{id}", produces = "application/json")
    ExternalUserInfo findById(@PathVariable("id") UUID id);

    @GetMapping(path = "", produces = "application/json")
    List<ExternalUserItem> findAll();

    @ResponseStatus(code = HttpStatus.NO_CONTENT)
    @DeleteMapping(path = "/{id}")
    void delete(@PathVariable UUID id);
}
