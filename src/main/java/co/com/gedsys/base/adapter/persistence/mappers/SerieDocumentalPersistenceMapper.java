package co.com.gedsys.base.adapter.persistence.mappers;

import co.com.gedsys.base.infrastructure.data_access.SerieDocumentalEntity;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR
)
public interface SerieDocumentalPersistenceMapper {

    @Mapping(target = "codigo", expression = "java(clasificacion.codigo())")
    SerieDocumentalEntity toEntity(SerieDocumental clasificacion);

    SerieDocumental toDomain(SerieDocumentalEntity serieDocumentalEntity);
}
