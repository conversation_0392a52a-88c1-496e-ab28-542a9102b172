package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.adapter.persistence.mappers.DataAccessRadicadoMapper;
import co.com.gedsys.base.infrastructure.data_access.repository.RadicadoJpaRepository;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@RequiredArgsConstructor
@Repository
public class RadicadoGateway implements RadicadoRepository {

    private final RadicadoJpaRepository jpaRepository;

    private final DataAccessRadicadoMapper map;

    @Override
    public Radicado save(Radicado domainEntity) {
        var entity = map.toEntity(domainEntity);
        return map.toDomain(jpaRepository.save(entity));
    }

    @Override
    public Optional<Radicado> findById(UUID uuid) {
        return Optional.ofNullable(uuid)
                .flatMap(jpaRepository::findById)
                .map(map::toDomain);
    }

    @Override
    public List<Radicado> findAll() {
        return jpaRepository.findAll()
                .stream()
                .map(map::toDomain)
                .toList();
    }

    @Override
    public boolean checkStock(String name) {
        return false;
    }

    @Override
    public boolean checkStock(Radicado entity) {
        return false;
    }

    @Override
    public void delete(UUID uuid) {

    }

    @Override
    public List<Radicado> buscarRadicadosDeDocumento(UUID documentId) {
        return jpaRepository.searchByDocumentoId(documentId)
                .stream()
                .map(map::toDomain)
                .toList();
    }
}
