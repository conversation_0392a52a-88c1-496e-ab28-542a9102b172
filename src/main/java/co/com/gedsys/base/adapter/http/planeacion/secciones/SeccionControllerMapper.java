package co.com.gedsys.base.adapter.http.planeacion.secciones;

import co.com.gedsys.base.application.usecase.planeacion.seccion.AgregarSeccionCommand;
import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.application.dto.UsuarioSeccionDTO;
import co.com.gedsys.base.application.usecase.planeacion.seccion.ActualizarSeccionCommand;
import co.com.gedsys.base.domain.organizacion.EstadoSeccion;
import co.com.gedsys.base.domain.organizacion.Seccion;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.UUID;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface SeccionControllerMapper {

    @Mapping(target = "padreId", source = "padreId")
    AgregarSeccionCommand toCommand(UnidadProductoraRequest request);

    EstadoSeccion map(EstadoSeccionPlaneacion estado);

    EstadoSeccionPlaneacion map(EstadoSeccion estado);

    SeccionPlaneacionDS toResponse(SeccionDTO dto);

    OrganigramaTreeResponse toResponse(Seccion ds);

    @Mapping(target = "seccionId", source = "id")
    @Mapping(target = "nombre", source = "request.nombre")
    @Mapping(target = "responsable", source = "request.responsable")
    ActualizarSeccionCommand toCommand(UUID id, ActualizarSeccionRequest request);
    
    UsuarioSeccionDS toResponse(UsuarioSeccionDTO dto);
}
