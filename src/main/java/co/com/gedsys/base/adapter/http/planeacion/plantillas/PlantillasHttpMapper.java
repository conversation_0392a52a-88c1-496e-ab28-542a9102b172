package co.com.gedsys.base.adapter.http.planeacion.plantillas;

import co.com.gedsys.base.application.dto.PlantillaDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface PlantillasHttpMapper {

    @Mapping(target = "metadatos", source = "esquemaDeMetadatos")
    @Mapping(target = "tipoPlantilla", source = "tipoPlantilla")
    PlantillaDetalladaDS toDS(PlantillaDTO dto);

    @Mapping(target = "tipoDocumentalNombre", source = "tipoDocumental.nombre")
    PlantillaItemDS toItem(PlantillaDTO dto);

    List<PlantillaItemDS> toItems(List<PlantillaDTO> dtos);
}
