package co.com.gedsys.base.application.usecase.planeacion.clasificacion_documental;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.CCDDto;
import co.com.gedsys.base.application.mapper.ClasificacionDocumentalMapper;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class GenerarCCDUseCase implements UseCase<Void, List<CCDDto>> {

    private final ClasificacionDocumentalRepository repository;
    private final ClasificacionDocumentalMapper mapper;

    @Override
    public List<CCDDto> execute(Void input) {
        var clasificaciones = repository.findAll();
        return mapper.toDtoList(clasificaciones);
    }
}
