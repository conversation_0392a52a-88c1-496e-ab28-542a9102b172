package co.com.gedsys.base.application.usecase.planeacion.metadatos;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.DefinicionMetadatoDTO;
import co.com.gedsys.base.application.mapper.DefinicionMetadatosAppMapper;
import co.com.gedsys.base.domain.metadato.DefinicionMetadato;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ActualizarDefinicionUseCase implements UseCase<ActualizarDefinicionCommand, DefinicionMetadatoDTO> {

    private final DefinicionMetadatosRepository repository;
    private final DefinicionMetadatosAppMapper mapper;

    @Override
    public DefinicionMetadatoDTO execute(ActualizarDefinicionCommand input) {
        return repository
                .findById(input.id())
                .map(d -> updateName(d, input.nombre()))
                .map(d -> d.setDescripcion(input.descripcion()))
                .map(d -> d.setRules(input.rules()))
                .map(repository::save)
                .map(mapper::toDto)
                .orElseThrow(() -> new RuntimeException("Definicion no encontrada"));
    }

    private DefinicionMetadato updateName(DefinicionMetadato definicionMetadato, String name) {
        if (definicionMetadato.getNombre().equals(name)) return definicionMetadato;
        if (repository.checkStock(name))
            throw new EntityAlreadyExistsException("Ya existe una definición de un metadato con el nombre " + name);

        return definicionMetadato.setNombre(name);
    }
}