package co.com.gedsys.base.application.usecase.planeacion.seccion;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.application.mapper.SeccionUseCaseMapper;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class ActualizarSeccionUseCase implements UseCase<ActualizarSeccionCommand, SeccionDTO> {

    private final SeccionRepository repository;
    private final SeccionUseCaseMapper mapper;

    @Override
    public SeccionDTO execute(ActualizarSeccionCommand input) {
        var seccion = repository.findById(input.seccionId())
                .orElseThrow(() -> new EntityNotExistsException(
                        String.format("Sección con id '%s' no existe", input.seccionId())));

        if (input.nombre() != null && !input.nombre().trim().isEmpty()) {
            seccion.setNombre(input.nombre());
        }

        if (input.responsable() != null && !input.responsable().trim().isEmpty()) {
            seccion.setResponsable(input.responsable());
        }

        return mapper.toDto(repository.save(seccion));
    }
}
