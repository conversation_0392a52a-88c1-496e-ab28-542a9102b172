package co.com.gedsys.base.application.mapper;

import co.com.gedsys.base.application.dto.ClasificacionUnidadDocumentalDS;
import co.com.gedsys.base.application.dto.UnidadDocumentalDto;
import co.com.gedsys.base.application.usecase.planeacion.unidades_documentales.BuscarUnidadDocumentalQuery;
import co.com.gedsys.base.application.usecase.planeacion.unidades_documentales.ListarUnidadDocumentalQuery;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface UnidadDocumentalMapper {

    @Mapping(target = "nombre", expression = "java(unidadDocumental.nombre())")
    @Mapping(target = "descripcion", source = "descripcion")
    ListarUnidadDocumentalQuery map(UnidadDocumental unidadDocumental);
    List<ListarUnidadDocumentalQuery> map(List<UnidadDocumental> unidadesDocumentales);

    @Mapping(target = "nombre", expression = "java(unidadDocumental.nombre())")
    @Mapping(target = "descripcion", source = "descripcion")
    BuscarUnidadDocumentalQuery toQuery(UnidadDocumental unidadDocumental);

    @Mapping(target = "codigo", expression = "java(clasificacion.codigoCompleto())")
    ClasificacionUnidadDocumentalDS map(ClasificacionDocumental clasificacion);

    @Mapping(target = "nombre", expression = "java(unidad.nombre())")
    @Mapping(target = "descripcion", source = "descripcion")
    @Mapping(target = "codigoClasificatorio", expression = "java(unidad.codigoClasificatorio())")
    UnidadDocumentalDto toDto(UnidadDocumental unidad);
}
