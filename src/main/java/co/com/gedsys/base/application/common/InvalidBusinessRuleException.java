package co.com.gedsys.base.application.common;

/**
 * Excepción lanzada cuando se viola una regla de negocio específica del dominio.
 * Esta excepción debe ser capturada y transformada en una respuesta HTTP 400 Bad Request.
 */
public class InvalidBusinessRuleException extends RuntimeException {
    
    public InvalidBusinessRuleException(String message) {
        super(message);
    }
    
    public InvalidBusinessRuleException(String message, Throwable cause) {
        super(message, cause);
    }
} 