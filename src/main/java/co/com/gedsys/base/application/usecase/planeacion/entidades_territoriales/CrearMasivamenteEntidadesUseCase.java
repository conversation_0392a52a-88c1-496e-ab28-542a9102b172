package co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.mapper.EntidadesTerritorialesAppMapper;
import co.com.gedsys.base.domain.entidad_territorial.EntidadesTerritorialesRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class CrearMasivamenteEntidadesUseCase implements UseCase<List<CrearEntidadTerritorialCommand>, Void> {
    private final EntidadesTerritorialesRepository repository;
    private final EntidadesTerritorialesAppMapper mapper;

    @Transactional
    @Override
    public Void execute(List<CrearEntidadTerritorialCommand> input) {
        var toCreateList = input.stream().map(e -> mapper.toDomainEntity(e)).toList();
        var repeatedEntities = repository.checkStock(toCreateList);
        if (!repeatedEntities.isEmpty()) {
            String message = String.format("Duplicados [%s]", String.join(",",
                    repeatedEntities.stream().map(e -> e.getCodigo() + " - " + e.getNombre()).toList()));
            throw new EntityAlreadyExistsException(message);
        }
        repository.saveAll(toCreateList);
        return null;
    }
}
