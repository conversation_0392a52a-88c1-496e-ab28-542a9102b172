package co.com.gedsys.base.application.usecase.planeacion.clasificacion_documental;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.ClasificacionDS;
import co.com.gedsys.base.application.mapper.ClasificacionDocumentalMapper;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@RequiredArgsConstructor
public class BuscarClasificacionDocumentalUseCase implements UseCase<UUID, ClasificacionDS> {
    private final ClasificacionDocumentalRepository repository;

    private final ClasificacionDocumentalMapper map;

    @Override
    public ClasificacionDS execute(UUID input) {
        return repository.findById(input)
                .map(map::toDS)
                .orElseThrow(() ->
                        new EntityNotExistsException("Clasificación documental con Id: " + input + " no encontrada"));
    }
}
