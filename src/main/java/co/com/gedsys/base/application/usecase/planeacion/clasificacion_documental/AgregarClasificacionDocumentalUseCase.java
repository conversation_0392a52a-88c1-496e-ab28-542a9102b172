package co.com.gedsys.base.application.usecase.planeacion.clasificacion_documental;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import co.com.gedsys.base.domain.organizacion.CodigoSeccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import co.com.gedsys.base.domain.serie_documental.CodigoSerie;
import co.com.gedsys.base.domain.serie_documental.SerieDocumentalRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class AgregarClasificacionDocumentalUseCase implements UseCase<AgregarClasificacionDocumentalCommand, Void> {
    private final SeccionRepository seccionRepository;
    private final SerieDocumentalRepository serieDocumentalRepository;
    private final ClasificacionDocumentalRepository repository;

    @Override
    public Void execute(AgregarClasificacionDocumentalCommand input) {
        var seccionFormateada = new CodigoSeccion(input.unidadProductora());
        var serieFormateada = new CodigoSerie(input.serieDocumental());
        var seccion = seccionRepository.findByCode(seccionFormateada.getValue())
                .orElseThrow(() -> new EntityNotExistsException("Sección " + seccionFormateada.getValue()));
        var serie = serieDocumentalRepository.findByCode(serieFormateada.getValue())
                .orElseThrow(() -> new EntityNotExistsException("Serie Documental " + serieFormateada.getValue()));
        var domainEntity = new ClasificacionDocumental(seccion, serie);
        if (repository.checkStock(domainEntity)) throw new ClasificacionDocumentalDuplicadaException();

        repository.save(domainEntity);

        return null;
    }
}
