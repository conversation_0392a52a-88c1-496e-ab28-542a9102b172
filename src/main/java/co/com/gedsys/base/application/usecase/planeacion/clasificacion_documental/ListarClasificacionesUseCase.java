package co.com.gedsys.base.application.usecase.planeacion.clasificacion_documental;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.ClasificacionDS;
import co.com.gedsys.base.application.mapper.ClasificacionDocumentalMapper;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ListarClasificacionesUseCase implements UseCase<Void, List<ClasificacionDS>> {
    private final ClasificacionDocumentalRepository repository;
    private final ClasificacionDocumentalMapper map;

    @Override
    public List<ClasificacionDS> execute(Void input) {
        var clasificaciones = repository.findAll();
        return map.toDSList(clasificaciones);
    }
}
