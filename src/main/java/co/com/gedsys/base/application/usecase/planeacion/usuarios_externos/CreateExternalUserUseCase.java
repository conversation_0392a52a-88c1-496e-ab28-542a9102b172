package co.com.gedsys.base.application.usecase.planeacion.usuarios_externos;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.ExternalUserDto;
import co.com.gedsys.base.application.mapper.ExternalUsersAppMapper;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.CreateExternalUserCommand;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserIdentificationType;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
public class CreateExternalUserUseCase implements UseCase<CreateExternalUserCommand, ExternalUserDto> {
    private final ExternalUsersRepository externalUsersRepository;
    private final ExternalUsersAppMapper mapper;

    @Transactional
    @Override
    public ExternalUserDto execute(CreateExternalUserCommand input) {
        // Normalizar entrada para tipo NA
        String normalizedNumber = normalizeIdentificationNumber(input.identificationType(), input.identificationNumber());

        var externalUser = new ExternalUser(input.name(), input.identificationType(), normalizedNumber);

        // Validación de unicidad adaptada para tipo NA
        boolean externalUserAlreadyExists = checkUserExists(externalUser);
        if (externalUserAlreadyExists) {
            throw new EntityAlreadyExistsException(buildDuplicateMessage(externalUser));
        }

        externalUser = mapper.toDomain(input, externalUser);
        var saved = externalUsersRepository.save(externalUser);

        return mapper.toDto(saved);
    }

    private String normalizeIdentificationNumber(String type, String number) {
        return "NA".equals(type) ? null : number;
    }

    private boolean checkUserExists(ExternalUser user) {
        if (user.getIdentificationType() == ExternalUserIdentificationType.NA) {
            // Para tipo NA, verificar unicidad por nombre y tipo
            return externalUsersRepository.existsByNameAndIdentificationType(
                user.getName(),
                ExternalUserIdentificationType.NA
            );
        } else {
            // Para otros tipos, usar la lógica existente (por número de identificación)
            return externalUsersRepository.checkStock(user);
        }
    }

    private String buildDuplicateMessage(ExternalUser user) {
        if (user.getIdentificationType() == ExternalUserIdentificationType.NA) {
            return String.format("Ya existe un usuario con nombre '%s' y tipo NA", user.getName());
        }
        return String.format("El usuario con identificación %s ya existe", user.getIdentificationNumber());
    }

}
