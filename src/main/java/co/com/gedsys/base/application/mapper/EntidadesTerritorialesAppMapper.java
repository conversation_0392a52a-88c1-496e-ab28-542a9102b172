package co.com.gedsys.base.application.mapper;

import co.com.gedsys.base.application.dto.EntidadTerritorialDto;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.CrearEntidadTerritorialCommand;
import co.com.gedsys.base.domain.entidad_territorial.EntidadTerritorial;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface EntidadesTerritorialesAppMapper {

    EntidadTerritorialDto toDto(EntidadTerritorial entity);

    @Mapping(target = "tipo", ignore = true)
    @Mapping(target = "id", ignore = true)
    EntidadTerritorial toDomainEntity(CrearEntidadTerritorialCommand c);
}
