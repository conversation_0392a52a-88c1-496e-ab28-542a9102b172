package co.com.gedsys.base.application.usecase.planeacion.unidades_documentales;

import lombok.RequiredArgsConstructor;

import java.util.UUID;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.mapper.UnidadDocumentalMapper;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;

@Service
@RequiredArgsConstructor
public class BuscarUnidadDocumentalUseCase implements UseCase<UUID, BuscarUnidadDocumentalQuery> {
    private final UnidadDocumentalRepository unidadDocumentalRepository;
    private final UnidadDocumentalMapper mapper;

    @Override
    public BuscarUnidadDocumentalQuery execute(UUID input) {
        return unidadDocumentalRepository.findById(input)
                .map(mapper::toQuery)
                .orElseThrow(() -> new EntityNotExistsException("Unidad Documental con id " + input + " no encontrada"));
    }
}
