package co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.EntidadTerritorialDto;
import co.com.gedsys.base.application.mapper.EntidadesTerritorialesAppMapper;
import co.com.gedsys.base.domain.entidad_territorial.EntidadesTerritorialesRepository;

@RequiredArgsConstructor
@Service
public class ActualizarEntidadTerritorialUseCase implements UseCase<ActualizarEntidadTerritorialCommand, EntidadTerritorialDto> {
    private final EntidadesTerritorialesRepository repository;
    private final EntidadesTerritorialesAppMapper mapper;

    @Override
    public EntidadTerritorialDto execute(ActualizarEntidadTerritorialCommand input) {
        boolean entityExists = repository.checkStock(input.codigo(), input.nombre());
        if (entityExists)
            throw new EntityAlreadyExistsException("Ya existe una entidad territorial con el código o el nombre especificado");
        var entityFounded = repository.findById(input.id())
                .orElseThrow(() -> new EntityNotExistsException("Entidad territorial con id " + input.id() + " no encontrada"));
        entityFounded.setCodigo(input.codigo());
        entityFounded.setNombre(input.nombre());
        var saved = repository.save(entityFounded);
        return mapper.toDto(saved);
    }
}
