package co.com.gedsys.base.application.common;

/**
 * Excepción lanzada cuando se intenta activar una sección sin responsable asignado.
 * Esta excepción forma parte de las reglas de negocio que establecen que una sección
 * solo puede ser activada si tiene un responsable asignado.
 */
public class SeccionSinResponsableException extends RuntimeException {

    /**
     * Constructor por defecto con mensaje predeterminado.
     */
    public SeccionSinResponsableException() {
        super("No se puede activar una sección sin responsable asignado");
    }

    /**
     * Constructor que permite especificar un mensaje personalizado.
     *
     * @param message el mensaje de error personalizado
     */
    public SeccionSinResponsableException(String message) {
        super(message);
    }

    /**
     * Constructor que permite formatear el mensaje con argumentos.
     *
     * @param format el formato del mensaje (estilo String.format)
     * @param args   los argumentos para el formato
     */
    public SeccionSinResponsableException(String format, Object... args) {
        super(String.format(format, args));
    }
} 