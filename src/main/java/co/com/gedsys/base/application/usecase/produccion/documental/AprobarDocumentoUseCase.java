package co.com.gedsys.base.application.usecase.produccion.documental;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.usecase.produccion.documental.command.AprobarDocumentoCommand;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class AprobarDocumentoUseCase implements UseCase<AprobarDocumentoCommand, Void> {
    private final DocumentoRepository documentRepository;

    @Override
    public Void execute(AprobarDocumentoCommand input) {
        var document = documentRepository.findById(input.documentId())
                .orElseThrow(() -> new EntityNotExistsException(
                        "No se encontró el documento con el ID: " + input.documentId()));
        document.aprobar(input.aprobador());
        documentRepository.save(document);
        return null;
    }
}
