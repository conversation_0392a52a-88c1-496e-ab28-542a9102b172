package co.com.gedsys.base.application.usecase.planeacion.plantillas;

import java.util.List;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.PlantillaDTO;
import co.com.gedsys.base.application.mapper.PlantillasAppMapper;
import co.com.gedsys.base.domain.plantillas.PlantillaRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ListarPlantillasUseCase implements UseCase<ListarPlantillasQuery, List<PlantillaDTO>> {
    private final PlantillaRepository repository;
    private final PlantillasAppMapper mapper;

    @Override
    public List<PlantillaDTO> execute(ListarPlantillasQuery input) {
        return repository.filtrarPorTipo(input.tipo())
                .stream()
                .map(mapper::toDto)
                .toList();
    }
}
