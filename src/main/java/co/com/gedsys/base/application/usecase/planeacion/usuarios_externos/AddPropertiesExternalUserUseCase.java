package co.com.gedsys.base.application.usecase.planeacion.usuarios_externos;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.ExternalUserDto;
import co.com.gedsys.base.application.mapper.ExternalUsersAppMapper;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.AddPropertiesExternalUserCommand;
import co.com.gedsys.base.domain.usuario_externo.DuplicatePropertyException;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;

@Service
@RequiredArgsConstructor
public class AddPropertiesExternalUserUseCase implements UseCase<AddPropertiesExternalUserCommand, ExternalUserDto> {
    private final ExternalUsersRepository externalUsersRepository;
    private final ExternalUsersAppMapper mapper;

    @Override
    public ExternalUserDto execute(AddPropertiesExternalUserCommand input) {
        if (input.properties().isEmpty())
            throw new IllegalArgumentException("No hay propiedades para añadir");

        try {
            var externalUser = externalUsersRepository
                    .findById(input.externalUserId())
                    .orElseThrow(() -> new EntityNotExistsException(
                            "Usuario externo con id " + input.externalUserId() + " no encontrado"))
                    .addProperties(input.properties().stream().map(mapper::toDomain).toList());
            return mapper.toDto(externalUsersRepository.save(externalUser));
        } catch (DuplicatePropertyException e) {
            throw new IllegalArgumentException(e.getMessage(), e);
        }
    }
}
