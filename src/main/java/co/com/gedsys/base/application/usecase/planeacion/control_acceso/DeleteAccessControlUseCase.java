package co.com.gedsys.base.application.usecase.planeacion.control_acceso;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;

import co.com.gedsys.base.domain.control_acceso.AccessControlRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.UUID;

@RequiredArgsConstructor
@Service
public class DeleteAccessControlUseCase implements UseCase<UUID, Void> {

    private final AccessControlRepository repository;

    @Override
    public Void execute(UUID input) {
        if (!repository.checkStock(input.toString())) {
            throw new EntityNotExistsException("Access Control Policy with id " + input + " not found. Cannot delete.");
        }
        repository.delete(input);
        return null;
    }
}
