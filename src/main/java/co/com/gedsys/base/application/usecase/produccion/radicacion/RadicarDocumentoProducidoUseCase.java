package co.com.gedsys.base.application.usecase.produccion.radicacion;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.mapper.RadicadoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.produccion.radicacion.command.RadicarDocumentoProducidoCommand;
import co.com.gedsys.base.application.usecase.produccion.radicacion.exception.PoliticaUnicoRadicadoDeProduccionException;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.radicado.EstadoRadicado;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class RadicarDocumentoProducidoUseCase implements UseCase<RadicarDocumentoProducidoCommand, RadicadoDTO> {

    private final DocumentoRepository documentRepository;
    private final ConsecutivoRepository consecutivoRepository;
    private final RadicadoRepository radicadoRepository;
    private final RadicadoApplicationLayerMapper radicadoMapper;
    @Transactional
    @Override
    public RadicadoDTO execute(RadicarDocumentoProducidoCommand command) {
        Documento documento = obtenerDocumentoConRadicados(command.documentId());
        validarDocumentoNoTieneRadicadoProduccionActivo(documento);
        Consecutivo consecutivo = obtenerConsecutivoParaDocumento(documento);
        Radicado nuevoRadicado = new Radicado(consecutivo);
        nuevoRadicado.expedir(documento);
        persistirRadicacion(nuevoRadicado);
        return radicadoMapper.toDTO(nuevoRadicado);
    }

    private Documento obtenerDocumentoConRadicados(UUID documentId) {
        Documento documento = documentRepository
                .findById(documentId)
                .orElseThrow(() -> new EntityNotExistsException("No se encontró el documento con id " + documentId));
        List<Radicado> radicados = radicadoRepository.buscarRadicadosDeDocumento(documentId);
        documento.setRadicados(radicados);
        return documento;
    }

    private void validarDocumentoNoTieneRadicadoProduccionActivo(Documento documento) {
        boolean tieneRadicadoProduccionActivo = documento.getRadicados().stream()
                .anyMatch(this::esRadicadoProduccionActivo);

        if (tieneRadicadoProduccionActivo) {
            throw new PoliticaUnicoRadicadoDeProduccionException();
        }
    }

    private boolean esRadicadoProduccionActivo(Radicado radicado) {
        return radicado.getEstado().equals(EstadoRadicado.ACTIVO) &&
                radicado.getTipo().equals(TipoConsecutivo.PRODUCCION);
    }

    private Consecutivo obtenerConsecutivoParaDocumento(Documento documento) {
        List<Consecutivo> consecutivosDisponibles = consecutivoRepository
                .buscarConsecutivosParaTipoDocumental(documento.getTipoDocumental());

        return buscarConsecutivoSegunEspecificidad(consecutivosDisponibles, documento);
    }

    private Consecutivo buscarConsecutivoSegunEspecificidad(List<Consecutivo> consecutivos, Documento documento) {
        return buscarConsecutivoEspecifico(consecutivos, documento.getClasificacionDocumentalId())
                .orElseGet(() -> buscarConsecutivoGeneral(consecutivos, documento.getTipoDocumentalId())
                        .orElseGet(() -> crearNuevoConsecutivoGeneral(documento.getTipoDocumental())));
    }

    private Optional<Consecutivo> buscarConsecutivoEspecifico(List<Consecutivo> consecutivos, UUID clasificacionId) {
        return consecutivos.stream()
                .filter(consecutivo -> clasificacionId.equals(consecutivo.getClasificacionDocumentalId()))
                .findFirst();
    }

    private Optional<Consecutivo> buscarConsecutivoGeneral(List<Consecutivo> consecutivos, UUID tipoDocumentalId) {
        return consecutivos.stream()
                .filter(consecutivo -> tipoDocumentalId.equals(consecutivo.getTipoDocumentalId()))
                .findFirst();
    }

    private Consecutivo crearNuevoConsecutivoGeneral(TipoDocumental tipoDocumental) {
        return new Consecutivo(null, null, 0, tipoDocumental, null);
    }

    private void persistirRadicacion(Radicado radicado) {
        consecutivoRepository.save(radicado.getConsecutivo());
        radicadoRepository.save(radicado);
    }
}
