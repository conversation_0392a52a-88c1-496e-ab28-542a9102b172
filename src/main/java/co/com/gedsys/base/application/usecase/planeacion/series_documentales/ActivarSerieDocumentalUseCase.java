package co.com.gedsys.base.application.usecase.planeacion.series_documentales;

import co.com.gedsys.base.application.dto.SerieDocumentalDto;
import co.com.gedsys.base.application.mapper.SerieDocumentalMapper;
import co.com.gedsys.base.domain.serie_documental.SerieDocumentalRepository;
import co.com.gedsys.base.domain.serie_documental.SeriePadreInactivaException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class ActivarSerieDocumentalUseCase {

    private final SerieDocumentalRepository serieDocumentalRepository;
    private final SerieDocumentalMapper mapper;

    @Transactional
    public SerieDocumentalDto ejecutar(ActivarSerieDocumentalCommand command) {
        var serieDocumental = serieDocumentalRepository.findById(command.id())
                .orElseThrow(() -> new IllegalArgumentException("Serie documental no encontrada"));

        if (serieDocumental.esSubserie() && serieDocumentalRepository.tienePadreInactivo(command.id())) {
            throw new SeriePadreInactivaException("La serie padre se encuentra inactiva y no se puede activar la subserie.");
        }

        serieDocumental.activar();
        serieDocumentalRepository.guardar(serieDocumental);

        return mapper.toDto(serieDocumental);
    }
}