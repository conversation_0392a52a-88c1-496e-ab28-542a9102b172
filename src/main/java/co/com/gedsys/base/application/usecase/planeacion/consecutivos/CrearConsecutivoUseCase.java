package co.com.gedsys.base.application.usecase.planeacion.consecutivos;

import java.util.UUID;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.ConsecutivoDTO;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class CrearConsecutivoUseCase implements UseCase<CrearConsecutivoCommand, ConsecutivoDTO> {
    private final ConsecutivoRepository consecutivoRepository;
    private final TipoDocumentalRepository tipoDocumentalRepository;
    private final ClasificacionDocumentalRepository clasificacionDocumentalRepository;
    private final ConsecutivoMapper mapper;

    @Override
    public ConsecutivoDTO execute(CrearConsecutivoCommand command) {
        var consecutivo = createConsecutivoBasedOnType(command);
        return mapper.toDto(saveConsecutivo(consecutivo));
    }

    private Consecutivo createConsecutivoBasedOnType(CrearConsecutivoCommand command) {
        return command.tipo() == TipoConsecutivo.PRODUCCION
                ? createConsecutivoProduccion(command)
                : createConsecutivoRegular(command);
    }

    private Consecutivo saveConsecutivo(Consecutivo consecutivo) {
        return consecutivoRepository.save(consecutivo);
    }

    private Consecutivo createConsecutivoRegular(CrearConsecutivoCommand command) {
        if (consecutivoRepository.buscarConsecutivoPorTipo(command.tipo()).size() > 0) {
            throw new ConsecutivoUnicoTipoException(command.tipo());
        }

        return new Consecutivo(
                command.prefijo(),
                command.sufijo(),
                command.contador(),
                command.tipo());
    }

    private Consecutivo createConsecutivoProduccion(CrearConsecutivoCommand command) {
        var tipoDocumental = getTipoDocumental(command.tipoDocumentalId());
        var clasificacionDocumental = getClasificacionDocumental(command.clasificacionDocumentalId());

        var consecutivo = new Consecutivo(
                command.prefijo(),
                command.sufijo(),
                command.contador(),
                tipoDocumental,
                clasificacionDocumental);

        validateNoPreexistingConsecutivoProduccion(consecutivo);
        return consecutivo;
    }

    private void validateNoPreexistingConsecutivoProduccion(Consecutivo consecutivo) {
        consecutivoRepository.findByExample(consecutivo)
                .ifPresent((_) -> {
                    throw new ConsecutivoAlreadyExistsException();
                });
    }

    private TipoDocumental getTipoDocumental(UUID id) {
        return tipoDocumentalRepository.findById(id)
                .orElse(null);
    }

    private ClasificacionDocumental getClasificacionDocumental(UUID id) {
        return clasificacionDocumentalRepository.findById(id)
                .orElse(null);
    }
}
