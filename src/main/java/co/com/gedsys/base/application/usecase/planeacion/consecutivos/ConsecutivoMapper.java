package co.com.gedsys.base.application.usecase.planeacion.consecutivos;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import co.com.gedsys.base.application.dto.ConsecutivoDTO;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface ConsecutivoMapper {

    ConsecutivoDTO toDto(Consecutivo consecutivo);

    @Mapping(target = "nombre", expression = "java(tipoDocumental.nombre())")
    ConsecutivoDTO.TipoDocumentalConsecutivo toTipoDocumentalConsecutivo(TipoDocumental tipoDocumental);

    @Mapping(target = "nombreSeccion", source = "seccion.nombre")
    @Mapping(target = "codigo", expression = "java(clasificacionDocumental.codigoCompleto())")
    ConsecutivoDTO.ClasificacionDocumentalConsecutivo toClasificacionDocumentalConsecutivo(
            ClasificacionDocumental clasificacionDocumental);
}
