package co.com.gedsys.base.application.usecase.planeacion.series_documentales;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import co.com.gedsys.base.domain.serie_documental.SerieDocumentalRepository;
import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class RegistrarSerieDocumentalUseCase implements UseCase<RegistrarSerieDocumentalCommand, Void> {
    private final SerieDocumentalRepository repository;

    @Override
    public Void execute(RegistrarSerieDocumentalCommand input) {
        var domainEntity = new SerieDocumental(input.codigo(), input.nombre());
        boolean alreadyExists = repository.existenciaPorCodigo(domainEntity.codigo());
        if (alreadyExists) {
            throw new EntityAlreadyExistsException("Ya existe una clasificación con el código " + domainEntity.codigo());
        }

        if (domainEntity.esSubserie()) {
            var serieContenedora = repository
                    .findByCode(domainEntity.codigoSeriePadre())
                    .orElseThrow(() -> new EntityNotExistsException("No se encontró padreId de la sub serie con código " + domainEntity.codigoSeriePadre()));
            domainEntity.setPadre(serieContenedora);
        }
        repository.guardar(domainEntity);
        return null;
    }
}
