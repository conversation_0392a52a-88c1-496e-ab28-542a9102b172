package co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.EntidadTerritorialDto;
import co.com.gedsys.base.application.mapper.EntidadesTerritorialesAppMapper;
import co.com.gedsys.base.domain.entidad_territorial.EntidadesTerritorialesRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class ListarTodasLasEntidadesTerritorialesUseCase implements UseCase<Void, List<EntidadTerritorialDto>> {
    private final EntidadesTerritorialesRepository repository;
    private final EntidadesTerritorialesAppMapper mapper;

    @Override
    public List<EntidadTerritorialDto> execute(Void input) {
        return repository.findAll()
                .stream()
                .map(mapper::toDto)
                .toList();
    }
}
