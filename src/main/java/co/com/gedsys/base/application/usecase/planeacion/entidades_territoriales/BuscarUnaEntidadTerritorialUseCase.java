package co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.EntidadTerritorialDto;
import co.com.gedsys.base.application.mapper.EntidadesTerritorialesAppMapper;
import co.com.gedsys.base.domain.entidad_territorial.EntidadesTerritorialesRepository;

@RequiredArgsConstructor
@Service
public class BuscarUnaEntidadTerritorialUseCase implements UseCase<Integer, EntidadTerritorialDto> {
    private final EntidadesTerritorialesRepository repository;
    private final EntidadesTerritorialesAppMapper mapper;

    @Override
    public EntidadTerritorialDto execute(Integer input) {
        return repository.findById(input)
                .map(mapper::toDto)
                .orElseThrow(() -> new EntityNotExistsException(
                        "Entidad territorial con id " + input + " no encontrada"));
    }
}
