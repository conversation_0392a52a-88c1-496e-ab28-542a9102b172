package co.com.gedsys.base.application.mapper;

import co.com.gedsys.base.application.dto.CCDDto;
import co.com.gedsys.base.application.dto.ClasificacionDS;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ClasificacionDocumentalMapper {


    @Mapping(target = "subSerieNombre", source = "nombreSubSerie")
    @Mapping(target = "subSerieCodigo", source = "codigoSubSerie")
    @Mapping(target = "subSeccionNombre", source = "nombreDependencia")
    @Mapping(target = "subSeccionCodigo", source = "codigoDependencia")
    @Mapping(target = "serieNombre", source = "nombreSerie")
    @Mapping(target = "serieCodigo", source = "codigoSerie")
    @Mapping(target = "seccionNombre", source = "nombreOficinaPrincipal")
    @Mapping(target = "seccionCodigo", source = "codigoOficinaPrincipal")
    CCDDto toDto(ClasificacionDocumental clasificacion);

    List<CCDDto> toDtoList(List<ClasificacionDocumental> clasificaciones);

    @Mapping(target = "serie", source = "subSerie")
    @Mapping(target = "codigo", expression = "java(clasificacion.codigoCompleto())")
    ClasificacionDS toDS(ClasificacionDocumental clasificacion);
    List<ClasificacionDS> toDSList(List<ClasificacionDocumental> clasificaciones);

    @Mapping(target = "codigo", expression = "java(seccion.codigo())")
    ClasificacionDS.SeccionDS toDS(Seccion seccion);

    @Mapping(target = "codigo", expression = "java(serie.codigo())")
    ClasificacionDS.SerieDS toDS(SerieDocumental serie);

    @Mapping(target = "nombre", expression = "java(tipoDocumental.nombre())")
    ClasificacionDS.TipoDocumentalDS toDS(TipoDocumental tipoDocumental);
}
