package co.com.gedsys.base.application.usecase.planeacion.seccion;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import co.com.gedsys.base.domain.organizacion.TipoRelacionUsuarioSeccion;
import co.com.gedsys.base.domain.organizacion.UsuarioSinSeccionPrimariaException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class AgregarUsuarioSeccionUseCase implements UseCase<AgregarUsuarioSeccionCommand, Void> {

    private final SeccionRepository repository;

    @Override
    public Void execute(AgregarUsuarioSeccionCommand input) {
        Seccion seccion = repository.findById(input.seccionId())
                .orElseThrow(() -> new EntityNotExistsException(
                        String.format("No existe una sección con el id '%s'", input.seccionId())));

        // Si se está intentando agregar una sección secundaria, verificar que el usuario tenga una sección primaria
        if (input.tipoRelacion() == TipoRelacionUsuarioSeccion.SECUNDARIA) {
            validarUsuarioTieneSeccionPrimaria(input.username());
        }

        seccion.agregarUsuario(input.username(), input.tipoRelacion());
        repository.save(seccion);

        return null;
    }

    private void validarUsuarioTieneSeccionPrimaria(String username) {
        List<Seccion> secciones = repository.buscarSeccionesPorEstado(co.com.gedsys.base.domain.organizacion.EstadoSeccion.ACTIVA);

        boolean tienePrimaria = secciones.stream()
            .flatMap(seccion -> seccion.getUsuarios().stream())
            .anyMatch(usuarioSeccion -> 
                usuarioSeccion.getUsername().equals(username) && 
                usuarioSeccion.getRelacion() == TipoRelacionUsuarioSeccion.PRIMARIA);

        if (!tienePrimaria) {
            throw new UsuarioSinSeccionPrimariaException(username);
        }
    }
}
