package co.com.gedsys.base.application.usecase.planeacion.tipologia;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class RegistrarTipoDocumentalUseCase implements UseCase<RegistrarTipoDocumentalCommand, Void> {
    private final TipoDocumentalRepository repository;

    @Override
    public Void execute(RegistrarTipoDocumentalCommand input) {
        validateUnrepeatedPolicy(input.nombre());
        var newTipoDocumental = new TipoDocumental(input.nombre());
        repository.save(newTipoDocumental);
        return null;
    }

    private void validateUnrepeatedPolicy(String name) {
        if (repository.checkStock(name))
            throw new EntityAlreadyExistsException("El tipo de documento: \"%s\" ya existe", name);
    }
}
