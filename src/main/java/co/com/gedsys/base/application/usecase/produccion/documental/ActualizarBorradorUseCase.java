package co.com.gedsys.base.application.usecase.produccion.documental;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.application.mapper.DocumentoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.produccion.documental.command.UpdateDraftCommand;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.metadato.DefinicionMetadato;
import co.com.gedsys.base.domain.metadato.Metadato;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@AllArgsConstructor
public class ActualizarBorradorUseCase implements UseCase<UpdateDraftCommand, DocumentoDTO> {

    private final DocumentoRepository documentoRepository;
    private final DefinicionMetadatosRepository definicionMetadatosRepository;
    private final DocumentoApplicationLayerMapper mapper;
    private final ActualizarBorradorUseCaseMapper commandMapper;

    @Override
    @Transactional
    public DocumentoDTO execute(UpdateDraftCommand command) {
        var documento = documentoRepository.findById(command.documentId())
                .orElseThrow(() -> new EntityNotExistsException("Documento no encontrado"));

        if (command.title() != null) {
            documento.setTitulo(command.title());
        }

        if (command.metadatos() != null && !command.metadatos().isEmpty()) {
            // Verificar si hay cambios en los metadatos existentes o si hay nuevos metadatos
            if (hayNuevosMetadatosOCambios(documento.getMetadatos(), command.metadatos())) {
                var metadatos = buildMetadatos(documento.getMetadatos(), command.metadatos());
                documento.setMetadatos(metadatos);
            }
        }

        if (command.anexos() != null && !command.anexos().isEmpty()) {
            var anexos = commandMapper.toAnexos(command.anexos());
            documento.setAnexos(anexos);
        }

        return mapper.toDTO(documentoRepository.save(documento));
    }

    /**
     * Verifica si hay nuevos metadatos o cambios en los existentes
     * @param metadatosExistentes Metadatos actuales del documento
     * @param inputMetadatos Nuevos metadatos a evaluar
     * @return true si hay nuevos metadatos o cambios, false en caso contrario
     */
    private boolean hayNuevosMetadatosOCambios(Set<Metadato> metadatosExistentes, Map<String, String> inputMetadatos) {
        // Crear un mapa para acceder rápidamente a los metadatos existentes por patrón
        Map<String, Metadato> metadatosExistentesPorPatron = new HashMap<>();
        for (Metadato metadato : metadatosExistentes) {
            metadatosExistentesPorPatron.put(metadato.patron(), metadato);
        }

        // Verificar si hay nuevos patrones o valores diferentes
        for (Map.Entry<String, String> entry : inputMetadatos.entrySet()) {
            String patron = entry.getKey();
            String nuevoValor = entry.getValue();

            // Si el patrón no existe o el valor es diferente, hay cambios
            Metadato metadatoExistente = metadatosExistentesPorPatron.get(patron);
            if (metadatoExistente == null || !metadatoExistente.valor().equals(nuevoValor)) {
                return true;
            }
        }

        return false;
    }

    private Set<Metadato> buildMetadatos(Set<Metadato> metadatosExistentes, Map<String, String> inputMetadatos) {
        var definiciones = definicionMetadatosRepository.buscarPorPatrones(
                new ArrayList<>(inputMetadatos.keySet())
        );

        var metadatosExistentesPorPatron = new HashMap<String, Metadato>();
        for (Metadato metadato : metadatosExistentes) {
            metadatosExistentesPorPatron.put(metadato.patron(), metadato);
        }

        var metadatosResultantes = new HashSet<Metadato>(metadatosExistentes);

        for (DefinicionMetadato definicion : definiciones) {
            String patron = definicion.getPatron();
            String nuevoValor = inputMetadatos.get(patron);

            Metadato metadatoExistente = metadatosExistentesPorPatron.get(patron);
            boolean esNuevoOActualizado = metadatoExistente == null || !metadatoExistente.valor().equals(nuevoValor);

            if (esNuevoOActualizado) {
                if (metadatoExistente != null) {
                    metadatosResultantes.remove(metadatoExistente);
                }

                Metadato nuevoMetadato = definicion.generarMetadato(nuevoValor);
                metadatosResultantes.add(nuevoMetadato);
            }
        }

        return metadatosResultantes;
    }
}
