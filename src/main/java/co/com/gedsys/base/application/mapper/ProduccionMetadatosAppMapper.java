package co.com.gedsys.base.application.mapper;

import co.com.gedsys.base.application.dto.MetadatoDTO;
import co.com.gedsys.base.domain.metadato.Metadato;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.Set;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ProduccionMetadatosAppMapper {

    @Mapping(target = "id", expression = "java(metadato.id().toString())")
    @Mapping(target = "patron", expression = "java(metadato.patron().toString())")
    @Mapping(target = "nombre", expression = "java(metadato.nombre())")
    @Mapping(target = "tipo", expression = "java(metadato.tipo().toString())")
    @Mapping(target = "formato", expression = "java(metadato.formato().toString())")
    @Mapping(target = "valor", expression = "java(metadato.valor())")
    MetadatoDTO toDTO(Metadato metadato);
    Set<MetadatoDTO> toDTO(Set<Metadato> metadatos);
}
