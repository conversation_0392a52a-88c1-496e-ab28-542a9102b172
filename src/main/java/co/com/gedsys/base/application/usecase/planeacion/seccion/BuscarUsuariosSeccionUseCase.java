package co.com.gedsys.base.application.usecase.planeacion.seccion;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.UsuarioSeccionDTO;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionMapper;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BuscarUsuariosSeccionUseCase implements UseCase<BuscarUsuariosSeccionCommand, List<UsuarioSeccionDTO>> {

    private final SeccionRepository repository;
    private final SeccionMapper seccionMapper;

    public BuscarUsuariosSeccionUseCase(
            SeccionRepository repository,
            SeccionMapper seccionMapper) {
        this.repository = repository;
        this.seccionMapper = seccionMapper;
    }

    @Override
    public List<UsuarioSeccionDTO> execute(BuscarUsuariosSeccionCommand input) {
        Seccion seccion = repository.findById(input.seccionId())
                .orElseThrow(() -> new EntityNotExistsException(
                        String.format("No existe una sección con el id '%s'", input.seccionId())));

        return seccion.getUsuarios()
                .stream().map(seccionMapper::toDTO)
                .toList();
    }
}