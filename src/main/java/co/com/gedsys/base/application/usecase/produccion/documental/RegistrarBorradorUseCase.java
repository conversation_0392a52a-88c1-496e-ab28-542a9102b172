package co.com.gedsys.base.application.usecase.produccion.documental;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.application.mapper.DocumentoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.produccion.documental.command.RegistrarBorradorCommand;
import co.com.gedsys.base.domain.documento.*;
import co.com.gedsys.base.domain.metadato.Metadato;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;
import lombok.AllArgsConstructor;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class RegistrarBorradorUseCase implements UseCase<RegistrarBorradorCommand, DocumentoDTO> {
    private final TipoDocumentalRepository tipoDocumentalRepository;
    private final UnidadDocumentalRepository unidadDocumentalRepository;
    private final DefinicionMetadatosRepository definicionMetadatosRepository;
    private final DocumentoRepository documentRepository;
    private final DocumentoApplicationLayerMapper map;
    private final RegistrarBorradorUseCaseMapper commandMapper = RegistrarBorradorUseCaseMapper.INSTANCE;

    @Transactional
    @Override
    public DocumentoDTO execute(RegistrarBorradorCommand input) {
        var tipoDocumental = getTipoDocumental(input.tipoDocumentalId());
        var unidadDocumental = getUnidadDocumental(input.unidadDocumentalId());
        var metadatos = buildMetadatos(input.metadatos());
        var firmas = buildFirmas(input.firmas());
        var aprobaciones = buildAprobaciones(input.aprobaciones());
        var anexos = commandMapper.toAnexos(input.anexos());
        var documento = new Documento(
                input.titulo(),
                input.fileId(),
                tipoDocumental,
                unidadDocumental,
                input.autor(),
                metadatos,
                firmas,
                aprobaciones,
                anexos);

        return map.toDTO(documentRepository.save(documento));
    }

    private List<Aprobacion> buildAprobaciones(List<RegistrarBorradorCommand.Aprobacion> aprobaciones) {
        return Optional.ofNullable(aprobaciones)
                .orElse(List.of())
                .stream()
                .map(aprobacion -> new Aprobacion(aprobacion.aprobador()))
                .toList();
    }

    private List<FirmaUsuario> buildFirmas(List<RegistrarBorradorCommand.FirmaUsuario> firmas) {
        return firmas.stream()
                .map(commandMapper::toDomain)
                .toList();
    }

    private Set<Metadato> buildMetadatos(List<co.com.gedsys.base.application.dto.MetadatoDocumentoDTO> inputMetadatos) {
        Map<String, String> mapaMetadatos = inputMetadatos.stream()
                .collect(Collectors.toMap(
                        co.com.gedsys.base.application.dto.MetadatoDocumentoDTO::nombre,
                        co.com.gedsys.base.application.dto.MetadatoDocumentoDTO::valor
                ));

        var definiciones = definicionMetadatosRepository.buscarPorPatrones(
                new ArrayList<>(mapaMetadatos.keySet())
        );
        return definiciones.stream()
                .map(d -> d.generarMetadato(mapaMetadatos.get(d.getPatron())))
                .collect(Collectors.toUnmodifiableSet());
    }

    private TipoDocumental getTipoDocumental(UUID tipoDocumentalId) {
        return tipoDocumentalRepository
                .findById(tipoDocumentalId)
                .orElseThrow(() -> new EntityNotExistsException("Tipo de documento no encontrado"));
    }

    private UnidadDocumental getUnidadDocumental(UUID unidadDocumentalId) {
        return unidadDocumentalRepository
                .findById(unidadDocumentalId)
                .orElseThrow(() -> new EntityNotExistsException("Unidad documental no encontrada"));
    }
}

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
interface RegistrarBorradorUseCaseMapper {

    RegistrarBorradorUseCaseMapper INSTANCE = Mappers.getMapper(RegistrarBorradorUseCaseMapper.class);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "firmante", source = "owner")
    @Mapping(target = "firmadoEn", ignore = true)
    @Mapping(target = "estado", ignore = true)
    FirmaUsuario toDomain(RegistrarBorradorCommand.FirmaUsuario firmaUsuario);

    @Mapping(target = "id", ignore = true)
    Anexo toAnexo(RegistrarBorradorCommand.Anexo anexos);

    List<Anexo> toAnexos(List<RegistrarBorradorCommand.Anexo> anexos);
}
