package co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales;

import java.util.List;
import java.util.UUID;

public record UpdateExternalUserCommand(UUID id,
                                        String name,
                                        String salutation,
                                        String identificationType,
                                        String identificationNumber,
                                        String notes,
                                        String status,
                                        List<ExternalUserPropertyRegistration> properties
) {
    public record ExternalUserPropertyRegistration(
            UUID id,
            String propertyType,
            String propertyName,
            String propertyValue,
            String notes
    ) {
    }
}
