package co.com.gedsys.base.application.mapper;

import co.com.gedsys.base.application.dto.TipoDocumentalDTO;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface TiposDocumentalesAppMapper {

    @Mapping(target = "nombre", expression = "java(tipoDocumental.nombre())")
    TipoDocumentalDTO toDto(TipoDocumental tipoDocumental);

}
