package co.com.gedsys.base.application.usecase.planeacion.series_documentales;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.serie_documental.SerieDocumentalRepository;

@Service
public class BuscarSeriesUseCase implements UseCase<BuscarSeriesQuery, List<SerieDocumentalDto>> {
    private final SerieDocumentalRepository repository;
    private final SerieDocumentalMapper mapper;

    @Autowired
    public BuscarSeriesUseCase(SerieDocumentalRepository repository) {
        this.repository = repository;
        this.mapper = SerieDocumentalMapper.INSTANCE;
    }

    @Override
    public List<SerieDocumentalDto> execute(BuscarSeriesQuery input) {
        return repository.buscar(input.estado()).stream().map(mapper::toDto).toList();
    }
}
