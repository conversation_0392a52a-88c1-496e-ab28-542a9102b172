package co.com.gedsys.base.application.usecase.planeacion.plantillas;

import java.util.UUID;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.plantillas.Plantilla;
import co.com.gedsys.base.domain.plantillas.PlantillaRepository;
import co.com.gedsys.base.domain.plantillas.TipoPlantilla;
import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class RegistrarPlantillaUseCase implements UseCase<CreatePlantillaCommand, UUID> {

    private final PlantillaRepository repository;

    @Override
    public UUID execute(CreatePlantillaCommand input) {
        checkDuplicity(input);
        final var savedTemplate = saveTemplateWithTitle(input);
        return savedTemplate.getId();
    }

    private void checkDuplicity(CreatePlantillaCommand input) {
        var alreadyExists = repository.checkStock(input.titulo());
        if (alreadyExists) {
            throw new EntityAlreadyExistsException("El título de la plantilla ya existe");
        }
    }

    private Plantilla saveTemplateWithTitle(CreatePlantillaCommand command) {
        // Crear nueva plantilla
        Plantilla nuevaPlantilla = new Plantilla(command.titulo(), command.tipo());
        // Establecer producidoPorGedsys según el comando
        if (command.tipo() == TipoPlantilla.PRODUCCION && command.producidoPorGedsys()) {
            nuevaPlantilla.setProducidoPorGedsys(true);
        }
        return repository.save(nuevaPlantilla);
    }
}
