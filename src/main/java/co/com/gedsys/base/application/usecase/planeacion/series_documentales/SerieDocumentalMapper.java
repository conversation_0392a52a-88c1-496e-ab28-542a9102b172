package co.com.gedsys.base.application.usecase.planeacion.series_documentales;

import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface SerieDocumentalMapper {
    SerieDocumentalMapper INSTANCE = Mappers.getMapper(SerieDocumentalMapper.class);

    @Mapping(target = "codigo", expression = "java(domainEntity.codigo())")
    SerieDocumentalDto toDto(SerieDocumental domainEntity);
}