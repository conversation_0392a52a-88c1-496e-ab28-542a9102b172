package co.com.gedsys.base.application.usecase.planeacion.series_documentales;

import co.com.gedsys.base.application.dto.SerieDocumentalDto;
import co.com.gedsys.base.application.mapper.SerieDocumentalMapper;
import co.com.gedsys.base.domain.serie_documental.SerieDocumentalRepository;
import co.com.gedsys.base.domain.serie_documental.SerieTieneDependenciasActivasException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@RequiredArgsConstructor
public class InactivarSerieDocumentalUseCase {

    private final SerieDocumentalRepository serieDocumentalRepository;
    private final SerieDocumentalMapper mapper;

    @Transactional
    public SerieDocumentalDto ejecutar(InactivarSerieDocumentalCommand command) {
        var serieDocumental = serieDocumentalRepository.findById(command.id())
                .orElseThrow(() -> new IllegalArgumentException("Serie documental no encontrada"));

        if (tieneDependenciasActivas(command.id())) {
            throw new SerieTieneDependenciasActivasException("La serie documental tiene dependencias activas y no puede ser inactivada.");
        }

        serieDocumental.inactivar();
        serieDocumentalRepository.guardar(serieDocumental);

        return mapper.toDto(serieDocumental);
    }

    private boolean tieneDependenciasActivas(UUID serieId) {
        return serieDocumentalRepository.tieneHijasActivas(serieId) ||
                serieDocumentalRepository.tieneClasificacionesDocumentalesAsociadas(serieId) ||
                serieDocumentalRepository.tieneDocumentosAsociados(serieId) ||
                serieDocumentalRepository.tieneUnidadesDocumentalesAsociadas(serieId);
    }
}