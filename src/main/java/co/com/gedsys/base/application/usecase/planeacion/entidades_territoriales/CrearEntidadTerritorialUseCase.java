package co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales;


import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.EntidadTerritorialDto;
import co.com.gedsys.base.application.mapper.EntidadesTerritorialesAppMapper;
import co.com.gedsys.base.domain.entidad_territorial.EntidadTerritorial;
import co.com.gedsys.base.domain.entidad_territorial.EntidadesTerritorialesRepository;

@RequiredArgsConstructor
@Service
public class CrearEntidadTerritorialUseCase implements UseCase<CrearEntidadTerritorialCommand, EntidadTerritorialDto> {

    private final EntidadesTerritorialesRepository repository;
    private final EntidadesTerritorialesAppMapper mapper;

    @Override
    public EntidadTerritorialDto execute(CrearEntidadTerritorialCommand input) {
        var entidadTerritorial = new EntidadTerritorial(input.codigo(), input.nombre());
        if (repository.checkStock(entidadTerritorial)) {
            throw new EntityAlreadyExistsException("Ya existe una entidad territorial con el código o el nombre especificado");
        }
        var saved = repository.save(entidadTerritorial);
        return mapper.toDto(saved);
    }
}
