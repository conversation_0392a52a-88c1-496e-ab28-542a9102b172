package co.com.gedsys.base.application.usecase.planeacion.usuarios_externos;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.dto.ExternalUserDto;
import co.com.gedsys.base.application.mapper.ExternalUsersAppMapper;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@RequiredArgsConstructor
public class FindExternalUserUseCase {
    private final ExternalUsersAppMapper mapper;
    private final ExternalUsersRepository externalUsersRepository;

    public ExternalUserDto execute(UUID id) {
        return externalUsersRepository.findById(id)
                .map(mapper::toDto)
                .orElseThrow(() -> new EntityNotExistsException("No se encontró el usuario con id " + id));
    }
}
