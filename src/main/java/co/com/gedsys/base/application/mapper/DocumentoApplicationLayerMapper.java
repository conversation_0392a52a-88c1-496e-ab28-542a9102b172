package co.com.gedsys.base.application.mapper;

import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.metadato.Metadato;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface DocumentoApplicationLayerMapper {


    DocumentoDTO toDTO(Documento documento);

    @Mapping(target = "numero", source = "numeroRadicado")
    DocumentoDTO.RadicadoDS toDS(Radicado radicado);

    @Mapping(target = "nombre", expression = "java(tipoDocumental.nombre())")
    DocumentoDTO.TipoDocumentalDS toDS(TipoDocumental tipoDocumental);

    @Mapping(target = "nombre", expression = "java(unidadDocumental.nombre())")
    @Mapping(target = "descripcion", source = "descripcion")
    @Mapping(target = "codigoClasificacion", expression = "java(unidadDocumental.codigoClasificatorio())")
    DocumentoDTO.UnidadDocumentalDS toDS(UnidadDocumental unidadDocumental);

    @Mapping(target = "valor", expression = "java(metadato.valor())")
    @Mapping(target = "tipo", expression = "java(metadato.tipo().name())")
    @Mapping(target = "patron", expression = "java(metadato.patron())")
    @Mapping(target = "nombre", expression = "java(metadato.nombre())")
    @Mapping(target = "id", expression = "java(metadato.id().toString())")
    @Mapping(target = "formato", expression = "java(metadato.formato().name())")
    DocumentoDTO.MetadatoDS toDS(Metadato metadato);

}
