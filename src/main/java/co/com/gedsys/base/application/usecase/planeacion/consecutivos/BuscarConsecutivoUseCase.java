package co.com.gedsys.base.application.usecase.planeacion.consecutivos;

import java.util.UUID;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.ConsecutivoDTO;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class BuscarConsecutivoUseCase implements UseCase<UUID, ConsecutivoDTO> {
    private final ConsecutivoRepository repository;

    private final ConsecutivoMapper mapper;

    @Override
    public ConsecutivoDTO execute(UUID input) {
        return repository
                .findById(input)
                .map(mapper::toDto)
                .orElseThrow(() -> new EntityNotExistsException("Consecutivo " + input.toString() + " no encontrado"));
    }
}
