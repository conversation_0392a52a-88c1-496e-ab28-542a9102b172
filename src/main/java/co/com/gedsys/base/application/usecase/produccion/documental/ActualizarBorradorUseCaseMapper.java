package co.com.gedsys.base.application.usecase.produccion.documental;

import co.com.gedsys.base.application.usecase.produccion.documental.command.UpdateDraftCommand;
import co.com.gedsys.base.domain.documento.Anexo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.ERROR
)
public interface ActualizarBorradorUseCaseMapper {
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "nombre", source = "nombre")
    @Mapping(target = "descripcion", source = "descripcion")
    @Mapping(target = "fileId", source = "fileId")
    @Mapping(target = "hash", source = "hash")
    @Mapping(target = "bytes", source = "bytes")
    @Mapping(target = "extension", source = "extension")
    Anexo toAnexo(UpdateDraftCommand.RegistroAnexo anexo);

    List<Anexo> toAnexos(List<UpdateDraftCommand.RegistroAnexo> anexos);
} 