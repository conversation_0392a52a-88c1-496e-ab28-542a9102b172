package co.com.gedsys.base.application.usecase.planeacion.usuarios_externos;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@RequiredArgsConstructor
public class DeleteExternalUserUseCase implements UseCase<UUID, Void> {
    private final ExternalUsersRepository externalUsersRepository;

    @Override
    public Void execute(UUID input) {
        externalUsersRepository.findById(input)
                .orElseThrow(() -> new EntityNotExistsException("No se encontró el usuario con id " + input));
        externalUsersRepository.delete(input);
        return null;
    }
}
