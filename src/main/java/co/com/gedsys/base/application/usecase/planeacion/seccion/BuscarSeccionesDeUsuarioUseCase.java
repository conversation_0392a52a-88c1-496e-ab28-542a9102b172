package co.com.gedsys.base.application.usecase.planeacion.seccion;

import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.application.mapper.SeccionUseCaseMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class BuscarSeccionesDeUsuarioUseCase implements co.com.gedsys.base.application.common.UseCase<String, List<SeccionDTO>> {
    private final SeccionRepository seccionRepository;
    private final SeccionUseCaseMapper seccionUseCaseMapper;

    @Override
    public List<SeccionDTO> execute(String username) {
        return seccionRepository.buscarSeccionesPorUsuario(username)
                .stream()
                .map(seccionUseCaseMapper::toDto)
                .toList();
    }
}
