package co.com.gedsys.base.application.usecase.planeacion.control_acceso;

import co.com.gedsys.base.domain.control_acceso.AccessControlPolicy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", unmappedTargetPolicy = org.mapstruct.ReportingPolicy.ERROR)
interface AccessControlPlanningUseCaseDataMapper {

    AccessControlPolicyDto toDto(AccessControlPolicy domainEntity);

    @Mapping(target = "id", ignore = true)
    AccessControlPolicy toDomain(RegisterAccessControlCommand command);
}
