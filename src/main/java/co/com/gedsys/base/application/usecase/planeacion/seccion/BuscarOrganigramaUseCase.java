package co.com.gedsys.base.application.usecase.planeacion.seccion;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.usecase.planeacion.instrumentos.BuscarOrganigramaQuery;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class BuscarOrganigramaUseCase implements UseCase<BuscarOrganigramaQuery, Seccion> {
    private final SeccionRepository repository;

    @Override
    public Seccion execute(BuscarOrganigramaQuery input) {
        // 1. Obtener todas las secciones según el estado
        List<Seccion> secciones = repository.buscarSeccionesPorEstado(input.estado());

        // 2. Si no hay secciones, retornar null
        if (secciones.isEmpty()) {
            return null;
        }

        // 3. Crear un mapa para acceso rápido por código
        Map<String, Seccion> seccionPorCodigo = new HashMap<>();

        // Primera pasada: mapear todas las secciones por código
        for (Seccion seccion : secciones) {
            seccionPorCodigo.put(seccion.getCodigo(), seccion);
        }

        // Segunda pasada: construir las relaciones padre-hijo
        Seccion raiz = null;
        for (Seccion seccion : secciones) {
            if (seccion.getPadre() == null) {
                raiz = seccion;
            } else {
                Seccion padre = seccionPorCodigo.get(seccion.codigoSuperiorInmediato());
                if (padre != null && seccion != padre) {
                    padre.agregarHijo(seccion);
                }
            }
        }

        return raiz;
    }
}