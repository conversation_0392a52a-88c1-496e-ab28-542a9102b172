package co.com.gedsys.base.application.usecase.produccion.documental;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.usecase.produccion.documental.command.DesaprobarDocumentoCommand;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class DesaprobarDocumentoUseCase implements UseCase<DesaprobarDocumentoCommand, Void> {
    private final DocumentoRepository repository;

    @Override
    public Void execute(DesaprobarDocumentoCommand input) {
        var document = repository.findById(input.documentId())
                .orElseThrow(() -> new EntityNotExistsException(
                        "No se encontró el documento con el ID: " + input.documentId()));
        document.desaprobar(input.aprobador(), input.observaciones());
        repository.save(document);
        return null;
    }
}
