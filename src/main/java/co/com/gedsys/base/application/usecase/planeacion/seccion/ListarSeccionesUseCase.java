package co.com.gedsys.base.application.usecase.planeacion.seccion;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.domain.organizacion.EstadoSeccion;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionMapper;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class ListarSeccionesUseCase implements UseCase<ListarSeccionesCommand, List<SeccionDTO>> {

    private final SeccionRepository repository;
    private final SeccionMapper seccionMapper;

    public ListarSeccionesUseCase(
            SeccionRepository repository,
            SeccionMapper seccionMapper) {
        this.repository = repository;
        this.seccionMapper = seccionMapper;
    }

    @Override
    public List<SeccionDTO> execute(ListarSeccionesCommand input) {
        List<Seccion> secciones = repository.findAll();

        return secciones.stream()
                .filter(seccion -> filtrarPorNombre(seccion, input.nombre()))
                .filter(seccion -> filtrarPorEstado(seccion, input.estado()))
                .map(seccionMapper::toDTO)
                .collect(Collectors.toList());
    }

    private boolean filtrarPorNombre(Seccion seccion, String nombre) {
        return nombre == null || seccion.getNombre().toLowerCase().contains(nombre.toLowerCase());
    }

    private boolean filtrarPorEstado(Seccion seccion, EstadoSeccion estado) {
        return estado == null || seccion.getEstado() == estado;
    }
}
