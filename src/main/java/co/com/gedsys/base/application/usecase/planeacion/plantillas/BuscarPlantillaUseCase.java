package co.com.gedsys.base.application.usecase.planeacion.plantillas;

import java.util.UUID;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.PlantillaDTO;
import co.com.gedsys.base.application.mapper.PlantillasAppMapper;
import co.com.gedsys.base.domain.plantillas.PlantillaRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class BuscarPlantillaUseCase implements UseCase<UUID, PlantillaDTO> {
    private final PlantillaRepository repository;

    private final PlantillasAppMapper mapper;

    @Override
    public PlantillaDTO execute(UUID input) {
        return repository.findById(input)
                .map(mapper::toDto)
                .orElseThrow(() -> new EntityNotExistsException("Plantilla con id " + input + " no encontrada"));
    }
}