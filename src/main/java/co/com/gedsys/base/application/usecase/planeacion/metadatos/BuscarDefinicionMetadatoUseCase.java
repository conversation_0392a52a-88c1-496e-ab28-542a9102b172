package co.com.gedsys.base.application.usecase.planeacion.metadatos;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.DefinicionMetadatoDTO;
import co.com.gedsys.base.application.mapper.DefinicionMetadatosAppMapper;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BuscarDefinicionMetadatoUseCase implements UseCase<String, DefinicionMetadatoDTO> {
    private final DefinicionMetadatosRepository repository;

    private final DefinicionMetadatosAppMapper mapper;

    @Override
    public DefinicionMetadatoDTO execute(String input) {
        return repository.buscarPatron(input)
                .map(mapper::toDto)
                .orElseThrow(() -> new RuntimeException("Definicion no encontrada"));
    }
}
