package co.com.gedsys.base.application.usecase.planeacion.tipologia;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.TipoDocumentalDTO;
import co.com.gedsys.base.application.mapper.TiposDocumentalesAppMapper;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.UUID;

@RequiredArgsConstructor
@Service
public class IdentificarTipoDocumentalUseCase implements UseCase<UUID, TipoDocumentalDTO> {
    private final TipoDocumentalRepository repository;
    private final TiposDocumentalesAppMapper mapper;

    @Override
    public TipoDocumentalDTO execute(UUID identifier) {

        return repository.findById(identifier)
                .map(mapper::toDto)
                .orElseThrow(() -> new EntityNotExistsException("Tipo documental con id " + identifier + " no encontrado"));
    }
}
