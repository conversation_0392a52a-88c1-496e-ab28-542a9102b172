package co.com.gedsys.base.application.usecase.planeacion.seccion;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class EliminarUsuarioSeccionUseCase implements UseCase<EliminarUsuarioSeccionCommand, Void> {

    private final SeccionRepository repository;

    @Override
    public Void execute(EliminarUsuarioSeccionCommand input) {
        Seccion seccion = repository.findById(input.seccionId())
                .orElseThrow(() -> new EntityNotExistsException(
                        String.format("No existe una sección con el id '%s'", input.seccionId())));

        try {
            seccion.removerUsuario(input.username());
            repository.save(seccion);
        } catch (IllegalArgumentException e) {
            throw new EntityNotExistsException(
                    String.format("El usuario '%s' no existe en la sección", input.username()));
        }

        return null;
    }
}