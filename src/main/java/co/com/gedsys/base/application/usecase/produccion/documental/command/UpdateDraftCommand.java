package co.com.gedsys.base.application.usecase.produccion.documental.command;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.UUID;

public record UpdateDraftCommand(
        UUID documentId,
        String title,
        LinkedHashMap<String, String> metadatos,
        List<RegistroAnexo> anexos
) {
    public record RegistroAnexo(
            String nombre,
            String descripcion,
            String fileId,
            String hash,
            Long bytes,
            String extension
    ) {
    }
}
