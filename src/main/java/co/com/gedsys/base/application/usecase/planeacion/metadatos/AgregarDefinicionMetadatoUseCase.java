package co.com.gedsys.base.application.usecase.planeacion.metadatos;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.metadato.DefinicionMetadato;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AgregarDefinicionMetadatoUseCase implements UseCase<AgregarDefinicionMetadatoCommand, Void> {

    private final DefinicionMetadatosRepository repository;

    @Override
    public Void execute(AgregarDefinicionMetadatoCommand input) {
        runDuplicatedPolicy(input);
        var definicionMetadato = extractFromInput(input);
        repository.save(definicionMetadato);
        return null;
    }

    private DefinicionMetadato extractFromInput(AgregarDefinicionMetadatoCommand input) {
        return DefinicionMetadato.create(input.nombre(),
                        input.tipo(),
                        input.formato())
                .setRules(input.rules())
                .setDescripcion(input.descripcion());

    }

    private void runDuplicatedPolicy(String name) {
        if (repository.checkStock(name))
            throw new IllegalArgumentException("La definición de metadato ya existe");
    }

    private void runDuplicatedPolicy(AgregarDefinicionMetadatoCommand input) {
        if (repository.checkStockByPatronAndTipo(input.nombre(), input.tipo()))
            throw new IllegalArgumentException("Ya existe una definición de metadato con el mismo nombre y tipo");
    }
}
