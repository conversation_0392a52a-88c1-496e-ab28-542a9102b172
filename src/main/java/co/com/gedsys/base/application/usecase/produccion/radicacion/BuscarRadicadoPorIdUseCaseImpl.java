package co.com.gedsys.base.application.usecase.produccion.radicacion;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.mapper.RadicadoApplicationLayerMapper;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@RequiredArgsConstructor
public class BuscarRadicadoPorIdUseCaseImpl implements BuscarRadicadoPorIdUseCase {

    private final RadicadoRepository radicadoRepository;
    private final RadicadoApplicationLayerMapper map;

    @Transactional(readOnly = true)
    @Override
    public RadicadoDTO execute(UUID id) {
        Radicado radicado = radicadoRepository.findById(id)
                .orElseThrow(() -> new EntityNotExistsException("No se encontró el radicado con id " + id));

        Documento documento = radicado.getDocumento();
        if (documento == null) {
            throw new IllegalStateException("El radicado con id " + id + " no tiene un documento asociado, no se puede generar el DTO.");
        }

        return map.toDTO(radicado);
    }
} 