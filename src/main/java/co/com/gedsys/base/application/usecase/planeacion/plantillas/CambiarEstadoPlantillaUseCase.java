package co.com.gedsys.base.application.usecase.planeacion.plantillas;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.plantillas.PlantillaRepository;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class CambiarEstadoPlantillaUseCase implements UseCase<CambiarEstadoCommand, Void> {

    private final PlantillaRepository repository;

    @Override
    public Void execute(CambiarEstadoCommand input) {
        final var plantilla = repository
                .findById(input.id())
                .orElseThrow(() -> new EntityNotExistsException("Plantilla no encontrada"));
        switch (input.estado()) {
            case ACTIVA -> plantilla.activar();
            case INACTIVA -> plantilla.desactivar();
            default -> throw new IllegalArgumentException("El nuevo estado debe ser ACTIVA o INACTIVA");
        }
        repository.save(plantilla);
        return null;
    }
}
