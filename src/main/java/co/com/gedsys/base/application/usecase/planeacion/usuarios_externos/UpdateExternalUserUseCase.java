package co.com.gedsys.base.application.usecase.planeacion.usuarios_externos;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.ExternalUserDto;
import co.com.gedsys.base.application.mapper.ExternalUsersAppMapper;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.UpdateExternalUserCommand;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserIdentificationType;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class UpdateExternalUserUseCase implements UseCase<UpdateExternalUserCommand, ExternalUserDto> {
    private final ExternalUsersRepository externalUserRepository;
    private final ExternalUsersAppMapper mapper;

    @Transactional
    @Override
    public ExternalUserDto execute(UpdateExternalUserCommand input) {
        var externalUser = externalUserRepository.findById(input.id())
                .orElseThrow(() -> new EntityNotExistsException("No se encontró el usuario con id " + input.id()));

        // Normalizar entrada para tipo NA
        String normalizedNumber = normalizeIdentificationNumber(input.identificationType(), input.identificationNumber());

        // Crear comando normalizado para el mapper
        var normalizedCommand = new UpdateExternalUserCommand(
            input.id(),
            input.name(),
            input.salutation(),
            input.identificationType(),
            normalizedNumber,
            input.notes(),
            input.status(),
            input.properties()
        );

        // Aplicar cambios usando el mapper
        externalUser = mapper.toDomain(normalizedCommand, externalUser);

        // Validar unicidad si cambió información relevante
        validateUniquenessIfNeeded(externalUser, input);

        var updatedExternalUser = externalUserRepository.update(input.id(), externalUser);
        return mapper.toDto(updatedExternalUser);
    }

    private String normalizeIdentificationNumber(String type, String number) {
        return "NA".equals(type) ? null : number;
    }

    private void validateUniquenessIfNeeded(ExternalUser updatedUser, UpdateExternalUserCommand input) {
        // Solo validar si es tipo NA y cambió el nombre o el tipo
        if (updatedUser.getIdentificationType() == ExternalUserIdentificationType.NA) {
            // Buscar si existe otro usuario con el mismo nombre y tipo NA (excluyendo el actual)
            var existingUser = externalUserRepository.findByNameAndIdentificationType(
                updatedUser.getName(),
                ExternalUserIdentificationType.NA
            );

            if (existingUser.isPresent() && !existingUser.get().getId().equals(input.id())) {
                throw new EntityAlreadyExistsException(
                    String.format("Ya existe otro usuario con nombre '%s' y tipo NA", updatedUser.getName())
                );
            }
        }
        // Para otros tipos, la validación de unicidad se maneja en el dominio
    }
}
