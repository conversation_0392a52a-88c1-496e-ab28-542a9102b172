package co.com.gedsys.base.application.mapper;

import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.application.dto.UnidadProductoraOrganigramaDS;
import co.com.gedsys.base.domain.organizacion.Seccion;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface SeccionUseCaseMapper {

    @Mapping(target = "codigo", expression = "java(seccion.codigo())")
    UnidadProductoraOrganigramaDS toDS(Seccion seccion);

    SeccionDTO toDto(Seccion seccion);
}
