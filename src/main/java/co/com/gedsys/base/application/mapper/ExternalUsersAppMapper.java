package co.com.gedsys.base.application.mapper;

import co.com.gedsys.base.application.dto.ExternalUserDto;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.AddPropertiesExternalUserCommand;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.CreateExternalUserCommand;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.UpdateExternalUserCommand;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserProperty;
import org.mapstruct.*;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ExternalUsersAppMapper {
    ExternalUserDto toDto(ExternalUser domainEntity);

    @Mapping(target = "status", ignore = true)
    @Mapping(target = "id", ignore = true)
    ExternalUser toDomain(CreateExternalUserCommand request, @MappingTarget ExternalUser domainEntity);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "owner", ignore = true)
    ExternalUserProperty toDomain(CreateExternalUserCommand.ExternalUserPropertyRegistration domainEntity);

    @Mapping(target = "id", ignore = true)
    ExternalUser toDomain(UpdateExternalUserCommand request, @MappingTarget ExternalUser domainEntity);

    @Mapping(target = "owner", ignore = true)
    ExternalUserProperty toDomain(UpdateExternalUserCommand.ExternalUserPropertyRegistration domainEntity);

    @Mapping(target = "owner", ignore = true)
    @Mapping(target = "id", ignore = true)
    ExternalUserProperty toDomain(AddPropertiesExternalUserCommand.PropertyDS propertyDS);
}
