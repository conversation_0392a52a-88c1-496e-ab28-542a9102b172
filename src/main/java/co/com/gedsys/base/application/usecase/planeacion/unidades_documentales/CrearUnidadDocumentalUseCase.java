package co.com.gedsys.base.application.usecase.planeacion.unidades_documentales;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;

@Service
@RequiredArgsConstructor
public class CrearUnidadDocumentalUseCase implements UseCase<CrearUnidadDocumentalCommand, Void> {
    private final UnidadDocumentalRepository repository;
    private final ClasificacionDocumentalRepository clasificacionRepository;

    @Override
    public Void execute(CrearUnidadDocumentalCommand input) {
        boolean alreadyExist = repository.checkStock(input.nombre(), input.clasificacion());
        if (alreadyExist) {
            throw new EntityAlreadyExistsException("Ya existe una unidad documental con el mismo nombre" +
                    " con la unidadDocumentalId asignada");
        }
        var clasificacion = clasificacionRepository
                .findById(input.clasificacion())
                .orElseThrow(() -> new EntityNotExistsException("No existe la unidadDocumentalId seleccionada"));
        
        var unidadDocumental = new UnidadDocumental(input.nombre(), clasificacion);
        if (input.descripcion() != null) {
            unidadDocumental.setDescripcion(input.descripcion());
        }
        
        repository.save(unidadDocumental);
        return null;
    }
}
