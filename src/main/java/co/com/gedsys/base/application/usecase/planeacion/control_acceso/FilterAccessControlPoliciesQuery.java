package co.com.gedsys.base.application.usecase.planeacion.control_acceso;

import co.com.gedsys.base.domain.control_acceso.AccessControlRepository;
import co.com.gedsys.base.domain.control_acceso.AccessLevel;
import co.com.gedsys.base.domain.control_acceso.InterestGroup;

public record FilterAccessControlPoliciesQuery(
        AccessLevel accessLevel,
        String code,
        InterestGroup interestGroup,
        String detail
) implements AccessControlRepository.Searchable {
}