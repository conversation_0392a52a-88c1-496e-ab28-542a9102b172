package co.com.gedsys.base.application.usecase.produccion.radicacion;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.RadicadoDTO;

import java.util.UUID;

/**
 * Caso de uso para buscar un radicado por su ID.
 * Recibe el UUID del radicado y devuelve el RadicadoDTO asociado.
 */
public interface BuscarRadicadoPorIdUseCase extends UseCase<UUID, RadicadoDTO> {

} 