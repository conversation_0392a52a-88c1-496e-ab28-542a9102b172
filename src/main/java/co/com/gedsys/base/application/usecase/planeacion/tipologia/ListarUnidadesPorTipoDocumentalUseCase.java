package co.com.gedsys.base.application.usecase.planeacion.tipologia;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.UnidadDocumentalDto;
import co.com.gedsys.base.application.mapper.UnidadDocumentalMapper;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class ListarUnidadesPorTipoDocumentalUseCase
        implements UseCase<ListarUnidadesPorTipoDocumentalUseCase.Query, List<UnidadDocumentalDto>> {
    private final UnidadDocumentalRepository unidadDocumentalRepository;
    private final TipoDocumentalRepository tipoDocumentalRepository;
    private final UnidadDocumentalMapper map;

    @Override
    public List<UnidadDocumentalDto> execute(Query input) {
        var tipoDocumental = tipoDocumentalRepository.findById(input.tipoDocumentalId)
                .orElseThrow(() -> new EntityNotExistsException("Tipo Documental no encontrado"));
        var unidades = unidadDocumentalRepository.buscarParaTipoDocumental(tipoDocumental);
        return unidades.stream().map(map::toDto).toList();
    }

    public record Query(UUID tipoDocumentalId) {
    }
}
