package co.com.gedsys.base.application.usecase.planeacion.plantillas;

import java.util.List;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.PlantillaDTO;
import co.com.gedsys.base.application.mapper.PlantillasAppMapper;
import co.com.gedsys.base.domain.plantillas.PlantillaRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ListarPlantillasPorTipoDocumentalUseCase implements UseCase<List<String>, List<PlantillaDTO>> {
    private final PlantillaRepository repository;
    private final PlantillasAppMapper plantillasAppMapper;

    @Override
    public List<PlantillaDTO> execute(List<String> input) {
        return repository.findByTipoDocumental(input)
                .stream().map(plantillasAppMapper::toDto)
                .toList();
    }
}
