package co.com.gedsys.base.application.usecase.planeacion.seccion;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class MoverSeccionUseCase implements UseCase<MoverSeccionCommand, Seccion> {

    private final SeccionRepository repository;

    @Override
    public Seccion execute(MoverSeccionCommand input) {
        // 1. Validar existencia de la sección a mover
        var seccion = repository.findById(input.seccionId())
                .orElseThrow(() -> new EntityNotExistsException(
                        String.format("Sección con id '%s' no existe", input.seccionId())));

        // 2. Validar existencia del nuevo padre
        var nuevoPadre = repository.findById(input.nuevoPadreId())
                .orElseThrow(() -> new EntityNotExistsException(
                        String.format("Padre con id '%s' no existe", input.nuevoPadreId())));

        // 3. Verificar si es el nodo principal
        var nodoPrincipal = repository.buscarNodoPrincipalDelOrganigrama()
                .orElseThrow(() -> new IllegalStateException("No existe un nodo principal en el organigrama"));

        if (seccion.equals(nodoPrincipal)) {
            throw new IllegalArgumentException("No se puede mover el nodo principal del organigrama");
        }

        // 4. Crear una nueva sección con el nuevo código
        var seccionActualizada = new Seccion(input.nuevoCodigo(), seccion.getNombre());
        seccionActualizada.setResponsable(seccion.getResponsable());
        seccionActualizada.setPadre(nuevoPadre);

        return repository.save(seccionActualizada);
    }
}