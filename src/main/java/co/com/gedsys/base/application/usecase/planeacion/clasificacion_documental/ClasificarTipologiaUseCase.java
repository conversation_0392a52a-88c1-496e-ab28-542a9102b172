package co.com.gedsys.base.application.usecase.planeacion.clasificacion_documental;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.ClasificacionDS;
import co.com.gedsys.base.application.mapper.ClasificacionDocumentalMapper;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@RequiredArgsConstructor
@Service
public class ClasificarTipologiaUseCase implements UseCase<ClasificarTipologiaUseCase.Command, ClasificacionDS> {
    private final ClasificacionDocumentalRepository repository;
    private final TipoDocumentalRepository tipoDocumentalRepository;
    private final ClasificacionDocumentalMapper map;

    @Override
    public ClasificacionDS execute(Command input) {
        var clasificacion = repository
                .findById(input.clasificacionId())
                .orElseThrow(() -> new RuntimeException("Clasificación no encontrada"));
        var tiposDocumentales = tipoDocumentalRepository.findByIdIn(input.tiposDocumentales());
        clasificacion.setTiposDocumentales(tiposDocumentales);
        var updated = repository.save(clasificacion);
        return map.toDS(updated);
    }

    public record Command(UUID clasificacionId, List<UUID> tiposDocumentales) {
    }
}
