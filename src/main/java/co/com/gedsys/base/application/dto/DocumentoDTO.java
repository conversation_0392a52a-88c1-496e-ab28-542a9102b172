package co.com.gedsys.base.application.dto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public record DocumentoDTO(
        String id,
        String titulo,
        String autor,
        String fileId,
        String estado,
        TipoDocumentalDS tipoDocumental,
        UnidadDocumentalDS unidadDocumental,
        Set<MetadatoDS> metadatos,
        List<RadicadoDS> radicados,
        List<FirmaDS> firmas,
        List<AprobacionDS> aprobaciones,
        List<AnexoDS> anexos) {
    public record RadicadoDS(
            String id,
            Integer numero,
            String fechaExpedicion,
            String emisor,
            String prefijo,
            String sufijo,
            String tipo
    ) {
    }

    public record UnidadDocumentalDS(
            String id,
            String nombre,
            String descripcion,
            String codigoClasificacion
    ) {
    }

    public record TipoDocumentalDS(String id, String nombre, String estado) {
    }

    public record MetadatoDS(String id,
                             String patron,
                             String nombre,
                             String tipo,
                             String formato,
                             String valor) {
    }

    public record FirmaDS(
            UUID id,
            String firmante,
            String estado,
            LocalDateTime firmadoEn,
            Integer height,
            String observaciones,
            Integer page,
            Integer width,
            Integer x,
            Integer y
    ) {
    }

    public record AprobacionDS(
            UUID id,
            String aprobador,
            String observaciones,
            LocalDateTime aprobadoEn,
            String estado
    ) {
    }

    public record AnexoDS(
            UUID id,
            String nombre,
            String descripcion,
            String fileId,
            String hash,
            Long bytes,
            String extension
    ) {
    }
}
