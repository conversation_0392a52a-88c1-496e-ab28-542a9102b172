package co.com.gedsys.base.application.usecase.planeacion.unidades_documentales;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.mapper.UnidadDocumentalMapper;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ListarUnidadesDocumentalesUseCase implements UseCase<Void, List<ListarUnidadDocumentalQuery>> {
    private final UnidadDocumentalRepository unidadesDocumentalesRepository;
    private final UnidadDocumentalMapper mapper;

    @Override
    public List<ListarUnidadDocumentalQuery> execute(Void input) {
        return mapper.map(unidadesDocumentalesRepository.findAll());
    }
}
