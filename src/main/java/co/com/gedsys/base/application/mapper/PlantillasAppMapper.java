package co.com.gedsys.base.application.mapper;

import co.com.gedsys.base.application.dto.PlantillaDTO;
import co.com.gedsys.base.domain.plantillas.Plantilla;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
        uses = {DefinicionMetadatosAppMapper.class, TiposDocumentalesAppMapper.class},
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface PlantillasAppMapper {

    @Mapping(target = "titulo", expression = "java(plantilla.titulo())")
    @Mapping(target = "estado", expression = "java(plantilla.estado())")
    @Mapping(target = "producidoPorGedsys", expression = "java(plantilla.isProducidoPorGedsys())")
    PlantillaDTO toDto(Plantilla plantilla);
}