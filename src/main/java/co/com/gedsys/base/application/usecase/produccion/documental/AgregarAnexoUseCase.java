package co.com.gedsys.base.application.usecase.produccion.documental;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.application.mapper.DocumentoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.produccion.documental.command.AgregarAnexoCommand;
import co.com.gedsys.base.domain.documento.Anexo;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.documento.DocumentoDescartadoException;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.documento.EstadoDocumento;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
public class AgregarAnexoUseCase implements UseCase<AgregarAnexoCommand, DocumentoDTO> {

    private final DocumentoRepository documentoRepository;
    private final DocumentoApplicationLayerMapper mapper;

    @Override
    @Transactional
    public DocumentoDTO execute(AgregarAnexoCommand input) {
        Documento documento = documentoRepository.findById(input.documentId())
                .orElseThrow(() -> new EntityNotExistsException(
                        String.format("Documento con id '%s' no existe", input.documentId())));

        if (documento.getEstado() == EstadoDocumento.DESCARTADO) {
            throw new DocumentoDescartadoException();
        }

        Anexo anexo = Anexo.builder()
                .nombre(input.nombre())
                .descripcion(input.descripcion())
                .fileId(input.fileId())
                .hash(input.hash())
                .bytes(input.bytes())
                .extension(input.extension())
                .build();

        documento.agregarAnexo(anexo);

        var documentoActualizado = documentoRepository.save(documento);
        return mapper.toDTO(documentoActualizado);
    }
}
