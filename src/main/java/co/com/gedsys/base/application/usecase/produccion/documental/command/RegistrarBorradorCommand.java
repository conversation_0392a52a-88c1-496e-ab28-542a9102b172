package co.com.gedsys.base.application.usecase.produccion.documental.command;

import co.com.gedsys.base.application.dto.MetadatoDocumentoDTO;
import java.util.List;
import java.util.UUID;

public record RegistrarBorradorCommand(
        String titulo,
        String autor,
        String fileId,
        UUID tipoDocumentalId,
        UUID unidadDocumentalId,
        List<MetadatoDocumentoDTO> metadatos,
        List<FirmaUsuario> firmas,
        List<Aprobacion> aprobaciones,
        List<Anexo> anexos) {
    public record FirmaUsuario(
            String owner,
            int height,
            String observaciones,
            int page,
            int width,
            int x,
            int y
    ) {
    }

    public record Aprobacion(
            String aprobador
    ) {
    }

    public record Anexo(
            String nombre,
            String descripcion,
            String fileId,
            String hash,
            Long bytes,
            String extension
    ) {
    }
}
