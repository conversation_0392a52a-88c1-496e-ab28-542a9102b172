package co.com.gedsys.base.application.usecase.planeacion.control_acceso;

import co.com.gedsys.base.domain.control_acceso.AccessControlPermission;
import co.com.gedsys.base.domain.control_acceso.AccessLevel;
import co.com.gedsys.base.domain.control_acceso.InterestGroup;

import java.util.List;

public record RegisterAccessControlCommand(
        AccessLevel accessLevel,
        String code,
        String detail,
        InterestGroup interestGroup,
        List<AccessControlPermission> permissions,
        String notes
) {

}
