package co.com.gedsys.base.application.usecase.planeacion.plantillas;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.InvalidBusinessRuleException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.plantillas.Plantilla;
import co.com.gedsys.base.domain.plantillas.PlantillaRepository;
import co.com.gedsys.base.domain.plantillas.TipoPlantilla;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ActualizarPlantillaUseCase implements UseCase<ActualizarPlantillaCommand, Void> {
    private final DefinicionMetadatosRepository metadatoRepository;
    private final PlantillaRepository repository;
    private final TipoDocumentalRepository tipoDocumentalRepository;

    @Override
    public Void execute(ActualizarPlantillaCommand input) {
        var plantilla = retrieveTemplate(input);
        
        // Validar reglas de negocio antes de realizar cambios
        validateBusinessRules(plantilla, input);
        
        // Actualizar campos en orden seguro
        updateTitle(plantilla, input.titulo());
        updateTipoDocumental(plantilla, input.tipoDocumentalId());
        updateProducidoPorGedsys(plantilla, input.producidoPorGedsys());
        setMetadata(plantilla, input.metadatos());
        
        repository.save(plantilla);
        return null;
    }

    /**
     * Valida las reglas de negocio antes de realizar actualizaciones
     */
    private void validateBusinessRules(Plantilla plantilla, ActualizarPlantillaCommand input) {
        // Validar regla: producidoPorGedsys solo puede ser true en plantillas PRODUCCION
        if (Boolean.TRUE.equals(input.producidoPorGedsys())) {
            TipoPlantilla tipoFinal = determinarTipoPlantillaFinal(plantilla, input.tipoDocumentalId());
            if (tipoFinal != TipoPlantilla.PRODUCCION) {
                throw new InvalidBusinessRuleException(
                    "Solo las plantillas de tipo PRODUCCION pueden ser producidas por Gedsys"
                );
            }
        }
    }

    /**
     * Determina cuál será el tipo de plantilla final después de las actualizaciones
     */
    private TipoPlantilla determinarTipoPlantillaFinal(Plantilla plantilla, UUID nuevoTipoDocumentalId) {
        // Si no se está cambiando el tipo documental, usar el actual
        if (nuevoTipoDocumentalId == null) {
            return plantilla.getTipoPlantilla();
        }
        
        // Si se está cambiando, verificar si el nuevo tipo documental afecta el tipo de plantilla
        // Por ahora mantenemos el tipo actual, pero esto podría cambiar según reglas de negocio
        return plantilla.getTipoPlantilla();
    }

    private void updateProducidoPorGedsys(Plantilla plantilla, Boolean producidoPorGedsys) {
        if (producidoPorGedsys != null) {
            try {
                plantilla.setProducidoPorGedsys(producidoPorGedsys);
            } catch (IllegalStateException e) {
                // Convertir excepción de dominio a excepción de aplicación
                throw new InvalidBusinessRuleException(
                    "No se puede establecer 'producidoPorGedsys' como true en plantillas que no son de tipo PRODUCCION",
                    e
                );
            }
        }
    }

    private void setMetadata(Plantilla plantilla, List<String> patterns) {
        if (patterns == null)
            return;
        var definiciones = metadatoRepository.buscarPorPatrones(patterns);
        plantilla.setEsquemaDeMetadatos(definiciones);
    }

    private Plantilla retrieveTemplate(ActualizarPlantillaCommand input) {
        return repository.findById(input.plantillaId())
                .orElseThrow(() -> new EntityNotExistsException(
                        "No se ha encontrado la plantilla con Id " + input.plantillaId()));
    }

    private void updateTipoDocumental(Plantilla plantilla, UUID tipoDocumentalId) {
        Optional.ofNullable(tipoDocumentalId)
                .flatMap(tipoDocumentalRepository::findById)
                .ifPresent(tipoDocumental -> {
                    plantilla.setTipoDocumental(tipoDocumental);
                    // Validar consistencia después de cambiar tipo documental
                    validateConsistenciaPostCambioTipoDocumental(plantilla);
                });
    }

    /**
     * Valida la consistencia después de cambiar el tipo documental
     */
    private void validateConsistenciaPostCambioTipoDocumental(Plantilla plantilla) {
        // Si la plantilla tiene producidoPorGedsys=true pero ya no es PRODUCCION,
        // automáticamente establecer a false para mantener consistencia
        if (plantilla.isProducidoPorGedsys() && plantilla.getTipoPlantilla() != TipoPlantilla.PRODUCCION) {
            plantilla.setProducidoPorGedsys(false);
        }
    }

    private void updateTitle(Plantilla plantilla, String title) {
        Optional.ofNullable(title)
                .filter(t -> !t.isEmpty())
                .filter(this::isNotDuplicated)
                .ifPresent(plantilla::setTitulo);
    }

    private boolean isNotDuplicated(String title) {
        boolean alreadyAssigned = repository.checkStock(title);
        if (alreadyAssigned)
            throw new EntityAlreadyExistsException("El título de la plantilla ya se encuentra en uso.");
        return true;
    }

}
