package co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.entidad_territorial.EntidadesTerritorialesRepository;

@RequiredArgsConstructor
@Service
public class EliminarEntidadTerritorialUseCase implements UseCase<Integer, Void> {
    private final EntidadesTerritorialesRepository repository;

    @Override
    public Void execute(Integer input) {
        repository.delete(input);
        return null;
    }
}
