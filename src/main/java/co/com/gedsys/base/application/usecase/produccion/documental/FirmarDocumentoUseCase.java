package co.com.gedsys.base.application.usecase.produccion.documental;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.mapper.DocumentoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.produccion.documental.command.FirmarDocumentoCommand;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class FirmarDocumentoUseCase implements UseCase<FirmarDocumentoCommand, Void> {
    private final DocumentoRepository documentRepository;
    private final DocumentoApplicationLayerMapper mapper;

    @Override
    public Void execute(FirmarDocumentoCommand input) {
        var document = documentRepository.findById(input.documentId())
                .orElseThrow(() -> new EntityNotExistsException("Documento", input.documentId()));
        document.firmar(input.firmante());
        documentRepository.save(document);
        return null;
    }
}
