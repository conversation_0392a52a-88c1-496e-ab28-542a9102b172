package co.com.gedsys.base.application.usecase.planeacion.seccion;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.application.mapper.SeccionUseCaseMapper;
import co.com.gedsys.base.domain.organizacion.EstadoSeccion;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionConHijasActivasException;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RequiredArgsConstructor
@Service
public class InactivarSeccionUseCase implements UseCase<InactivarSeccionCommand, SeccionDTO> {

    private final SeccionRepository repository;
    private final SeccionUseCaseMapper mapper;

    @Override
    @Transactional
    public SeccionDTO execute(InactivarSeccionCommand input) {
        Seccion seccion = repository.findById(input.seccionId())
                .orElseThrow(() -> new EntityNotExistsException(
                        String.format("Sección con id '%s' no existe", input.seccionId())));

        // Validación jerárquica: verificar si tiene hijos activos
        if (repository.tieneHijasActivas(seccion.getId())) {
            List<Seccion> hijasActivas = repository.buscarHijasConEstado(
                    seccion.getId(), EstadoSeccion.ACTIVA);
            List<String> nombres = hijasActivas.stream()
                    .map(Seccion::getNombre)
                    .toList();
            throw new SeccionConHijasActivasException(seccion.getNombre(), nombres);
        }

        // Validación de estado local
        seccion.validarInactivacionPermitida();
        seccion.setEstado(EstadoSeccion.INACTIVA);

        var seccionInactivada = repository.save(seccion);
        return mapper.toDto(seccionInactivada);
    }
}
