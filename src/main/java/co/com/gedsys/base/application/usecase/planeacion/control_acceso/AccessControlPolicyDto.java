package co.com.gedsys.base.application.usecase.planeacion.control_acceso;

import co.com.gedsys.base.domain.control_acceso.AccessControlPermission;
import co.com.gedsys.base.domain.control_acceso.AccessLevel;
import co.com.gedsys.base.domain.control_acceso.InterestGroup;

import java.util.Set;
import java.util.UUID;

public record AccessControlPolicyDto(
        UUID id,
        AccessLevel accessLevel,
        String code,
        InterestGroup interestGroup,
        String detail,
        String notes,
        Set<AccessControlPermission> permissions
) {
}
