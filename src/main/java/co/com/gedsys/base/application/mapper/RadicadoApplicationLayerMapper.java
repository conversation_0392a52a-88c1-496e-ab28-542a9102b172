package co.com.gedsys.base.application.mapper;

import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.UsuarioSeccion;
import co.com.gedsys.base.domain.radicado.PropiedadesRadicado;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", unmappedTargetPolicy = org.mapstruct.ReportingPolicy.ERROR)
public interface RadicadoApplicationLayerMapper {

    RadicadoDTO toDTO(Radicado radicado);

    RadicadoDTO.PropiedadesRadicadoDS toDS(PropiedadesRadicado propiedadesRadicado);

    @Mapping(target = "tipoDocumentalId", ignore = true)//todo: mapear tipo documental luego de cambiar la implementacion
    @Mapping(target = "tipo", source = "tipoConsecutivo")
    RadicadoDTO.ConsecutivoDS toDS(Consecutivo consecutivo);

    @Mapping(target = "unidadDocumentalId", source = "unidadDocumental.id")
    @Mapping(target = "tipoDocumentalId", source = "tipoDocumental.id")
    RadicadoDTO.DocumentoDS toDS(Documento documento);

    @Mapping(target = "status", source = "status")
    RadicadoDTO.ExternalUserDS toDS(ExternalUser externalUser);

    @Mapping(target = "codigo", expression = "java(seccion.getCodigo())")
    @Mapping(target = "tipo", source = "tipo")
    @Mapping(target = "estado", source = "estado")
    RadicadoDTO.SeccionDS toDS(Seccion seccion);

    @Mapping(target = "username", expression = "java(usuarioSeccion.getUsername())")
    @Mapping(target = "relacion", source = "relacion")
    @Mapping(target = "seccion", source = "seccion")
    RadicadoDTO.UsuarioSeccionDS toDS(UsuarioSeccion usuarioSeccion);
}
