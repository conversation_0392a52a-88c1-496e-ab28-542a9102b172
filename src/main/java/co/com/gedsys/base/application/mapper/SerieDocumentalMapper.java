package co.com.gedsys.base.application.mapper;

import co.com.gedsys.base.application.dto.SerieDocumentalDto;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR
)
public interface SerieDocumentalMapper {

    @Mapping(target = "codigo", expression = "java(serie.codigo())")
    @Mapping(target = "tipo", expression = "java(serie.getTipo().name())")
    @Mapping(target = "estado", expression = "java(serie.getEstado().name())")
    @Mapping(target = "codigoSeriePadre", expression = "java(serie.codigoSeriePadre())")
    @Mapping(target = "nombreSeriePadre", expression = "java(serie.nombreSeriePadre())")
    SerieDocumentalDto toDto(SerieDocumental serie);
}