package co.com.gedsys.base.application.usecase.produccion.documental;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.usecase.produccion.documental.command.RechazarFirmaCommand;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class RechazarFirmaUseCase implements UseCase<RechazarFirmaCommand, Void> {

    private final DocumentoRepository documentRepository;

    @Override
    public Void execute(RechazarFirmaCommand input) {
        var documento = documentRepository.findById(input.documentId())
                .orElseThrow(
                        () -> new RuntimeException("No se encontró el documento con el ID: " + input.documentId()));
        documento.rechazarFirma(input.firmante(), input.observaciones());
        documentRepository.save(documento);
        return null;
    }
}
