package co.com.gedsys.base.application.usecase.planeacion.control_acceso;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.control_acceso.AccessControlRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class FilterAccessControlPoliciesUseCase implements UseCase<FilterAccessControlPoliciesQuery, List<AccessControlPolicyDto>> {

    private final AccessControlRepository repository;

    private final AccessControlPlanningUseCaseDataMapper mapper;

    @Override
    public List<AccessControlPolicyDto> execute(FilterAccessControlPoliciesQuery input) {
        return repository.search(input)
                .stream()
                .map(mapper::toDto)
                .toList();
    }
}
