package co.com.gedsys.base.application.usecase.planeacion.seccion;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class EliminarSeccionUseCase implements UseCase<EliminarSeccionCommand, Void> {
    private final SeccionRepository repository;

    @Override
    public Void execute(EliminarSeccionCommand input) {
        var id = input.id();
        repository.findById(id)
                .orElseThrow(() -> new EntityNotExistsException(Seccion.class.getSimpleName(), id));

        if (repository.tieneHijos(id)) {
            throw new EliminarSeccionException("No se puede eliminar sección con elementos hijos");
        }

        if (repository.tieneUsuarios(id)) {
            throw new EliminarSeccionException("No se puede eliminar sección con usuarios");
        }

        repository.delete(id);
        return null;
    }
}
