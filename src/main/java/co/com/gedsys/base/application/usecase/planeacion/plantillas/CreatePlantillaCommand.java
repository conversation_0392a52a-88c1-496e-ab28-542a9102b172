package co.com.gedsys.base.application.usecase.planeacion.plantillas;

import co.com.gedsys.base.domain.plantillas.TipoPlantilla;

public record CreatePlantillaCommand(String titulo, TipoPlantilla tipo, boolean producidoPorGedsys) {
    public CreatePlantillaCommand {
        // Validar que si producidoPorGedsys es true, el tipo debe ser PRODUCCION
        if (producidoPorGedsys && tipo != TipoPlantilla.PRODUCCION) {
            throw new IllegalArgumentException("producidoPorGedsys solo puede ser true para plantillas de tipo PRODUCCION");
        }
    }
}
