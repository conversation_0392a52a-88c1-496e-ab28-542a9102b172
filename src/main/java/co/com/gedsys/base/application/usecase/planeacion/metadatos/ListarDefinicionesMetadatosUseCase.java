package co.com.gedsys.base.application.usecase.planeacion.metadatos;

import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.DefinicionMetadatoDTO;
import co.com.gedsys.base.application.mapper.DefinicionMetadatosAppMapper;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ListarDefinicionesMetadatosUseCase implements UseCase<Void, Set<DefinicionMetadatoDTO>> {
    private final DefinicionMetadatosRepository repository;
    private final DefinicionMetadatosAppMapper mapper;

    @Override
    public Set<DefinicionMetadatoDTO> execute(Void input) {
        return repository.findAll()
                .stream().map(mapper::toDto).collect(Collectors.toSet());
    }
}
