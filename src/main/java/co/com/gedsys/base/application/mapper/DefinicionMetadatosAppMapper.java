package co.com.gedsys.base.application.mapper;

import co.com.gedsys.base.application.dto.DefinicionMetadatoDTO;
import co.com.gedsys.base.domain.metadato.DefinicionMetadato;
import org.mapstruct.Mapper;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

import java.util.Collection;
import java.util.Set;

@Mapper(componentModel = "spring")
@MapperConfig(unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface DefinicionMetadatosAppMapper {

    DefinicionMetadatoDTO toDto(DefinicionMetadato domainEntity);

    Set<DefinicionMetadatoDTO> toDtoSet(Collection<DefinicionMetadato> domainCollection);
}