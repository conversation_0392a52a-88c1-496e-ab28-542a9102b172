package co.com.gedsys.base.application.usecase.gestion_tramite;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.application.mapper.DocumentoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.gestion_tramite.datastructure.BuscarDocumentoQuery;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class BuscarDocumentoUseCase implements UseCase<BuscarDocumentoQuery, DocumentoDTO> {
    private final DocumentoRepository documentoRepository;
    private final DocumentoApplicationLayerMapper mapper;

    public DocumentoDTO execute(BuscarDocumentoQuery command) {
        var documento = documentoRepository.findById(command.documentoId())
                .orElseThrow(() -> new EntityNotExistsException("Documento no encontrado"));
        return mapper.toDTO(documento);
    }
}