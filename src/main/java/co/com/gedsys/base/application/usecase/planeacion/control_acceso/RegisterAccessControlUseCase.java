package co.com.gedsys.base.application.usecase.planeacion.control_acceso;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.domain.control_acceso.AccessControlRepository;
import co.com.gedsys.base.domain.control_acceso.AccessLevel;
import co.com.gedsys.base.domain.control_acceso.InterestGroup;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import co.com.gedsys.base.domain.serie_documental.SerieDocumentalRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@AllArgsConstructor
public class RegisterAccessControlUseCase implements UseCase<RegisterAccessControlCommand, AccessControlPolicyDto> {

    private final AccessControlPlanningUseCaseDataMapper map;

    private final AccessControlRepository repository;

    private final SeccionRepository seccionRepository;

    private final SerieDocumentalRepository serieDocumentalRepository;

    @Transactional
    @Override
    public AccessControlPolicyDto execute(RegisterAccessControlCommand input) {
        validatePolicyExistence(input);
        verifyAccessLevelExistence(input.accessLevel(), input.code());
        verifyDetailExists(input.interestGroup(), input.detail());
        var accessControlPolicy = map.toDomain(input);
        var result = repository.save(accessControlPolicy);

        return map.toDto(result);
    }

    private void validatePolicyExistence(RegisterAccessControlCommand input) {
        var foundPolicy = repository.find(input.accessLevel(), input.code(), input.interestGroup(), input.detail());
        if (foundPolicy.isPresent()) {
            throw new EntityAlreadyExistsException("Access Control Policy already exists, update or delete it first.");
        }
    }

    private void verifyDetailExists(InterestGroup interestGroup, String detail) {
        switch (interestGroup) {
            case USUARIO_INTERNO -> throw new UnsupportedOperationException("Not implemented");
            case SECCIONES -> seccionRepository.findById(UUID.fromString(detail));
        }
    }

    private void verifyAccessLevelExistence(AccessLevel accessLevel, String code) {
        var found = switch (accessLevel) {
            case SECCION -> seccionRepository.findByCode(code);
            case SERIE -> serieDocumentalRepository.findByCode(code);
        };

        if (found.isEmpty()) {
            throw new EntityNotExistsException("Access Level " + accessLevel.name() + " and code " + code +
                    " does not exist. Cannot create Access Control Policy.");
        }
    }

}
