package co.com.gedsys.base.application.usecase.planeacion.consecutivos;

import java.util.List;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.ConsecutivoDTO;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ListarConsecutivosUseCase implements UseCase<Void, List<ConsecutivoDTO>> {
    private final ConsecutivoRepository repository;
    private final ConsecutivoMapper mapper;

    @Override
    public List<ConsecutivoDTO> execute(Void input) {
        return repository.findAll()
                .stream()
                .map(mapper::toDto)
                .toList();
    }
}
