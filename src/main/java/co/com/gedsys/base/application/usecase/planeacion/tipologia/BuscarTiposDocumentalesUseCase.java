package co.com.gedsys.base.application.usecase.planeacion.tipologia;


import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.TipoDocumentalDTO;
import co.com.gedsys.base.application.mapper.TiposDocumentalesAppMapper;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class BuscarTiposDocumentalesUseCase implements UseCase<BuscarTiposDocumentalesQuery, List<TipoDocumentalDTO>> {
    private final TipoDocumentalRepository tipoDocumentalRepository;
    private final TiposDocumentalesAppMapper mapper;

    @Override
    public List<TipoDocumentalDTO> execute(BuscarTiposDocumentalesQuery input) {
        var tiposDocumentalesEncontrados = tipoDocumentalRepository.filtrarPorEstado(input.estado());
        return tiposDocumentalesEncontrados.stream().map(mapper::toDto).toList();
    }
}
