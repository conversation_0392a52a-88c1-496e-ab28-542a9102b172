package co.com.gedsys.base.application.usecase.gestion_tramite;

import java.util.List;

import org.springframework.stereotype.Service;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.application.mapper.DocumentoApplicationLayerMapper;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.documento.ParametrosBusquedaDocumento;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class BusquedaPorParametrosUseCase implements UseCase<BusquedaPorParametrosQuery, List<DocumentoDTO>> {

    private final DocumentoRepository documentoRepository;
    private final DocumentoApplicationLayerMapper documentoApplicationLayerMapper;

    @Override
    public List<DocumentoDTO> execute(BusquedaPorParametrosQuery input) {
        var parameters = new ParametrosBusquedaDocumento(input.unidadDocumentalId());
        return documentoRepository.buscarPorParametros(parameters)
                .stream().map(documentoApplicationLayerMapper::toDTO).toList();
    }
}
