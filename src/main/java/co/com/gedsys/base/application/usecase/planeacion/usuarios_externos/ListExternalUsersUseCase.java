package co.com.gedsys.base.application.usecase.planeacion.usuarios_externos;

import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.ExternalUserDto;
import co.com.gedsys.base.application.mapper.ExternalUsersAppMapper;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ListExternalUsersUseCase implements UseCase<Void, List<ExternalUserDto>> {
    private final ExternalUsersAppMapper mapper;
    private final ExternalUsersRepository externalUsersRepository;

    @Override
    public List<ExternalUserDto> execute(Void input) {
        return externalUsersRepository.findAll().stream().map(mapper::toDto).toList();
    }
}
