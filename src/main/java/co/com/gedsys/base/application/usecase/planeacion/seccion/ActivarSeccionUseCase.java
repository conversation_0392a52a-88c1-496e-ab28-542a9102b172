package co.com.gedsys.base.application.usecase.planeacion.seccion;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.UseCase;
import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.application.mapper.SeccionUseCaseMapper;
import co.com.gedsys.base.domain.organizacion.EstadoSeccion;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionPadreInactivaException;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
public class ActivarSeccionUseCase implements UseCase<ActivarSeccionCommand, SeccionDTO> {

    private final SeccionRepository repository;
    private final SeccionUseCaseMapper mapper;

    @Override
    @Transactional
    public SeccionDTO execute(ActivarSeccionCommand input) {
        // Obtener la sección
        Seccion seccion = repository.findById(input.seccionId())
                .orElseThrow(() -> new EntityNotExistsException(
                        String.format("Sección con id '%s' no existe", input.seccionId())));

        // Validación jerárquica: verificar si el padre está inactivo
        if (repository.tienePadreInactivo(seccion.getId())) {
            // Cargar padre para obtener el nombre en la excepción
            Seccion padre = repository.findById(seccion.getPadre().getId())
                    .orElseThrow(() -> new EntityNotExistsException("Sección padre no encontrada"));
            throw new SeccionPadreInactivaException(seccion.getNombre(), padre.getNombre());
        }

        // Validación de estado local y responsable
        seccion.validarActivacionPermitida();
        seccion.setEstado(EstadoSeccion.ACTIVA);

        // Persistir la sección activada
        var seccionActivada = repository.save(seccion);
        return mapper.toDto(seccionActivada);
    }
}
