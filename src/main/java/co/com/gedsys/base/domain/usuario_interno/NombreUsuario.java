package co.com.gedsys.base.domain.usuario_interno;

import co.com.gedsys.base.domain.common.StringValueObject;

public class NombreUsuario extends StringValueObject {
    public NombreUsuario(String value) {
        super(value);
    }

    @Override
    protected String validate(String value) {
        if (value == null) return null;
        if (value.matches("[a-zA-Z0-9.]+")) {
            return value.toLowerCase();
        } else {
            throw new NombreDeUsuarioInvalidoException();
        }
    }
}
