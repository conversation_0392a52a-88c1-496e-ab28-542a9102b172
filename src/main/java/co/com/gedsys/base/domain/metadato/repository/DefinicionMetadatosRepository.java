package co.com.gedsys.base.domain.metadato.repository;

import co.com.gedsys.base.domain.common.DomainRepository;
import co.com.gedsys.base.domain.metadato.DefinicionMetadato;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

public interface DefinicionMetadatosRepository extends DomainRepository<DefinicionMetadato, UUID> {

    Set<DefinicionMetadato> buscarPorPatrones(List<String> patrones);

    Optional<DefinicionMetadato> buscarPatron(String input);

    boolean checkStockByPatronAndTipo(String patron, TipoMetadatoEnum tipo);
}
