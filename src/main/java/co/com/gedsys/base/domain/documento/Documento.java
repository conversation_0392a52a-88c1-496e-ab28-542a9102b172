package co.com.gedsys.base.domain.documento;

import co.com.gedsys.base.domain.common.Default;
import co.com.gedsys.base.domain.metadato.Metadato;
import co.com.gedsys.base.domain.radicado.Radicado;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import co.com.gedsys.base.domain.usuario_interno.NombreUsuario;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.*;

import static co.com.gedsys.base.domain.documento.EstadoDocumento.BORRADOR;

@Getter
public class Documento {
    private UUID id;
    @Setter(AccessLevel.NONE)
    private TituloDocumento titulo;
    private String fileId;
    private EstadoDocumento estado;
    private TipoDocumental tipoDocumental;
    private UnidadDocumental unidadDocumental;
    private NombreUsuario autor;
    @Setter(AccessLevel.NONE)
    private Set<Metadato> metadatos;
    private final List<Radicado> radicados = new ArrayList<>();
    private final List<FirmaUsuario> firmas = new ArrayList<>();
    private final Set<Aprobacion> aprobaciones = new HashSet<>();
    private final Set<Anexo> anexos = new HashSet<>();

    @Default
    public Documento(UUID id,
                     String titulo,
                     String fileId,
                     EstadoDocumento estado,
                     TipoDocumental tipoDocumental,
                     UnidadDocumental unidadDocumental,
                     String autor,
                     Set<Metadato> metadatos,
                     List<Radicado> radicados,
                     List<FirmaUsuario> firmas,
                     List<Aprobacion> aprobaciones, List<Anexo> anexos) {
        this.id = id;
        this.titulo = new TituloDocumento(titulo);
        this.fileId = fileId;
        this.estado = estado;
        this.tipoDocumental = tipoDocumental;
        this.unidadDocumental = unidadDocumental;
        this.autor = new NombreUsuario(autor);
        this.metadatos = new HashSet<>();
        this.radicados.addAll(radicados);
        this.firmas.addAll(firmas);
        setMetadatos(metadatos);
        setRadicados(radicados);
        Optional.ofNullable(aprobaciones).ifPresentOrElse(this.aprobaciones::addAll, ArrayList::new);
        setAnexos(anexos);
    }

    public void setAnexos(List<Anexo> anexos) {
        if (anexos == null) return;
        anexos.forEach(this::agregarAnexo);
    }

    public Documento(String titulo,
                     String fileId,
                     TipoDocumental tipoDocumental,
                     UnidadDocumental unidadDocumental,
                     String autor,
                     Set<Metadato> metadatos,
                     List<FirmaUsuario> firmas,
                     List<Aprobacion> aprobaciones, List<Anexo> anexos) {
        this(null, titulo, fileId, BORRADOR, tipoDocumental, unidadDocumental,
                autor, metadatos, List.of(), firmas, aprobaciones, anexos);
    }

    public String getTitulo() {
        return titulo.getValue();
    }

    public void setTitulo(String nuevoTitulo) {
        titulo = new TituloDocumento(nuevoTitulo);
    }

    public void setMetadatos(Collection<Metadato> metadatos) {
        if (metadatos == null) return;
        this.metadatos.addAll(metadatos);
    }

    public void setRadicados(List<Radicado> radicados) {
        if (radicados == null) return;
        this.radicados.addAll(radicados);
    }

    public void setAprobaciones(List<Aprobacion> aprobaciones) {
        if (aprobaciones == null || aprobaciones.isEmpty()) return;
        if (EstadoDocumento.BORRADOR != estado) {
            throw new AprobacionException("Sólo se pueden agregar aprobaciones a un Documento en construcción");
        }
        this.aprobaciones.addAll(aprobaciones);
    }

    public UUID getClasificacionDocumentalId() {
        return unidadDocumental.getclasificacionId();
    }

    public String getAutor() {
        return autor.getValue();
    }

    public UUID getTipoDocumentalId() {
        return tipoDocumental.getId();
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public void agregarAnexo(Anexo anexo) {
        if (anexo == null) return;
        this.anexos.add(anexo);
    }

    public FirmaUsuario getFirma(String firmante) {
        return firmas.stream()
                .filter(f -> f.getFirmante().equals(firmante))
                .findFirst()
                .orElseThrow(() -> new FirmaDocumentoNoExistenteException("No existe una firma pendiente para el usuario: " + firmante));
    }

    public void firmar(String firmante) {
        validarEstadoParaFirma();
        FirmaUsuario firma = getFirma(firmante);
        validarFirmaNoCompletada(firma);
        completarFirma(firma);
        actualizarEstadoDocumentoSiTodasFirmasCompletadas();
    }

    private void validarEstadoParaFirma() {
        boolean requireAprobacion = !aprobaciones.isEmpty();

        if (requireAprobacion && !estaAprobado())
            throw new FirmaDocumentoException("Solo se pueden firmar documentos aprobados");
    }

    private void validarFirmaNoCompletada(FirmaUsuario firma) {
        if (firma.completada()) {
            throw new FirmaDocumentoException("El documento ya ha sido firmado");
        }
    }

    private void completarFirma(FirmaUsuario firma) {
        firma.setFirmadoEn(LocalDateTime.now());
        firma.setEstado(EstadoDeLaFirma.COMPLETADA);
    }

    private void actualizarEstadoDocumentoSiTodasFirmasCompletadas() {
        // Ya no se cambia el estado automáticamente al firmar
        // El estado se maneja por transiciones explícitas
    }

    public boolean estaFirmado() {
        return !firmas.isEmpty() && firmas.stream().allMatch(FirmaUsuario::completada);
    }

    public void rechazarFirma(String firmante, String observacion) {
        validarEstadoParaFirma();
        if (fueRechazado())
            throw new RechazoFirmaDocumentoException("El documento ya fue rechazado");
        if (observacion == null || observacion.isBlank())
            throw new RechazoFirmaDocumentoException("Debe especificar una observación");
        if (estaFirmado()) {
            throw new RechazoFirmaDocumentoException("No se puede rechazar documento ya firmado");
        }
        var firma = getFirma(firmante);
        firma.setEstado(EstadoDeLaFirma.RECHAZADA);
        firma.setObservaciones(observacion);
        aprobaciones.forEach(Aprobacion::reset);
    }

    public boolean fueRechazado() {
        return firmas.stream().anyMatch(FirmaUsuario::esRechazada) ||
               aprobaciones.stream().anyMatch(Aprobacion::fueRechazada);
    }

    public void aprobar(String aprobador) {
        validateDocumentCanBeApproved();
        var aprobacion = getAprobacion(aprobador);
        aprobacion.aprobar(aprobador);
        validateDocumentApproved();
    }

    private Aprobacion getAprobacion(String aprobador) {
        return aprobaciones.stream().filter(a -> a.getAprobador().equals(aprobador)).findFirst()
                .orElseThrow(() ->
                        new AprobacionException("No existe una aprobación pendiente para el usuario: " + aprobador));
    }

    public boolean estaAprobado() {
        return !aprobaciones.isEmpty() && aprobaciones.stream().allMatch(Aprobacion::fueAprobada);
    }

    public void desaprobar(String aprobador, String observacion) {
        validateDocumentCanBeApproved();
        if (observacion == null || observacion.isBlank())
            throw new AprobacionException("Debe especificar una observación para desaprobar la aprobación");
        var aprobacion = getAprobacion(aprobador);
        aprobacion.desaprueba(aprobador, observacion);
        validateDocumentApproved();
    }

    private void validateDocumentApproved() {
        // Ya no se cambia el estado automáticamente al aprobar/desaprobar
        // El estado se maneja por transiciones explícitas
    }

    private void validateDocumentCanBeApproved() {
        if (EstadoDocumento.BORRADOR != estado) {
            throw new AprobacionException("Solo se pueden aprobar documentos en construcción");
        }
    }
}