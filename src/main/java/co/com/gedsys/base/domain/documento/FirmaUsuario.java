package co.com.gedsys.base.domain.documento;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.UUID;

@Accessors(chain = true)
@Builder
@Getter
@Setter
public class FirmaUsuario {
    private UUID id;
    @Builder.Default
    private EstadoDeLaFirma estado = EstadoDeLaFirma.PENDIENTE;
    private LocalDateTime firmadoEn;
    private String firmante; //todo: Deberia ser un ValueObject para validar el firmante
    private Integer height;
    private String observaciones;
    private Integer page;
    private Integer width;
    private Integer x;
    private Integer y;

    @Builder
    public FirmaUsuario(UUID id,
                        EstadoDeLaFirma estado,
                        LocalDateTime firmadoEn,
                        String firmante,
                        Integer height, String observaciones,
                        Integer page,
                        Integer width,
                        Integer x,
                        Integer y) {
        validatePositiveValue(x, "x");
        validatePositiveValue(y, "y");
        validatePositiveValue(width, "width");
        validatePositiveValue(height, "height");
        validatePositiveValue(page, "page");

        this.id = id;
        this.estado = estado == null ? EstadoDeLaFirma.PENDIENTE : estado;
        this.firmadoEn = firmadoEn;
        this.firmante = firmante;
        this.height = height;
        this.observaciones = observaciones;
        this.page = page;
        this.width = width;
        this.x = x;
        this.y = y;
    }

    private void validatePositiveValue(Integer value, String fieldName) {
        if (value != null && value < 0) {
            throw new IllegalArgumentException(fieldName + " must be greater than or equal to 0");
        }
    }

    public boolean completada() {
        return EstadoDeLaFirma.COMPLETADA == estado;
    }

    public boolean esRechazada() {
        return EstadoDeLaFirma.RECHAZADA == estado;
    }
}