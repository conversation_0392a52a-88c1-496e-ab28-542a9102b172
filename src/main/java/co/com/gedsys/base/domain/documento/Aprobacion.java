package co.com.gedsys.base.domain.documento;


import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import co.com.gedsys.base.domain.usuario_interno.NombreUsuario;

@Getter
@Setter
@Accessors(chain = true)
@EqualsAndHashCode(of = {"aprobador"})
public class Aprobacion {
    private UUID id;
    private NombreUsuario aprobador;
    private String observaciones;
    private LocalDateTime aprobadoEn;
    private ResultadoAprobacion estado;

    public Aprobacion(String aprobador) {
        setAprobador(aprobador);
        this.estado = ResultadoAprobacion.PENDIENTE;
    }

    public boolean estaPendiente() {
        return ResultadoAprobacion.PENDIENTE == estado;
    }

    public void rechazar(String observaciones) {
        if (observaciones == null || observaciones.isBlank()) {
            throw new AprobacionException("Debe especificar una observación para rechazar la aprobación");
        }
        estado = ResultadoAprobacion.RECHAZADA;
    }

    public boolean fueRechazada() {
        return ResultadoAprobacion.RECHAZADA == estado;
    }

    public Aprobacion setAprobador(String nombre) {
        if (nombre == null || nombre.isBlank()) {
            throw new AprobacionException("Se requiere un aprobador");
        }
        this.aprobador = new NombreUsuario(nombre);
        return this;
    }

    public String getAprobador() {
        return aprobador.getValue();
    }

    public void aprobar(String aprobador) {
        if (!esAprobador(aprobador)) {
            throw new AprobacionException("El aprobador no es el asignado");
        }
        if (ResultadoAprobacion.PENDIENTE != estado) {
            throw new AprobacionException("Solo se puede aprobar una aprobación pendiente");
        }
        setEstado(ResultadoAprobacion.APROBADA);
        setAprobadoEn(LocalDateTime.now());
    }

    public boolean esAprobador(String candidato) {
        var candidateUserName = new NombreUsuario(candidato);
        return this.aprobador.equals(candidateUserName);
    }

    public void desaprueba(String aprobador, String observaciones) {
        if (observaciones == null || observaciones.isBlank()) {
            throw new AprobacionException("Debe especificar una observación para desaprueba la aprobación");
        }
        if (ResultadoAprobacion.PENDIENTE != estado) {
            throw new AprobacionException("Solo se puede desaprueba una aprobación pendiente");
        }
        if (!esAprobador(aprobador)) {
            throw new AprobacionException("No se puede desaprueba una aprobación que no está pendiente");
        }
        setEstado(ResultadoAprobacion.RECHAZADA);
        setAprobadoEn(LocalDateTime.now());
        this.observaciones = observaciones;
    }

    public boolean fueAprobada() {
        return ResultadoAprobacion.APROBADA == estado;
    }

    public void reset() {
        estado = ResultadoAprobacion.PENDIENTE;
        aprobadoEn = null;
        observaciones = null;
    }

    /***
     * Indica si la aprobación ha sido aprobada o rechazada
     * por el responsable de la aprobación
     * @return true si la aprobación ha sido aprobada, false en caso contrario
     */
    public boolean estaResuelta() {
        return Optional.ofNullable(aprobadoEn).isPresent();
    }
}
