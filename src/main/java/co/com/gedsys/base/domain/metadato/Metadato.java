package co.com.gedsys.base.domain.metadato;

import co.com.gedsys.base.domain.metadato.enums.FormatoMetadatoEnum;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.UUID;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Accessors(fluent = true)
@EqualsAndHashCode(of = {"id", "patron"})
public class Metadato {
    @Getter
    @Setter
    private UUID id;
    private PatronMetadato patron;
    private NombreMetadato nombre;
    private TipoMetadato tipo;
    private FormatoMetadato formato;
    private ValorMetadato valor;

    private Metadato(PatronMetadato patron,
                     NombreMetadato nombre,
                     TipoMetadato tipo,
                     FormatoMetadato formato,
                     String valor) {
        this.patron = patron;
        this.nombre = nombre;
        this.tipo = tipo;
        this.formato = formato;
        this.valor = new ValorMetadato(valor);
    }

    protected static Metadato create(
            PatronMetadato patron,
            NombreMetadato nombre,
            TipoMetadato tipo,
            FormatoMetadato formato,
            String valor) {
        return new Metadato(
                patron,
                nombre,
                tipo,
                formato,
                valor
        );
    }

    public Metadato(
            UUID id
            , String patron
            , String nombre
            , TipoMetadatoEnum tipo
            , FormatoMetadatoEnum formato
            , String valor) {
        this.id = id;
        this.patron = new PatronMetadato(patron);
        this.nombre = new NombreMetadato(nombre);
        this.tipo = new TipoMetadato(tipo);
        this.formato = new FormatoMetadato(formato);
        this.valor = new ValorMetadato(valor);
    }

    public String patron() {
        return patron.getValue();
    }

    public Metadato patron(String patron) {
        this.patron = new PatronMetadato(patron);
        return this;
    }

    public String nombre() {
        return nombre.getValue();
    }

    public Metadato nombre(String nombre) {
        this.nombre = new NombreMetadato(nombre);
        return this;
    }

    public TipoMetadatoEnum tipo() {
        return tipo.getValue();
    }

    public Metadato tipo(TipoMetadatoEnum tipo) {
        this.tipo = new TipoMetadato(tipo);
        return this;
    }

    public FormatoMetadatoEnum formato() {
        return formato.getValue();
    }

    public Metadato formato(FormatoMetadatoEnum formato) {
        this.formato = new FormatoMetadato(formato);
        return this;
    }

    public String valor() {
        return valor.getValue();
    }

    public Metadato valor(String valor) {
        this.valor = new ValorMetadato(valor);
        return this;
    }
}
