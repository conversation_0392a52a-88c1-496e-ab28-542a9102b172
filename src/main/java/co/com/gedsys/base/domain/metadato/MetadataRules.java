package co.com.gedsys.base.domain.metadato;

import co.com.gedsys.base.domain.metadato.enums.MetadataRulesEnum;

import java.util.ArrayList;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.List;

public class MetadataRules {
    private final EnumMap<MetadataRulesEnum, Object> rulesMap;

    public MetadataRules(String rules) {
        rulesMap = getFromString(rules);
        rulesMap.putIfAbsent(MetadataRulesEnum.REQUERIDO, true);
    }

    private EnumMap<MetadataRulesEnum, Object> getFromString(String rules) {
        EnumMap<MetadataRulesEnum, Object> map = new EnumMap<>(MetadataRulesEnum.class);
        if (rules == null || rules.isBlank()) return map;

        String[] rulePairs = rules.split("\\s*,\\s*");
        for (String rulePair : rulePairs) {
            String[] keyValue = rulePair.split(":");
            var rule = MetadataRulesEnum.valueOf(keyValue[0].toUpperCase());
            var value = getRuleValue(rule, keyValue[1]);
            map.computeIfPresent(rule, (k, v) -> {
                throw new IllegalArgumentException(String.format("Regla repetida: %s", rule.name().toLowerCase()));
            });
            map.put(rule, value);
        }
        return map;
    }

    private Object getRuleValue(MetadataRulesEnum rule, String ruleValue) {
        return switch (rule.getAllowedValueType().getSimpleName()) {
            case "Integer" -> parseInteger(ruleValue);
            case "Boolean" -> parseBoolean(ruleValue);
            default ->
                    throw new IllegalStateException("Unexpected value: " + rule.getAllowedValueType().getSimpleName());
        };
    }

    private Boolean parseBoolean(String value) {
        return Boolean.parseBoolean(value);
    }

    private Integer parseInteger(String value) {
        return Integer.parseInt(value);
    }

    public String toString() {
        return getValue();
    }

    public String getValue() {
        final List<String> rulesList = new ArrayList<>();
        for (var entry : rulesMap.entrySet()) {
            rulesList.add(entry.getKey().name() + ":" + entry.getValue());
        }
        return String.join(",", rulesList).toLowerCase();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || getClass() != obj.getClass()) return false;
        if (this == obj) return true;
        return this.getValue().equals(((MetadataRules) obj).getValue());
    }

    @Override
    public int hashCode() {
        return this.getValue().hashCode();
    }

    public void applyTo(String value) {
        for (MetadataRulesEnum rule : rulesMap.keySet()) {
            Object ruleValue = rulesMap.get(rule);
            switch (rule) {
                case REQUERIDO -> validateRequired(value, (Boolean) ruleValue);
//                case FUTURO -> validateFuture(value);
                case LONGITUD -> validateLength(value, (Integer) ruleValue);
//                case MAXIMO -> validateMax(value, (Integer) ruleValue);
//                case MINIMO -> validateMin(value, (Integer) ruleValue);
//                case PASADO -> validatePast(value);
                case POSITIVO -> validatePositive(value);
            }
        }
    }

    private void validatePositive(String value) {
        if (value != null && Integer.parseInt(value) <= 0) {
            throw new RuntimeException("El valor debe ser positivo");
        }
    }

    private void validateRequired(String value, boolean required) {
        if (value == null && required) throw new RuntimeException("El valor es requerido");
    }

    private void validateLength(String value, Integer ruleValue) {
        if (value != null && value.length() > ruleValue) {
            throw new IllegalArgumentException("Longitud excedida " + ruleValue);
        }
    }

    public EnumSet<MetadataRulesEnum> names() {
        return EnumSet.copyOf(rulesMap.keySet());
    }
}
