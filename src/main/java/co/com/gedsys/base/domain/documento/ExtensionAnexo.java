package co.com.gedsys.base.domain.documento;

import co.com.gedsys.base.domain.common.StringValueObject;

class ExtensionAnexo extends StringValueObject {
    public ExtensionAnexo(String value) {
        super(value);
        judgeNullPolicy();
    }

    @Override
    protected String validate(String value) {
        if (value == null) return null;
        if (!value.matches("^[a-zA-Z0-9]+$")) {
            throw new IllegalArgumentException("La extensión solo puede contener letras y números sin puntos");
        }
        if (value.length() > 10) {
            throw new IllegalArgumentException("La extensión no puede exceder 10 caracteres");
        }
        return value.toLowerCase();
    }
}