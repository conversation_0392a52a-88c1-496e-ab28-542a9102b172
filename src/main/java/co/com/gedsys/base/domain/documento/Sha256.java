package co.com.gedsys.base.domain.documento;

import co.com.gedsys.base.domain.common.StringValueObject;

import java.util.regex.Pattern;

public class Sha256 extends StringValueObject {

    private static final Pattern SHA256_PATTERN = Pattern.compile("^[a-fA-F0-9]{64}$");


    protected Sha256(String value) {
        super(value);
        judgeNullPolicy();
    }

    @Override
    protected String validate(String value) {
        if (value == null) return null;
        if (!isValidSha256(value)) {
            throw new InvalidHashException();
        }
        return value;
    }

    public static boolean isValidSha256(String hash) {
        if (hash == null) {
            return false;
        }
        return SHA256_PATTERN.matcher(hash).matches();
    }
}
