package co.com.gedsys.base.domain.metadato.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum MetadataRulesEnum {
    FUTURO(Boolean.class),
    LONGITUD(Integer.class),
    MAXIMO(Integer.class),
    MINIMO(Integer.class),
    PASADO(Boolean.class),
    POSITIVO(Boolean.class),
    REQUERIDO(Boolean.class);

    private final Class allowedValueType;
}
