package co.com.gedsys.base.domain.plantillas;

import co.com.gedsys.base.domain.metadato.DefinicionMetadato;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Getter
@Setter
@Accessors(chain = true)
public class Plantilla {
    private UUID id;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private TituloPlantilla titulo;

    @Setter(AccessLevel.NONE)
    @Getter(AccessLevel.NONE)
    private EstadoPlantilla estado;

    private TipoDocumental tipoDocumental;

    private TipoPlantilla tipoPlantilla;

    private Set<DefinicionMetadato> esquemaDeMetadatos = new HashSet<>();
    
    private boolean producidoPorGedsys;

    public Plantilla(String titulo, TipoPlantilla tipo) {
        this.titulo = new TituloPlantilla(titulo);
        this.estado = EstadoPlantilla.INACTIVA;
        setTipoPlantilla(tipo); // Esto establecerá producidoPorGedsys según el tipo
    }

    @Builder
    public static Plantilla create(UUID id, String titulo,
                                   EstadoPlantilla estado,
                                   TipoDocumental tipoDocumental,
                                   Set<DefinicionMetadato> esquemaDeMetadatos,
                                   TipoPlantilla tipoPlantilla,
                                   boolean producidoPorGedsys) {
        var plantilla = new Plantilla(titulo, TipoPlantilla.PRODUCCION)
                .setId(id)
                .setTipoDocumental(tipoDocumental)
                .setEsquemaDeMetadatos(esquemaDeMetadatos)
                .setTipoPlantilla(tipoPlantilla)
                .setProducidoPorGedsys(producidoPorGedsys);
        if (estado == EstadoPlantilla.ACTIVA) plantilla.activar();
        return plantilla;
    }

    public Plantilla setTitulo(String titulo) {
        this.titulo = new TituloPlantilla(titulo);
        return this;
    }

    public String titulo() {
        return titulo.getValue();
    }

    public void activar() {
        if (estado != EstadoPlantilla.INACTIVA)
            throw new IllegalStateException("Solo se puede activar una plantilla inactiva.");
        if (tipoDocumental == null)
            throw new IllegalStateException("La plantilla debe tener un tipo documental para ser activada.");
        estado = EstadoPlantilla.ACTIVA;
    }

    public void desactivar() {
        if (estado == EstadoPlantilla.INACTIVA)
            throw new IllegalStateException("Solo se puede desactivar una plantilla activa.");
        estado = EstadoPlantilla.INACTIVA;
    }

    public String estado() {
        return estado.toString();
    }

    public Plantilla setTipoPlantilla(TipoPlantilla tipo) {
        if (tipo == null) throw new IllegalArgumentException("El tipo de plantilla es requerido.");
        this.tipoPlantilla = tipo;
        // Solo las plantillas de producción pueden ser producidas por Gedsys
        this.producidoPorGedsys = tipo == TipoPlantilla.PRODUCCION && this.producidoPorGedsys;
        return this;
    }
    
    public boolean isProducidoPorGedsys() {
        return producidoPorGedsys;
    }
    
    public Plantilla setProducidoPorGedsys(boolean producidoPorGedsys) {
        // Solo permitir establecer a true si el tipo es PRODUCCION
        if (producidoPorGedsys && tipoPlantilla != TipoPlantilla.PRODUCCION) {
            throw new IllegalStateException("Solo las plantillas de tipo PRODUCCION pueden ser producidas por Gedsys");
        }
        this.producidoPorGedsys = producidoPorGedsys;
        return this;
    }
}
