package co.com.gedsys.base.domain.organizacion;

import org.mapstruct.*;

import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.application.dto.UsuarioSeccionDTO;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SeccionMapper {
    SeccionDTO toDTO(Seccion seccion);

    UsuarioSeccionDTO toDTO(UsuarioSeccion usuarioSeccion);
}
