package co.com.gedsys.base.domain.usuario_externo;

/**
 * Excepción lanzada cuando se intenta agregar una propiedad duplicada a un usuario externo.
 * Una propiedad se considera duplicada si ya existe otra propiedad con el mismo valor
 * para el mismo usuario externo.
 */
public class DuplicatePropertyException extends RuntimeException {
    
    public DuplicatePropertyException(String propertyValue) {
        super(String.format("Ya existe una propiedad con el valor '%s' para este usuario externo", propertyValue));
    }
    
    public DuplicatePropertyException(String propertyValue, String propertyType) {
        super(String.format("Ya existe una propiedad de tipo '%s' con el valor '%s' para este usuario externo", 
                propertyType, propertyValue));
    }
}
