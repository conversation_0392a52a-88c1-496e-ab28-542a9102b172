package co.com.gedsys.base.domain.documento;

import co.com.gedsys.base.domain.common.LongValueObject;

public class AttachmentByteSize extends LongValueObject {

    protected AttachmentByteSize(Long value) {
        super(value);
        judgeNullPolicy();
    }

    @Override
    protected Long validate(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("Attachment byte size must be positive");
        }
        return value;
    }
}
