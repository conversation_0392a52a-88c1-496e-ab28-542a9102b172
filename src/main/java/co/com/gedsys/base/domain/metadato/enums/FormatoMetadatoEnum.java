package co.com.gedsys.base.domain.metadato.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.EnumSet;

@Getter
@RequiredArgsConstructor
public enum    FormatoMetadatoEnum {
    ALFANUMERICO(EnumSet.of(MetadataRulesEnum.REQUERIDO, MetadataRulesEnum.LONGITUD)),
    EMAIL(EnumSet.of(MetadataRulesEnum.REQUERIDO, MetadataRulesEnum.LONGITUD)),
    FECHA(EnumSet.of(MetadataRulesEnum.REQUERIDO, MetadataRulesEnum.PASADO, MetadataRulesEnum.FUTURO)),
    FECHA_HORA(EnumSet.of(MetadataRulesEnum.REQUERIDO, MetadataRulesEnum.PASADO, MetadataRulesEnum.FUTURO)),
    LISTA(EnumSet.of(MetadataRulesEnum.REQUERIDO)),
    NUMERICO(EnumSet.of(MetadataRulesEnum.REQUERIDO, MetadataRulesEnum.POSITIVO)),
    BOOLEANO(EnumSet.of(MetadataRulesEnum.REQUERIDO));

    private final EnumSet<MetadataRulesEnum> allowedRules;
}
