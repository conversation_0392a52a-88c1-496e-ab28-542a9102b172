package co.com.gedsys.base.domain.organizacion;

import co.com.gedsys.base.domain.common.StringValueObject;

public class CodigoSeccion extends StringValueObject {
    public CodigoSeccion(String code) {
        super(code);
    }

    @Override
    protected String validate(String code) {
        return format(code);
    }

    private String format(String rawCode) {
        if (rawCode == null || rawCode.isEmpty()) {
            throw new CodigoSeccionException("El código no puede ser nulo o vacío");
        }

        String[] segments = rawCode.split("\\.");
        if (segments.length > 3) {
            throw new CodigoSeccionException("El código no puede tener más de 3 segmentos");
        }

        StringBuilder formattedCode = new StringBuilder();

        for (int i = 0; i < 3; i++) {
            String segment = (i < segments.length) ? segments[i] : "0";

            if (!segment.matches("\\d+")) {
                throw new CodigoSeccionException("Cada segmento debe ser un número");
            }

            int segmentValue = Integer.parseInt(segment);
            if (segmentValue == 0 && i == 0) {
                throw new CodigoSeccionException("El primer segmento no puede ser cero");
            }

            // Formateamos todos los segmentos a dos dígitos
            formattedCode.append(String.format("%02d", segmentValue));

            if (i < 2) {
                formattedCode.append(".");
            }
        }

        return formattedCode.toString();
    }

    protected TipoUnidad identificarTipoUnidad() {
        final var code = getValue();

        if (code == null || code.isEmpty()) {
            throw new IllegalArgumentException("El código formateado no puede ser nulo o vacío");
        }

        String[] segmentos = code.split("\\.");
        if (segmentos.length != 3) {
            throw new IllegalArgumentException("El código formateado debe tener exactamente 3 segmentos");
        }

        if (!segmentos[2].equals("00")) {
            return TipoUnidad.AUXILIAR;
        }

        if (!segmentos[1].equals("00")) {
            return TipoUnidad.SECRETARIA;
        }

        if (!segmentos[0].equals("00")) {
            return TipoUnidad.DESPACHO;
        }

        throw new CodigoSeccionException("Código inválido: todos los segmentos son cero");
    }
}
