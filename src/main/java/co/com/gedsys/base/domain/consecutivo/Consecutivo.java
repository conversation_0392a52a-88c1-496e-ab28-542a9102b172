package co.com.gedsys.base.domain.consecutivo;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.UUID;

import co.com.gedsys.base.domain.common.Default;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;

@Getter
@Setter
@Accessors(chain = true)
public class Consecutivo {
    private UUID id;
    private String prefijo;
    private String sufijo;
    private EstadoConsecutivo estado;
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private ContadorConsecutivo contador;
    private TipoConsecutivo tipoConsecutivo;

    private TipoDocumental tipoDocumental;
    private ClasificacionDocumental clasificacionDocumental;

    public Consecutivo(String prefijo, String sufijo, Integer contador, TipoConsecutivo tipoConsecutivo) {
        this(null, prefijo, sufijo, EstadoConsecutivo.ACTIVO, contador, tipoConsecutivo, null, null);
    }

    public Consecutivo(String prefijo, String sufijo, Integer contador,
                       TipoDocumental tipoDocumental, ClasificacionDocumental clasificacionDocumental) {

        this(null, prefijo, sufijo, EstadoConsecutivo.ACTIVO, contador, TipoConsecutivo.PRODUCCION, tipoDocumental, clasificacionDocumental);

        if (tipoDocumental == null) throw new ConsecutivoWithoutTipoDocumentalException();
    }

    @Default
    public Consecutivo(UUID id, String prefijo, String sufijo, EstadoConsecutivo estado,
                       Integer contador, TipoConsecutivo tipoConsecutivo,
                       TipoDocumental tipoDocumental, ClasificacionDocumental clasificacionDocumental) {
        this.id = id;
        this.prefijo = prefijo;
        this.sufijo = sufijo;
        this.estado = estado;
        this.contador = new ContadorConsecutivo(contador);
        this.tipoConsecutivo = tipoConsecutivo;
        this.tipoDocumental = tipoDocumental;
        this.clasificacionDocumental = clasificacionDocumental;
    }

    public Integer getContador() {
        return contador.getValue();
    }

    public void setContador(Integer contador) {
        this.contador = new ContadorConsecutivo(contador);
    }

    public int incrementarContador() {
        this.contador = contador.incrementar();
        return getContador();
    }

    public UUID getClasificacionDocumentalId() {
        return clasificacionDocumental != null ? clasificacionDocumental.getId() : null;
    }

    public UUID getTipoDocumentalId() {
        return tipoDocumental != null ? tipoDocumental.getId() : null;
    }

}
