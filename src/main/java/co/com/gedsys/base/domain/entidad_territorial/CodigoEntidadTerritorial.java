package co.com.gedsys.base.domain.entidad_territorial;

import co.com.gedsys.base.domain.common.StringValueObject;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

class CodigoEntidadTerritorial extends StringValueObject {

    protected CodigoEntidadTerritorial(String value) {
        super(value);
    }

    @Override
    protected String validate(String value) {
        String regex = "^(0*\\d+)(\\.0*\\d+){0,2}$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(value);
        if (!matcher.matches()) {
            throw new IllegalArgumentException("El código tiene un formato incorrecto");
        }
        return formatCode(value);
    }

    protected TipoEntidadTerritorial identificarTipo() {
        if (getValue().matches("^\\d+$")) {
            return TipoEntidadTerritorial.departamento;
        } else if (getValue().matches("^\\d+\\.\\d+$")) {
            return TipoEntidadTerritorial.municipio;
        } else if (getValue().matches("^\\d+\\.\\d+\\.\\d+$")) {
            return TipoEntidadTerritorial.vereda_corregimiento;
        }

        throw new IllegalStateException("Código inválido");
    }

    public String formatCode(String value) {
        var segments = value.split("\\.");
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < segments.length; i++) {
            try {
                String formattedSegment = String.format("%02d", Integer.parseInt(segments[i]));
                result.append(formattedSegment);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("El código tiene un formato incorrecto");
            }

            if (i < segments.length - 1) result.append(".");
        }


        return result.toString();
    }
}
