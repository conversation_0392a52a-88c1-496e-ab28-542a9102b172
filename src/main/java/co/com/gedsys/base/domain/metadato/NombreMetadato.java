package co.com.gedsys.base.domain.metadato;

import co.com.gedsys.base.domain.common.StringValueObject;

class NombreMetadato extends StringValueObject {
    protected NombreMetadato(String value) {
        super(value);
        checkEmptyNamePolicy();
    }

    private void checkEmptyNamePolicy() {
        if (getValue().isEmpty())
            throw new IllegalArgumentException("Nombre de metadato no puede estar vacío");
    }
}
