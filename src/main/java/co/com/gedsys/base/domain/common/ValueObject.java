package co.com.gedsys.base.domain.common;

import lombok.Getter;

import java.util.Optional;

@Getter
public abstract class ValueObject<T> {

    private final T value;

    protected ValueObject(T value) {
        this.value = validate(value);
    }

    /***
     * Execute before value assignation. Must be implemented by subclasses. Validate right value.
     * @param value value to be validated and assigned to this object.
     * @return value to be assigned to this object.
     */
    protected T validate(T value) {
        return value;
    }

    protected void judgeNullPolicy() {
        if (value == null) {
            throw new IllegalArgumentException(this.getClass().getCanonicalName() + " No puede recibir valores nulos.");
        }
    }

    public String toString() {
        return Optional.ofNullable(value).map(Object::toString).orElse("null");
    }

    public boolean equals(ValueObject<T> other) {
        return this.value.equals(other.value);
    }

}
