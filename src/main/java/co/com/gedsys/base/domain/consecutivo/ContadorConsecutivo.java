package co.com.gedsys.base.domain.consecutivo;

import co.com.gedsys.base.domain.common.ValueObject;

public class ContadorConsecutivo extends ValueObject<Integer> {

    public ContadorConsecutivo(Integer value) {
        super(value);
    }

    @Override
    protected Integer validate(Integer value) {
        if (value < 0) {
            throw new ContadorInvalidoConsecutivoException("El contador no puede ser negativo");
        }
        return super.validate(value);
    }

    public ContadorConsecutivo incrementar() {
        return new ContadorConsecutivo(getValue() + 1);
    }
}
