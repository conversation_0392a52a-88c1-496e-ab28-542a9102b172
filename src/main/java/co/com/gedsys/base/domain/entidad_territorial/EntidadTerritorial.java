package co.com.gedsys.base.domain.entidad_territorial;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class EntidadTerritorial {
    private Integer id;
    private CodigoEntidadTerritorial codigo;
    private String nombre;
    private TipoEntidadTerritorial tipo;

    public EntidadTerritorial(String codigo, String nombre) {
        this.codigo = new CodigoEntidadTerritorial(codigo);
        this.nombre = nombre;
        this.tipo = this.codigo.identificarTipo();
    }

    public String getCodigo() {
        return codigo.getValue();
    }

    public void setCodigo(String codigo) {
        if (codigo == null || codigo.isEmpty()) return;
        this.codigo = new CodigoEntidadTerritorial(codigo);
        this.tipo =this.codigo.identificarTipo();
    }

    public void setNombre(String nombre) {
        if (nombre == null || nombre.isEmpty()) return;
        this.nombre =nombre;
    }
}
