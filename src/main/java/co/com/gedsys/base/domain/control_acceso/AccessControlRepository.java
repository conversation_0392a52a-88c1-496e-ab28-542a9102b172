package co.com.gedsys.base.domain.control_acceso;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import co.com.gedsys.base.domain.common.DomainRepository;

public interface AccessControlRepository extends DomainRepository<AccessControlPolicy, UUID> {

    Optional<AccessControlPolicy> find(AccessLevel accessLevel, String code, InterestGroup interestGroup, String detail);

    List<AccessControlPolicy> search(Searchable searchable);

    interface Searchable {
        AccessLevel accessLevel();

        String code();

        InterestGroup interestGroup();

        String detail();
    }
}
