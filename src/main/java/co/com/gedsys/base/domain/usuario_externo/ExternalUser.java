package co.com.gedsys.base.domain.usuario_externo;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;


@Getter @Setter @Accessors(chain = true)
public class ExternalUser {
    private UUID id;
    private String notes;
    private String salutation;

    private ExternalUserStatus status;
    private ExternalUserIdentificationType identificationType;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private ExternalUserName name;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private IdentificationNumber identificationNumber;

    private List<ExternalUserProperty> properties;

    public ExternalUser(String name, String identificationType, String identificationNumber) {
        this.name = new ExternalUserName(name);
        this.identificationType = ExternalUserIdentificationType.valueOf(identificationType);

        // Validación cruzada estricta
        validateIdentificationConsistency(this.identificationType, identificationNumber);

        this.identificationNumber = new IdentificationNumber(identificationNumber, this.identificationType);
        this.status = ExternalUserStatus.ACTIVO;
        this.properties = new ArrayList<>();
    }

    public String getIdentificationNumber() {
        return identificationNumber.getValue();
    }

    public ExternalUser setIdentificationNumber(String identificationNumber) {
        validateIdentificationConsistency(this.identificationType, identificationNumber);
        this.identificationNumber = new IdentificationNumber(identificationNumber, this.identificationType);
        return this;
    }

    public String getName() {
        return name.getValue();
    }

    public ExternalUser setName(String name) {
        this.name = new ExternalUserName(name);
        return this;
    }

    public ExternalUser addProperties(List<ExternalUserProperty> properties) {
        validateNoDuplicateProperties(properties);
        properties.forEach(p -> p.setOwner(this));

        // Inicializar la lista si es null
        if (this.properties == null) {
            this.properties = new ArrayList<>();
        }

        this.properties.addAll(properties);
        return this;
    }

    private void validateNoDuplicateProperties(List<ExternalUserProperty> newProperties) {
        // Verificar duplicados dentro de las nuevas propiedades primero
        Set<String> newPropertyValues = newProperties.stream()
                .map(ExternalUserProperty::getPropertyValue)
                .collect(Collectors.toSet());

        if (newPropertyValues.size() != newProperties.size()) {
            throw new DuplicatePropertyException("Se encontraron valores duplicados en las propiedades a agregar");
        }

        // Si no hay propiedades existentes, no hay más validaciones
        if (this.properties == null || this.properties.isEmpty()) {
            return;
        }

        // Obtener valores existentes
        Set<String> existingPropertyValues = this.properties.stream()
                .map(ExternalUserProperty::getPropertyValue)
                .collect(Collectors.toSet());

        // Verificar si alguna nueva propiedad tiene un valor duplicado con las existentes
        for (ExternalUserProperty newProperty : newProperties) {
            String newValue = newProperty.getPropertyValue();
            if (existingPropertyValues.contains(newValue)) {
                throw new DuplicatePropertyException(newValue, newProperty.getPropertyType().name());
            }
        }
    }

    // Setter de tipo con validación cruzada
    public ExternalUser setIdentificationType(ExternalUserIdentificationType identificationType) {
        String currentNumber = this.identificationNumber != null ? this.identificationNumber.getValue() : null;

        // Normalizar número según el nuevo tipo
        String normalizedNumber = normalizeNumberForType(identificationType, currentNumber);

        this.identificationType = identificationType;
        this.identificationNumber = new IdentificationNumber(normalizedNumber, identificationType);
        return this;
    }

    // Método para normalizar número según el tipo
    private String normalizeNumberForType(ExternalUserIdentificationType type, String number) {
        if (type == ExternalUserIdentificationType.NA) {
            return null; // Forzar null para tipo NA
        } else {
            // Para tipos distintos a NA, validar que hay un número
            if (number == null || number.isBlank()) {
                throw new IllegalArgumentException("Usuarios con tipo " + type + " deben tener número de identificación");
            }
            return number; // Mantener el número para otros tipos
        }
    }

    // Método de validación cruzada
    private void validateIdentificationConsistency(ExternalUserIdentificationType type, String number) {
        if (type == ExternalUserIdentificationType.NA && number != null) {
            throw new IllegalArgumentException("Usuarios con tipo NA no pueden tener número de identificación");
        }
        if (type != ExternalUserIdentificationType.NA && number == null) {
            throw new IllegalArgumentException("Usuarios con tipo " + type + " deben tener número de identificación");
        }
    }
}
