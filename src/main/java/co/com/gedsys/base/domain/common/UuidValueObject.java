package co.com.gedsys.base.domain.common;

import java.util.Optional;
import java.util.UUID;

public abstract class UuidValueObject extends ValueObject<String> {
    protected UuidValueObject(String value) {
        super(value);
    }

    @Override
    protected String validate(String value) {
        return nullCheckPolicy(value);
    }

    protected String nullCheckPolicy(String value) {
        return Optional.ofNullable(value).map(this::validateUuid).orElse(UUID.randomUUID().toString());
    }

    private String validateUuid(String value) {
        if (value.matches("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-4[0-9a-fA-F]{3}-[89aAbB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$")) {
            return value;
        }
        throw new IllegalArgumentException("Invalid UUID v4 format");
    }
}
