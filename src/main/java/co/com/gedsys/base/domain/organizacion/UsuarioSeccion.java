package co.com.gedsys.base.domain.organizacion;

import java.util.UUID;

import co.com.gedsys.base.domain.usuario_interno.NombreUsuario;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UsuarioSeccion {
    private UUID id;
    private NombreUsuario username;
    private TipoRelacionUsuarioSeccion relacion;
    private Seccion seccion;

    public UsuarioSeccion(String username, TipoRelacionUsuarioSeccion relacion, Seccion seccion) {
        this.username = new NombreUsuario(username);
        this.relacion = relacion;
        this.seccion = seccion;
    }

    public String getUsername() {
        return username.getValue();
    }

    public void setUsername(String username) {
        this.username = new NombreUsuario(username);
    }
}