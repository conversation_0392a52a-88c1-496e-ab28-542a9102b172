package co.com.gedsys.base.domain.metadato;

import co.com.gedsys.base.domain.metadato.enums.FormatoMetadatoEnum;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Optional;
import java.util.UUID;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Accessors(chain = true)
public class DefinicionMetadato {
    @Getter @Setter
    private UUID id;
    private NombreMetadato nombre;
    @Getter
    private String descripcion;
    private TipoMetadato tipo;
    private FormatoMetadato formato;
    private MetadataRules rules;
    private PatronMetadato patron;

    public DefinicionMetadato(String nombre, TipoMetadatoEnum tipo, FormatoMetadatoEnum formato) {

        this(
                null,
                new NombreMetadato(nombre),
                "",
                new TipoMetadato(tipo),
                new FormatoMetadato(formato),
                new MetadataRules(null),
                new PatronMetadato(nombre)
        );
    }

    public static DefinicionMetadato create(
            String nombre,
            TipoMetadatoEnum tipo,
            FormatoMetadatoEnum formato) {

        return new DefinicionMetadato(
                nombre,
                tipo,
                formato
        );
    }

    private void runFormatRulesCompatibilityPolicy() {
        formato.validateCompatibility(rules);
    }

    public Metadato generarMetadato(String value) {
        rules.applyTo(value);
        formato.validateFormat(value);
        return Metadato.create(
                patron,
                nombre,
                tipo,
                formato,
                value
        );
    }

    public String getNombre() {
        return nombre.getValue();
    }

    public DefinicionMetadato setNombre(String nombre) {
        Optional.ofNullable(nombre)
                .ifPresent(name -> {
                    this.nombre = new NombreMetadato(name);
                    this.patron = new PatronMetadato(name);
                });
        return this;
    }

    public TipoMetadatoEnum getTipo() {
        return tipo.getValue();
    }

    public DefinicionMetadato setTipo(TipoMetadatoEnum tipo) {
        this.tipo = new TipoMetadato(tipo);
        return this;
    }

    public FormatoMetadatoEnum getFormato() {
        return formato.getValue();
    }

    public DefinicionMetadato setFormato(FormatoMetadatoEnum formato) {
        this.formato = new FormatoMetadato(formato);
        return this;
    }

    public String getRules() {
        return rules.getValue();
    }

    public DefinicionMetadato setRules(String rules) {
        Optional.ofNullable(rules)
                .ifPresent(r -> {
                    this.rules = new MetadataRules(r);
                    runFormatRulesCompatibilityPolicy();
                });
        return this;
    }

    public String getPatron() {
        return patron.getValue();
    }

    public DefinicionMetadato setDescripcion(String descripcion) {
        Optional.ofNullable(descripcion)
                .ifPresent(desc -> this.descripcion = desc);
        return this;
    }
}
