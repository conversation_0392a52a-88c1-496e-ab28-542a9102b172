package co.com.gedsys.base.domain.metadato;

import co.com.gedsys.base.domain.common.ValueObject;
import co.com.gedsys.base.domain.metadato.enums.FormatoMetadatoEnum;

class FormatoMetadato extends ValueObject<FormatoMetadatoEnum> {

    protected FormatoMetadato(FormatoMetadatoEnum value) {
        super(value);
        judgeNullPolicy();
    }

    public void validateCompatibility(MetadataRules rules) {
        if (!this.getValue().getAllowedRules().containsAll(rules.names())) {
            throw new IllegalArgumentException("Formato no compatible con las rules");
        }
    }

    public void validateFormat(String value) {
        if (value == null) return;

        if (this.getValue() == FormatoMetadatoEnum.EMAIL) {
            validateEmail(value);
        }
    }

    private void validateEmail(String value) {
        // Patrón básico para validar email: debe contener @ y al menos un punto después del @
        if (!value.matches("^[^@]+@[^@]+\\.[^@]+$")) {
            throw new RuntimeException("formato de email inválido");
        }
    }
}
