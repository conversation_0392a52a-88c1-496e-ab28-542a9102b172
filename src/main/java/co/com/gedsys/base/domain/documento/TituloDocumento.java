package co.com.gedsys.base.domain.documento;

import co.com.gedsys.base.domain.common.StringValueObject;

import java.util.Optional;

public class TituloDocumento extends StringValueObject {
    protected TituloDocumento(String value) {
        super(value);
        judgeNullPolicy();
        judgeBlankPolicy();
    }

    @Override
    protected String validate(String value) {
        return Optional.ofNullable(value)
                .filter(v -> v.equals(v.replaceAll("[\\\\/:*?\"<>|]", "")))
                .orElseThrow(() -> new IllegalArgumentException("El título contiene caracteres no permitidos"));
    }
}
