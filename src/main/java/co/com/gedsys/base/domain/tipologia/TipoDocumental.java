package co.com.gedsys.base.domain.tipologia;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

import static co.com.gedsys.base.domain.tipologia.EstadoTipoDocumental.ACTIVO;

@Getter
@Setter
public class TipoDocumental {
    private UUID id;
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private NombreTipoDocumental nombre;
    private EstadoTipoDocumental estado;


    public TipoDocumental(String nombre) {
        this.nombre = new NombreTipoDocumental(nombre);
        this.estado = ACTIVO;
    }

    public String nombre() {
        return nombre.getValue();
    }

    public TipoDocumental renombrar(String nombre) {
        this.nombre = new NombreTipoDocumental(nombre);
        return this;
    }
}
