package co.com.gedsys.base.domain.organizacion;

import co.com.gedsys.base.application.common.SeccionSinResponsableException;
import lombok.*;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

@Getter
@Setter
@ToString(of = {"id", "codigo", "nombre", "padre"})
@EqualsAndHashCode(of = {"id", "codigo", "nombre"})
public class Seccion {
    private UUID id;
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private CodigoSeccion codigo;
    private String nombre;
    private String responsable;
    @Setter(AccessLevel.NONE)
    private TipoUnidad tipo;
    private EstadoSeccion estado;

    private Seccion padre;

    private final Set<Seccion> hijos = new HashSet<>();

    private final Set<UsuarioSeccion> usuarios = new HashSet<>();

    public Seccion(String codigo, String nombre) {
        if (codigo == null || nombre == null) {
            throw new IllegalArgumentException("Código y nombre son requeridos");
        }
        this.codigo = new CodigoSeccion(codigo);
        this.tipo = this.codigo.identificarTipoUnidad();
        this.nombre = nombre;
        this.estado = EstadoSeccion.ACTIVA;
    }

    public String codigo() {
        return codigo != null ? codigo.getValue() : null;
    }

    public String codigoSuperiorInmediato() {
        return padre != null ? padre.getCodigo() : null;
    }

    public String nombreSuperiorInmediato() {
        return padre != null ? padre.getNombre() : null;
    }

    public boolean esUnDespacho() {
        return tipo == TipoUnidad.DESPACHO;
    }

    public void setPadre(Seccion padre) {
        validarPadre(padre);
        Seccion antiguoPadre = this.padre;
        validarJerarquiaCodigo(padre, this);
        this.padre = padre;

        if (antiguoPadre != null) {
            antiguoPadre.hijos.remove(this);
        }

        if (padre != null && !padre.hijos.contains(this)) {
            padre.hijos.add(this);
        }
    }

    private void validarPadre(Seccion padre) {
        if (padre == null) return;

        if (esUnDespacho() && !padre.esUnDespacho()) {
            throw new IllegalArgumentException("No se puede asignar un padre a una seccion despacho");
        }
        if (Objects.equals(padre, this)) {
            throw new IllegalArgumentException("No se puede autoagregar una seccion como padre");
        }
        if (hijos.contains(padre)) {
            throw new IllegalArgumentException("No se puede agregar un padre a una seccion hijo");
        }
    }

    public void agregarHijo(Seccion subSeccion) {
        validarHijo(subSeccion);
        if (subSeccion.padre != this) {
            subSeccion.setPadre(this);
        }
    }

    private void validarHijo(Seccion subSeccion) {
        if (subSeccion == null || Objects.equals(subSeccion, this)) {
            throw new IllegalArgumentException("No se puede autoagregar una seccion como hijo");
        }

        // Verificar si la sección que intentamos agregar como hijo es nuestro padre
        if (Objects.equals(this.padre, subSeccion)) {
            throw new IllegalArgumentException("No se puede agregar como hijo a una sección que ya es padre");
        }

        // Verificar si ya es hijo (ignorar si ya existe)
        if (Objects.equals(subSeccion.padre, this)) {
            return;
        }

        // Verificar la jerarquía de tipos
        if (this.tipo.tieneCategoriaInferior(subSeccion.tipo)) {
            throw new IllegalArgumentException("No se puede agregar un hijo con categoría superior");
        }

        validarJerarquiaCodigo(this, subSeccion);
    }

    private void validarJerarquiaCodigo(Seccion padre, Seccion hijo) {
        if (padre == null || padre.getPadre() == null) return;
        String codigoPadre = padre.getCodigo();
        String codigoHijo = hijo.getCodigo();

        String[] segmentosPadre = codigoPadre.split("\\.");
        String[] segmentosHijo = codigoHijo.split("\\.");

        // El primer segmento debe coincidir para mantener la jerarquía
        if (!segmentosPadre[0].equals(segmentosHijo[0])) {
            throw new IllegalArgumentException(
                    "El código del hijo debe pertenecer a la misma jerarquía del padre");
        }

        // Para secretarías, el segundo segmento debe ser coherente
        if (padre.getTipo() == TipoUnidad.DESPACHO && !segmentosHijo[1].equals("00")) {
            if (!segmentosPadre[0].equals(segmentosHijo[0])) {
                throw new IllegalArgumentException(
                        "El código de la secretaría debe pertenecer al mismo despacho");
            }
        }

        // Para auxiliares, los dos primeros segmentos deben coincidir con su secretaría padre
        if (padre.getTipo() == TipoUnidad.SECRETARIA && !segmentosHijo[2].equals("00")) {
            if (!segmentosPadre[0].equals(segmentosHijo[0]) ||
                    !segmentosPadre[1].equals(segmentosHijo[1])) {
                throw new IllegalArgumentException(
                        "El código del auxiliar debe pertenecer a la misma secretaría");
            }
        }
    }

    public void removerHijo(Seccion hijo) {
        if (!hijos.contains(hijo)) {
            throw new IllegalArgumentException("No se puede remover un hijo que no existe");
        }
        hijos.remove(hijo);
    }

    public String getCodigo() {
        return codigo.getValue();
    }

    public void agregarUsuario(String username, TipoRelacionUsuarioSeccion relacion) {
        if (username == null || relacion == null) {
            throw new IllegalArgumentException("El username y la relación no pueden ser nulos");
        }

        boolean existeUsuario = usuarios.stream()
                .anyMatch(u -> u.getUsername().equals(username));
        //todo: simplificar la validación de usuario repetido filtrando por username y relación
        if (existeUsuario) {
            UsuarioSeccion usuarioExistente = usuarios.stream()
                    .filter(u -> u.getUsername().equals(username))
                    .findFirst()
                    .orElse(null);

            if (usuarioExistente != null && usuarioExistente.getRelacion() != relacion) {
                throw new UsuarioSeccionRelacionDobleException();
            }

            throw new UsuarioSeccionDuplicadoException();
        }

        UsuarioSeccion nuevoUsuario = new UsuarioSeccion(username, relacion, this);
        usuarios.add(nuevoUsuario);
    }

    public void removerUsuario(String username) {
        if (username == null) {
            throw new IllegalArgumentException("El username no puede ser nulo");
        }

        boolean eliminado = usuarios.removeIf(u -> u.getUsername().equals(username));

        if (!eliminado) {
            throw new IllegalArgumentException("El usuario no existe en la sección");
        }
    }

    public void validarActivacion() {
        if (this.responsable == null || this.responsable.trim().isEmpty()) {
            throw new SeccionSinResponsableException();
        }
    }

    public void validarActivacionPermitida() {
        validarActivacion();
    }

    public void validarInactivacionPermitida() {
        if (this.estado == EstadoSeccion.INACTIVA) {
            throw new SeccionYaInactivaException();
        }
    }

    public void activar() {
        validarActivacionPermitida();
        this.estado = EstadoSeccion.ACTIVA;
    }

    public void inactivar() {
        validarInactivacionPermitida();
        this.estado = EstadoSeccion.INACTIVA;
    }

    public void asignarEstadoPorDefecto() {
        this.estado = (this.responsable == null || this.responsable.trim().isEmpty())
            ? EstadoSeccion.INACTIVA
            : this.estado;
    }
}
