package co.com.gedsys.base.domain.unidad_documental;

import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
public class UnidadDocumental {
    private UUID id;
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private NombreUnidadDocumental nombre;
    private String descripcion;
    private ClasificacionDocumental clasificacion;
    private Estado estado;


    public UnidadDocumental(String nombre, ClasificacionDocumental clasificacion) {
        this.nombre = new NombreUnidadDocumental(nombre);
        this.clasificacion = clasificacion;
        this.estado = Estado.ABIERTA;
    }

    public enum Estado {
        ABIERTA,
        CERRADA,
        ELIMINADA
    }

    public String codigoClasificatorio() {
        return clasificacion.codigoCompleto();
    }

    public String nombre() {
        return nombre.getValue();
    }

    public UUID getclasificacionId() {
        return clasificacion.getId();
    }
}
