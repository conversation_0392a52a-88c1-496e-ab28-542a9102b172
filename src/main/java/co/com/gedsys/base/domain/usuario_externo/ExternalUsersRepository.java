package co.com.gedsys.base.domain.usuario_externo;

import co.com.gedsys.base.domain.common.DomainRepository;

import java.util.Optional;
import java.util.UUID;

public interface ExternalUsersRepository extends DomainRepository<ExternalUser, UUID> {
    ExternalUser update(UUID id, ExternalUser entity);

    // Métodos específicos para tipo NA
    boolean existsByNameAndIdentificationType(String name, ExternalUserIdentificationType type);
    Optional<ExternalUser> findByNameAndIdentificationType(String name, ExternalUserIdentificationType type);
}
