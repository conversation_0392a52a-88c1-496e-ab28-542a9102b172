package co.com.gedsys.base.domain.control_acceso;

import co.com.gedsys.base.domain.common.StringValueObject;

public class ClassificationCode extends StringValueObject {
    protected ClassificationCode(String value) {
        super(value);
    }

    @Override
    protected String validate(String value) {
        return validateCodeString(value);
    }

    private String validateCodeString(String code) throws IllegalArgumentException {
        if (code == null || code.isEmpty()) {
            throw new IllegalArgumentException("Code cannot be null or empty");
        }

        // Update regex to ensure each segment is at least two digits
        if (!code.matches("(\\d{2,})(\\.(\\d{2,}))*")) {
            throw new IllegalArgumentException("Each segment must be at least 2 digits");
        }

        String[] segments = code.split("\\.");
        StringBuilder validatedCode = new StringBuilder();

        for (int i = 0; i < segments.length; i++) {
            try {
                int num = Integer.parseInt(segments[i]);
                if (i == 0 && num <= 0) {
                    // Only the first segment must be greater than 0
                    throw new IllegalArgumentException("The first segment must be greater than 0");
                }
                // Ensure each segment has two digits
                validatedCode.append(String.format("%02d", num));
                if (i < segments.length - 1) {
                    validatedCode.append(".");
                }
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("Each segment must be a valid number");
            }
        }

        return validatedCode.toString();
    }
}
