package co.com.gedsys.base.domain.usuario_externo;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.UUID;

@Getter
@Setter
@Accessors(chain = true)
public class ExternalUserProperty {
    private UUID id;
    private String propertyName;
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private PropertyValue propertyValue;
    private String notes;

    private ExternalUser owner;

    public ExternalUserProperty(ExternalUserPropertyType propertyType, String propertyName, String propertyValue) {
        this.propertyName = propertyName;
        this.propertyValue = new PropertyValue(propertyType, propertyValue);
    }

    public ExternalUserPropertyType getPropertyType() {
        return propertyValue.getType();
    }

    public String getPropertyValue() {
        return propertyValue.getValue();
    }
}
