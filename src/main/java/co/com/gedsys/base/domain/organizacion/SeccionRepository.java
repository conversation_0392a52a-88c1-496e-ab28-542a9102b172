package co.com.gedsys.base.domain.organizacion;

import co.com.gedsys.base.domain.common.DomainRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface SeccionRepository extends DomainRepository<Seccion, UUID> {

    Optional<Seccion> findByCode(String s);

    List<Seccion> buscarSeccionesPorEstado(EstadoSeccion estado);

    Optional<Seccion> findById(UUID id);

    Optional<Seccion> buscarNodoPrincipalDelOrganigrama();

    boolean tieneHijos(UUID id);

    boolean tieneUsuarios(UUID id);

    List<Seccion> buscarSeccionesPorUsuario(String username);

    List<Seccion> buscarHijasConEstado(UUID padreId, EstadoSeccion estado);

    boolean tieneHijasActivas(UUID padreId);

    boolean tienePadreInactivo(UUID seccionId);
}
