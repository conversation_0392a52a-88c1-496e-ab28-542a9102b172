package co.com.gedsys.base.domain.documento;

import co.com.gedsys.base.domain.common.StringValueObject;

public class NombreAnexo extends StringValueObject {
    public NombreAnexo(String value) {
        super(value);
        judgeNullPolicy();
    }

    @Override
    protected String validate(String value) {
        if (value == null) return null;
        if (value.length() > 255) {
            throw new IllegalArgumentException("El nombre del anexo no puede exceder 255 caracteres");
        }
        if (!value.matches("^[a-zA-Z0-9\\-_\\s]+$")) {
            throw new IllegalArgumentException("El nombre del anexo solo puede contener letras, números, guiones, guiones bajos y espacios");
        }
        return value;
    }
}