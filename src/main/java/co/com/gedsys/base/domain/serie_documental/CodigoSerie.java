package co.com.gedsys.base.domain.serie_documental;

import co.com.gedsys.base.domain.common.StringValueObject;

public class CodigoSerie extends StringValueObject {
    public CodigoSerie(String value) {
        super(value);

    }

    @Override
    protected String validate(String value) {
        return format(value);
    }

    private String format(String rawCode) {
        if (rawCode == null || rawCode.isEmpty()) {
            throw new IllegalArgumentException("Code cannot be null or empty");
        }

        String[] parts = rawCode.split("\\.");
        if (parts.length > 2) {
            throw new IllegalArgumentException("Invalid code format");
        }

        StringBuilder formattedCode = new StringBuilder();

        for (int i = 0; i < parts.length; i++) {
            String part = parts[i].replaceFirst("^0+", "");
            if (part.isEmpty() || !part.matches("\\d+")) {
                throw new IllegalArgumentException("Invalid code segment: " + parts[i]);
            }

            int number = Integer.parseInt(part);
            if (number <= 0) {
                throw new IllegalArgumentException("Code segments must be greater than 0");
            }

            formattedCode.append(number < 10 ? String.format("%02d", number) : part);
            if (i < parts.length - 1) {
                formattedCode.append(".");
            }
        }

        return formattedCode.toString();
    }

    public TipoSerie getType() {
        return getValue().contains(".") ? TipoSerie.SUBSERIE : TipoSerie.SERIE;
    }

    public String codigoSerie() {
        return getValue().split("\\.")[0];
    }
}
