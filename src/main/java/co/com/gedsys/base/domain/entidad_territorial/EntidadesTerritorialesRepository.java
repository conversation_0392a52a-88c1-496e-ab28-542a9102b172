package co.com.gedsys.base.domain.entidad_territorial;

import co.com.gedsys.base.domain.common.DomainRepository;

import java.util.List;

public interface EntidadesTerritorialesRepository extends DomainRepository<EntidadTerritorial, Integer> {
    List<EntidadTerritorial> checkStock(List<EntidadTerritorial> forChecking);

    List<EntidadTerritorial> saveAll(List<EntidadTerritorial> toCreateList);

    boolean checkStock(String codigo, String nombre);
}
