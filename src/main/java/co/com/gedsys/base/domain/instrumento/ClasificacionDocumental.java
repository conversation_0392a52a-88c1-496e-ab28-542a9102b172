package co.com.gedsys.base.domain.instrumento;

import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;

import java.util.List;
import java.util.UUID;

@Setter
@Getter
public class ClasificacionDocumental {
    private UUID id;
    private Seccion seccion;
    private SerieDocumental subSerie;
    private List<TipoDocumental> tiposDocumentales;

    public ClasificacionDocumental(@NonNull Seccion seccion, @NonNull SerieDocumental subSerie) {
        this.seccion = seccion;
        this.subSerie = subSerie;
    }

    public String getCodigoOficinaPrincipal() {
        return seccion.esUnDespacho() ? seccion.codigo() : seccion.codigoSuperiorInmediato();
    }

    public String getNombreOficinaPrincipal() {
        return seccion.esUnDespacho() ? seccion.getNombre() : seccion.nombreSuperiorInmediato();
    }

    public String getCodigoDependencia() {
        return seccion.esUnDespacho() ? "" : seccion.codigo();
    }

    public String getNombreDependencia() {
        return seccion.esUnDespacho() ? "" : seccion.getNombre();
    }

    public String getNombreSerie() {
        return subSerie.nombreSeriePadre();
    }

    public String getCodigoSerie() {
        return subSerie.codigoSeriePadre();
    }

    public String getNombreSubSerie() {
        return subSerie.getNombre();
    }

    public String getCodigoSubSerie() {
        return subSerie.codigo();
    }

    public String codigoCompleto() {
        return String.format("%s.%s", seccion.codigo(), subSerie.codigo());
    }

    public String getCodigoSeccion() {
        return seccion.codigo();
    }
}
