package co.com.gedsys.base.domain.usuario_externo;

import co.com.gedsys.base.domain.common.StringValueObject;

class IdentificationNumber extends StringValueObject {
    protected IdentificationNumber(String value, ExternalUserIdentificationType type) {
        super(validateByType(value, type));
        applyValidationPolicy(value, type);
    }

    private static String validateByType(String value, ExternalUserIdentificationType type) {
        if (type == ExternalUserIdentificationType.NA) {
            if (value != null) {
                throw new IllegalArgumentException("Tipo NA requiere número de identificación NULL");
            }
            return null; // Forzar NULL para tipo NA
        } else {
            if (value == null || value.isBlank()) {
                throw new IllegalArgumentException("Tipos distintos a NA requieren número de identificación");
            }
            return value;
        }
    }

    private void applyValidationPolicy(String value, ExternalUserIdentificationType type) {
        if (type != ExternalUserIdentificationType.NA) {
            judgeNullPolicy();
            judgeBlankPolicy();
        }
        // Para tipo NA, value ya es null y es válido
    }
}
