package co.com.gedsys.base.domain.metadato;

import co.com.gedsys.base.util.CamelCaseUtil;
import co.com.gedsys.base.domain.common.StringValueObject;

class PatronMetadato extends StringValueObject {
    protected PatronMetadato(String value) {
        super(value);
        judgeNullPolicy();
    }

    @Override
    protected String validate(String value) {
        return new CamelCaseUtil(value).camelize();
    }
}
