package co.com.gedsys.base.domain.serie_documental;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface SerieDocumentalRepository {
    boolean existenciaPorCodigo(String codigo);
    void guardar(SerieDocumental serieDocumental);
    Optional<SerieDocumental> findByCode(String codigo);
Optional<SerieDocumental> findById(UUID id);
    List<SerieDocumental> buscar(EstadoSerie estado);
    boolean tieneHijasActivas(UUID serieId);
    boolean tieneClasificacionesDocumentalesAsociadas(UUID serieId);
    boolean tieneDocumentosAsociados(UUID serieId);
    boolean tieneUnidadesDocumentalesAsociadas(UUID serieId);
    boolean tienePadreInactivo(UUID serieId);
}
