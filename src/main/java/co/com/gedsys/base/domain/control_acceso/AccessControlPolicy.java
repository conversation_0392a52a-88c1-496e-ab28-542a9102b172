package co.com.gedsys.base.domain.control_acceso;

import lombok.Getter;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.Set;
import java.util.UUID;

import co.com.gedsys.base.domain.common.Default;

@Getter
@Accessors(chain = true)
public class AccessControlPolicy {
    private UUID id;
    private AccessLevel accessLevel;
    @Getter(lombok.AccessLevel.NONE)
    private ClassificationCode code;
    private String detail; //planned to be username, groupName or externalUserId
    private InterestGroup interestGroup;
    private Set<AccessControlPermission> permissions;
    private String notes;

    public AccessControlPolicy(
            AccessLevel accessLevel,
            String code,
            InterestGroup interestGroup,
            String detail,
            Collection<AccessControlPermission> permissions,
            String notes
    ) {
        this.accessLevel = accessLevel;
        this.code = new ClassificationCode(code);
        this.detail = detail;
        this.interestGroup = interestGroup;
        this.permissions = Set.copyOf(permissions);
        this.notes = notes;
    }

    @Default
    public AccessControlPolicy(UUID id, AccessLevel accessLevel, String code, String detail,
                               InterestGroup interestGroup, Collection<AccessControlPermission> permissions, String notes) {
        this(accessLevel, code, interestGroup, detail, permissions, notes);
        this.id = id;
    }

    public boolean hasPermission(AccessControlPermission permission) {
        return permissions.contains(permission);
    }

    public String getCode() {
        return code.getValue();
    }

}
