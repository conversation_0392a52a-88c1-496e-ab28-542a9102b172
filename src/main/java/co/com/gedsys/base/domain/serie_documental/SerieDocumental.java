package co.com.gedsys.base.domain.serie_documental;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

import static co.com.gedsys.base.domain.serie_documental.EstadoSerie.ACTIVA;
import static co.com.gedsys.base.domain.serie_documental.EstadoSerie.INACTIVA;
import static co.com.gedsys.base.domain.serie_documental.TipoSerie.SUBSERIE;

@Setter
@Getter
public class SerieDocumental {
    private UUID id;
    @Getter(AccessLevel.NONE)
    private CodigoSerie codigo;
    private String nombre;
    private TipoSerie tipo;
    private EstadoSerie estado;

    private SerieDocumental padre;


    public SerieDocumental(String codigo, String nombre) {
        this.codigo = new CodigoSerie(codigo);
        this.nombre = nombre;
        this.tipo = this.codigo.getType();
        this.estado = ACTIVA;
    }

    public String codigo() {
        return codigo.getValue();
    }

    public SerieDocumental setCodigo(String codigo) {
        this.codigo = new CodigoSerie(codigo);
        return this;
    }

    public boolean esSubserie() {
        return tipo == SUBSERIE;
    }

    public String codigoSeriePadre() {
        return codigo.codigoSerie();
    }

    public String nombreSeriePadre() {
        return padre != null ? padre.getNombre() : "";
    }

    public void activar() {
        if (this.estado == ACTIVA) {
            throw new IllegalStateException("La serie documental ya se encuentra activa.");
        }
        this.estado = ACTIVA;
    }

    public void inactivar() {
        if (this.estado == INACTIVA) {
            throw new IllegalStateException("La serie documental ya se encuentra inactiva.");
        }
        this.estado = INACTIVA;
    }
}
