package co.com.gedsys.base.domain.usuario_externo;

import lombok.Getter;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Getter
final class PropertyValue {
    private final ExternalUserPropertyType type;
    private final String value;

    private final String PHONE_PATTERN = "^(?:\\+57|57)?60[1-8][0-9]{7}$";
    private final String MOBILE_PATTERN = "^(?:\\+57|57)?3(?:0[0-9]|1[0-9]|2[0-9]|3[0-9]|4[0-9]|5[0-9])[0-9]{7}$";

    public PropertyValue(ExternalUserPropertyType type, String value) {
        this.type = type;
        this.value = value;
        runValueTypePolicy();
    }

    private void runValueTypePolicy() {
        switch (type) {
                    case ADDRESS -> validateAddress(value);
                    case CHARACTERIZATION -> validateCharacterization(value);
                    case MOBILE -> validateMobile(value);
                    case PHONE -> validateLandline(value);
                    case WHATSAPP -> validateWhatsapp(value);
                    case EMAIL -> validateEmail(value);
                    case DATE -> validateDate(value);
                    case DATETIME -> validateDateTime(value);
                    default -> throw new IllegalArgumentException("Unexpected value: " + type);
        }
    }

    private void validateDateTime(String datetime) {
        try {
            OffsetDateTime.parse(datetime, DateTimeFormatter.ISO_DATE_TIME);
        } catch (DateTimeParseException e1) {
            try {
                ZonedDateTime.parse(datetime, DateTimeFormatter.ISO_DATE_TIME);
            } catch (DateTimeParseException e2) {
                throw new IllegalArgumentException("Invalid datetime, ISO 8601 is required");
            }
        }
    }

    private void validateDate(String date) {
        try {
            LocalDate.parse(date, DateTimeFormatter.ISO_DATE);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date, ISO 8601 is required");
        }
    }

    private void validateEmail(String email) {
        if (!email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"))
            throw new IllegalArgumentException("Invalid email");
    }

    private void validatePhoneNumber(String inputPhoneNumber, String regex) {
        if (!inputPhoneNumber.matches(regex)) throw new IllegalArgumentException("Invalid phone number");
    }

    private void validateLandline(String value) {
        validatePhoneNumber(value, PHONE_PATTERN);
    }

    private void validateWhatsapp(String whatsappNumber) {
        validatePhoneNumber(whatsappNumber, MOBILE_PATTERN);
    }

    private void validateMobile(String value) {
        validatePhoneNumber(value, MOBILE_PATTERN);
    }

    private void validateCharacterization(String value) {
        if (value == null || value.isBlank()) throw new IllegalArgumentException("Invalid characterization");
    }

    private void validateAddress(String value) {
        if (value == null || value.isBlank()) throw new IllegalArgumentException("Invalid address");
    }
}
