package co.com.gedsys.base.domain.instrumento;

import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Getter @Setter
@EqualsAndHashCode(of = {"id"})
public class ClasificacionTipologica {
    private UUID id;
    private ClasificacionDocumental clasificacion;
    private TipoDocumental tipoDocumental;

    public ClasificacionTipologica(ClasificacionDocumental clasificacion, TipoDocumental tipoDocumental) {
        this.clasificacion = clasificacion;
        this.tipoDocumental = tipoDocumental;
    }
}
