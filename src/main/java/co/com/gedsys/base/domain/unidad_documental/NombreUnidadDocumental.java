package co.com.gedsys.base.domain.unidad_documental;

import co.com.gedsys.base.domain.common.StringValueObject;

import java.util.Optional;

public class NombreUnidadDocumental extends StringValueObject {
    protected NombreUnidadDocumental(String value) {
        super(value);
        judgeNullPolicy();
    }

    @Override
    protected String validate(String value) {
        return Optional.ofNullable(value)
                .filter(v -> v.equals(v.replaceAll("[\\\\/:*?\"<>|]", "")))
                .orElseThrow(() -> new IllegalArgumentException("El nombre contiene caracteres no permitidos"));
    }
}
