package co.com.gedsys.base.infrastructure.data_access.repository;

import co.com.gedsys.base.infrastructure.data_access.TipoDocumentalEntity;
import co.com.gedsys.base.domain.tipologia.EstadoTipoDocumental;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

public interface TipoDocumentalJpaRepository extends JpaRepository<TipoDocumentalEntity, UUID> {

    boolean existsByNombreIgnoreCaseAndEstadoIsNot(String nombre, EstadoTipoDocumental estado);

    List<TipoDocumentalEntity> findByIdIn(List<UUID> ids);
}
