package co.com.gedsys.base.infrastructure.data_access.repository;

import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import co.com.gedsys.base.infrastructure.data_access.DefinicionMetadatoEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Collection;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

public interface DefinicionMetadatoJpaRepository extends JpaRepository<DefinicionMetadatoEntity, UUID> {
    boolean existsByPatron(String patron);

    boolean existsByPatronAndTipo(String patron, TipoMetadatoEnum tipo);

    Set<DefinicionMetadatoEntity> findByPatronIn(Collection<String> patrones);

    Optional<DefinicionMetadatoEntity> findByPatron(String patron);
}
