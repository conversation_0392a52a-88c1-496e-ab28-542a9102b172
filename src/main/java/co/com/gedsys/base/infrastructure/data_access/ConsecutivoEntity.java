package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.consecutivo.EstadoConsecutivo;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.UUID;

@Accessors(chain = true)
@Entity
@Getter
@NoArgsConstructor
@Setter
@Table(name = "consecutivos")
public class ConsecutivoEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column
    private String prefijo;

    @Column
    private String sufijo;

    private Integer contador;

    @Enumerated(EnumType.STRING)
    private EstadoConsecutivo estado;

    @Column(name = "tipo_consecutivo", nullable = false)
    @Enumerated(EnumType.STRING)
    private TipoConsecutivo tipoConsecutivo;

    @Column(name = "tipo_documental_id", insertable = false, updatable = false)
    private UUID tipoDocumentalId;

    @Column(name = "clasificacion_id", insertable = false, updatable = false)
    private UUID clasificacionDocumentalId;

    @OneToOne
    @JoinColumn(name = "tipo_documental_id")
    private TipoDocumentalEntity tipoDocumental;

    @OneToOne
    @JoinColumn(name = "clasificacion_id")
    private ClasificacionDocumentalEntity clasificacionDocumental;
}
