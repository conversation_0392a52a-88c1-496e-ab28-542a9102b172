package co.com.gedsys.base.infrastructure.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.graphql.client.HttpGraphQlClient;
import org.springframework.web.reactive.function.client.WebClient;
@Configuration
public class GraphQLClientConfig {

    private final GraphQLConfigurationProperties graphQLConfigurationProperties;

    public GraphQLClientConfig(GraphQLConfigurationProperties graphQLConfigurationProperties) {
        this.graphQLConfigurationProperties = graphQLConfigurationProperties;
    }

    @Bean
    HttpGraphQlClient httpGraphQlClient() {
        return HttpGraphQlClient.builder(WebClient.builder()
                .baseUrl(graphQLConfigurationProperties.endpoint())
                .defaultHeader("x-hasura-admin-secret", graphQLConfigurationProperties.adminSecretHeader())
                .build())
                .build();
    }
}
