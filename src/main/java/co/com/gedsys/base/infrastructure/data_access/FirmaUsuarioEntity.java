package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.documento.EstadoDeLaFirma;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.UUID;

@Table(name = "firma_usuario")
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class FirmaUsuarioEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Enumerated(EnumType.STRING)
    private EstadoDeLaFirma estado;

    @Column(name = "firmado_en")
    private LocalDateTime firmadoEn;

    @Column(name = "firmante")
    private String firmante;

    @Column(name = "height")
    private Integer height;

    @Column(name = "observaciones")
    private String observaciones;

    @Column(name = "page")
    private Integer page;

    @Column(name = "width")
    private Integer width;

    @Column(name = "x")
    private Integer x;

    @Column(name = "y")
    private Integer y;

    @ManyToOne(optional = false)
    @JoinColumn(name = "documento_id", nullable = false)
    private DocumentoEntity documento;
}
