package co.com.gedsys.base.infrastructure.data_access.repository;

import co.com.gedsys.base.infrastructure.data_access.PlantillaEntity;
import co.com.gedsys.base.domain.plantillas.EstadoPlantilla;
import co.com.gedsys.base.domain.plantillas.TipoPlantilla;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

public interface PlantillaJpaRepository extends JpaRepository<PlantillaEntity, UUID> {
    boolean existsByTituloIgnoreCase(String titulo);

    List<PlantillaEntity> findByTipoDocumentalNombreInIgnoreCaseAndEstado(List<String> nombreTipoDocumental, EstadoPlantilla estado);

    List<PlantillaEntity> findByTipoPlantilla(TipoPlantilla tipoPlantilla);

}
