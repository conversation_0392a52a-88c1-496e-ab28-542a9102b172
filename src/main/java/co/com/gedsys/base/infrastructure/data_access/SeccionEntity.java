package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.organizacion.EstadoSeccion;
import co.com.gedsys.base.domain.organizacion.TipoUnidad;
import jakarta.persistence.*;
import lombok.*;

import java.util.*;

@Entity
@Table(name = "secciones")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SeccionEntity {
    @GeneratedValue(strategy = GenerationType.UUID)
    @Id
    private UUID id;
    @Column(length = 50, nullable = false, columnDefinition = "VARCHAR(50)")
    private String codigo;
    @Column(length = 50, columnDefinition = "VARCHAR(50) DEFAULT 'ACTIVA'")
    @Enumerated(EnumType.STRING)
    private EstadoSeccion estado = EstadoSeccion.ACTIVA;
    @Column(nullable = false, columnDefinition = "VARCHAR(255)")
    private String nombre;
    @Column(length = 50, columnDefinition = "VARCHAR(50)")
    private String responsable;
    @Column(nullable = false, length = 50, columnDefinition = "VARCHAR(50)")
    @Enumerated(EnumType.STRING)
    private TipoUnidad tipo;

    @ManyToOne
    @JoinColumn(name = "padre_id")
    private SeccionEntity padre;

    @OneToMany(mappedBy = "seccion", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<UsuarioSeccionEntity> usuarios = new HashSet<>();

    public void setUsuarios(Set<UsuarioSeccionEntity> usuarios) {
        if (this.usuarios != null) {
            this.usuarios.clear();
            usuarios.forEach(usuario -> usuario.setSeccion(this));
            this.usuarios.addAll(usuarios);
        }
    }
}
