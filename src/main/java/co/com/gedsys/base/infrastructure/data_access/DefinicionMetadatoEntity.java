package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.metadato.enums.FormatoMetadatoEnum;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.proxy.HibernateProxy;

import java.util.Objects;
import java.util.UUID;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "definicion_metadatos", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"patron", "tipo"})
})
public class DefinicionMetadatoEntity {
    @Id
    @Column(nullable = false)
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(nullable = false)
    private String nombre;

    private String descripcion;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private TipoMetadatoEnum tipo;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private FormatoMetadatoEnum formato;

    private String rules;

    @Column(nullable = false)
    private String patron;

    @Override
    public final boolean equals(Object o) {
        if (this == o) return true;
        if (o == null) return false;
        Class<?> oEffectiveClass = o instanceof HibernateProxy ? ((HibernateProxy) o).getHibernateLazyInitializer().getPersistentClass() : o.getClass();
        Class<?> thisEffectiveClass = this instanceof HibernateProxy ? ((HibernateProxy) this).getHibernateLazyInitializer().getPersistentClass() : this.getClass();
        if (thisEffectiveClass != oEffectiveClass) return false;
        DefinicionMetadatoEntity that = (DefinicionMetadatoEntity) o;
        return getId() != null && Objects.equals(getId(), that.getId());
    }

    @Override
    public final int hashCode() {
        return this instanceof HibernateProxy ? ((HibernateProxy) this).getHibernateLazyInitializer().getPersistentClass().hashCode() : getClass().hashCode();
    }
}
