package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.usuario_externo.ExternalUserPropertyType;
import jakarta.persistence.*;
import lombok.*;

import java.util.UUID;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Table(name = "external_users_properties")
@Entity
public class ExternalUsersPropertyEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", nullable = false)
    private UUID id;

    @Column(name = "property_type", nullable = false)
    @Enumerated(EnumType.STRING)
    private ExternalUserPropertyType propertyType;

    @Column(name = "property_name", nullable = false)
    private String propertyName;

    @Column(name = "property_value", nullable = false)
    private String propertyValue;

    private String notes;

    @ManyToOne
    @JoinColumn(name = "external_user_id", nullable = false)
    private ExternalUserEntity owner;

}