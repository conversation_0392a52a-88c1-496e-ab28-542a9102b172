package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.usuario_externo.ExternalUserIdentificationType;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserStatus;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.SQLRestriction;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@Table(name = "external_users")
@Entity
@SQLRestriction(value = "status <> 'ELIMINADO'")
public class ExternalUserEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    private String name;
    @Enumerated(EnumType.STRING)
    private ExternalUserIdentificationType identificationType;
    private String identificationNumber;
    private String salutation;
    private String notes;
    @Enumerated(EnumType.STRING)
    private ExternalUserStatus status;

    @OneToMany(mappedBy = "owner", orphanRemoval = true, cascade = CascadeType.ALL)
    private List<ExternalUsersPropertyEntity> properties = new ArrayList<>();

    public ExternalUserEntity setProperties(List<ExternalUsersPropertyEntity> properties) {
        if (properties == null) {
            return this;
        }
        this.properties = properties;
        properties.forEach(p -> p.setOwner(this));
        return this;
    }
}
