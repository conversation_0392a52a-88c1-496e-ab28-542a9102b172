package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.control_acceso.AccessLevel;
import co.com.gedsys.base.domain.control_acceso.InterestGroup;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.UUID;

@Entity
@Table(name = "access_control")
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
public class AccessControlEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", nullable = false)
    private UUID id;

    @Column(name = "interest_group", nullable = false)
    @Enumerated(EnumType.STRING)
    private InterestGroup interestGroup;

    @Column(name = "detail", nullable = false)
    private String detail;

    @Column
    private String notes;

    @Column
    private String permissions;

    @Column(name = "access_level", nullable = false)
    @Enumerated(EnumType.STRING)
    private AccessLevel accessLevel;

    @Column(nullable = false)
    private String code;

}
