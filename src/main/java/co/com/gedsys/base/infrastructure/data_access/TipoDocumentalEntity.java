package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.tipologia.EstadoTipoDocumental;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.SQLRestriction;

import java.util.UUID;

@Entity
@Table(name = "tipos_documentales")
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@SQLRestriction(value = "estado <> 'ELIMINADO'")
public class TipoDocumentalEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    private String nombre;

    @Enumerated(EnumType.STRING)
    private EstadoTipoDocumental estado;
}