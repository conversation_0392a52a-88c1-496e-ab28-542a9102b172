package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.radicado.EstadoRadicado;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@Table(name = "radicados")
@Entity
public class RadicadoEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(nullable = false)
    private UUID id;

    @Enumerated(EnumType.STRING)
    @Column
    private EstadoRadicado estado;

    @Column
    private Integer numeroRadicado;

    @Column
    private LocalDateTime fechaExpedicion;

    @Column
    private String emisor;

    @Column
    private String observaciones;

    @Column
    private String prefijo;

    @Column
    private String sufijo;

    @Enumerated(EnumType.STRING)
    @Column
    private TipoConsecutivo tipo;

    @Column(name = "prop_page")
    private Integer propPage;

    @Column(name = "prop_x")
    private Integer propX;

    @Column(name = "prop_y")
    private Integer propY;

    @Column(name = "prop_height")
    private Integer propHeight;

    @Column(name = "prop_width")
    private Integer propWidth;

    @Column(name = "prop_rotation_degrees")
    private Integer propRotationDegrees;

    @Column(name = "documento_id", insertable = false, updatable = false)
    private UUID documentoId;

    @Column(name = "consecutivo_id", insertable = false, updatable = false)
    private UUID consecutivoId;

    @ManyToOne
    @JoinColumn(name = "documento_id", nullable = false)
    private DocumentoEntity documento;

    @ManyToOne(cascade = CascadeType.MERGE)
    @JoinColumn(name = "consecutivo_id", nullable = false)
    private ConsecutivoEntity consecutivo;

    @Column(name = "destinatario_id", insertable = false, updatable = false)
    private UUID destinatarioId;

    @ManyToOne
    @JoinColumn(name = "destinatario_id")
    private ExternalUserEntity destinatario;

    // Nuevos campos para correspondencia (Issue #8)
    @Column(name = "remitente_id", insertable = false, updatable = false)
    private UUID remitenteId;

    @ManyToOne
    @JoinColumn(name = "remitente_id")
    private ExternalUserEntity remitente;

    @Column(name = "destino_id", insertable = false, updatable = false)
    private UUID destinoId;

    @ManyToOne
    @JoinColumn(name = "destino_id")
    private SeccionEntity destino;

    @Column(name = "destinatario_interno")
    private String destinatarioInterno;

    @Column(name = "remitente_interno")
    private String remitenteInterno;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
