package co.com.gedsys.base.infrastructure.data_access;

import jakarta.persistence.*;
import lombok.*;

import java.util.LinkedHashSet;
import java.util.Set;
import java.util.UUID;

@Table(name = "clasificaciones_documentales")
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClasificacionDocumentalEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", nullable = false)
    private UUID id;

    @ManyToOne
    @JoinColumn(name = "seccion_id", nullable = false)
    private SeccionEntity seccion;

    @ManyToOne
    @JoinColumn(name = "subserie_id", nullable = false)
    private SerieDocumentalEntity subserie;

    @ManyToMany
    @JoinTable(name = "clasificaciones_tipologicas",
            joinColumns = @JoinColumn(name = "clasificacion_id"),
            inverseJoinColumns = @JoinColumn(name = "tipologia_id"))
    @Builder.Default
    private Set<TipoDocumentalEntity> tiposDocumentales = new LinkedHashSet<>();

}
