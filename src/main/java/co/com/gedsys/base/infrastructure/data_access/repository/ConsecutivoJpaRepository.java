package co.com.gedsys.base.infrastructure.data_access.repository;

import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.infrastructure.data_access.ConsecutivoEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

public interface ConsecutivoJpaRepository extends JpaRepository<ConsecutivoEntity, UUID> {

    List<ConsecutivoEntity> searchAllByTipoDocumental_Id(UUID tipoDocumentalId);
    
    List<ConsecutivoEntity> searchAllByTipoConsecutivo(TipoConsecutivo tipoConsecutivo);

}
