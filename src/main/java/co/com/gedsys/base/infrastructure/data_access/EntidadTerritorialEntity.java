package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.entidad_territorial.TipoEntidadTerritorial;
import jakarta.persistence.*;
import lombok.*;

@Table(name = "entidades_territoriales")
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
public class EntidadTerritorialEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;
    private String codigo;
    private String nombre;
    @Enumerated(EnumType.STRING)
    private TipoEntidadTerritorial tipo;
}
