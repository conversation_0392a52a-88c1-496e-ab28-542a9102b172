package co.com.gedsys.base.infrastructure.config;

import jakarta.validation.constraints.NotBlank;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@Validated
@ConfigurationProperties(prefix = "hasura")
public record GraphQLConfigurationProperties(
        @NotBlank
        String endpoint,
        @NotBlank
        String adminSecretHeader
) {
}
