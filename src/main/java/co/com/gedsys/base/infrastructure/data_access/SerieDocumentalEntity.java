package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.serie_documental.EstadoSerie;
import co.com.gedsys.base.domain.serie_documental.TipoSerie;
import jakarta.persistence.*;
import lombok.*;

import java.util.UUID;

@Entity
@Table(name = "series_documentales")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SerieDocumentalEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", nullable = false)
    private UUID id;

    private String codigo;
    private String nombre;
    @Enumerated(EnumType.STRING)
    private TipoSerie tipo;
    @Enumerated(EnumType.STRING)
    private EstadoSerie estado;

    @ManyToOne
    @JoinColumn(name = "padre_id")
    private SerieDocumentalEntity padre;

}
