package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.SQLRestriction;

import java.util.UUID;

@Table(name = "unidades_documentales")
@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@SQLRestriction(value = "estado <> 'ELIMINADA'")
public class UnidadDocumentalEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    private String nombre;
    private String descripcion;
    @Enumerated(EnumType.STRING)
    private UnidadDocumental.Estado estado;

    @ManyToOne
    @JoinColumn(name = "clasificacion_id")
    private ClasificacionDocumentalEntity clasificacion;
}
