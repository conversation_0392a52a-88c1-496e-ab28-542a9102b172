package co.com.gedsys.base.infrastructure.data_access.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

import co.com.gedsys.base.domain.organizacion.UsuarioSeccion;
import co.com.gedsys.base.infrastructure.data_access.UsuarioSeccionEntity;

@Mapper(componentModel = "spring", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface UsuarioSeccionEntityMapper {

    @Mapping(target = "seccion", ignore = true)
    UsuarioSeccionEntity toEntity(UsuarioSeccion usuarioSeccion);

    @Mapping(target = "seccion", ignore = true)
    UsuarioSeccion toDomainEntity(UsuarioSeccionEntity entity);
}
