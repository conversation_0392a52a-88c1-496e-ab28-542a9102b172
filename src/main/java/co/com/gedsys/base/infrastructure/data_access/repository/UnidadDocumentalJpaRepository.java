package co.com.gedsys.base.infrastructure.data_access.repository;

import co.com.gedsys.base.infrastructure.data_access.UnidadDocumentalEntity;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface UnidadDocumentalJpaRepository extends JpaRepository<UnidadDocumentalEntity, UUID> {

    Optional<UnidadDocumentalEntity> findByIdAndEstado(@NonNull UUID id, @NonNull UnidadDocumental.Estado estado);

    List<UnidadDocumentalEntity> searchAllByEstado(@NonNull UnidadDocumental.Estado estado);

    boolean existsByNombreIgnoreCase(@NonNull String nombre);

    boolean existsByNombreIgnoreCaseAndClasificacion_Id(@NonNull String nombre, @NonNull UUID id);

    List<UnidadDocumentalEntity> queryByClasificacion_TiposDocumentales_Id(@NonNull UUID id);
}