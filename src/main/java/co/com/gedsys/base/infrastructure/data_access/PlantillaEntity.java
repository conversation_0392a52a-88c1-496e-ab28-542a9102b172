package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.plantillas.EstadoPlantilla;
import co.com.gedsys.base.domain.plantillas.TipoPlantilla;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.UuidGenerator;

import java.util.LinkedHashSet;
import java.util.Set;
import java.util.UUID;

@Entity
@Table(name = "Plantillas")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PlantillaEntity {
    @Id
    @UuidGenerator(style = UuidGenerator.Style.RANDOM)
    private UUID id;

    private String titulo;

    @Enumerated(value = EnumType.STRING)
    private EstadoPlantilla estado;

    @Column(name = "tipo_plantilla")
    @Enumerated(value = EnumType.STRING)
    private TipoPlantilla tipoPlantilla;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "metadatos_plantilla",
            joinColumns = @JoinColumn(name = "plantilla_id"),
            inverseJoinColumns = @JoinColumn(name = "definicion_metadato_id"))
    @Builder.Default
    private Set<DefinicionMetadatoEntity> esquemaDeMetadatos = new LinkedHashSet<>();

    @ManyToOne
    @JoinColumn(name = "tipo_documental_id")
    private TipoDocumentalEntity tipoDocumental;
    
    @Column(name = "producido_por_gedsys", nullable = false)
    private boolean producidoPorGedsys;

}
