package co.com.gedsys.base.infrastructure.data_access.repository;

import co.com.gedsys.base.infrastructure.data_access.ClasificacionDocumentalEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.UUID;

public interface ClasificacionDocumentalJpaRepository extends JpaRepository<ClasificacionDocumentalEntity, UUID> {

    boolean existsBySeccion_CodigoAndSubserie_Codigo(String codigoSeccion, String codigoSubSerie);
}