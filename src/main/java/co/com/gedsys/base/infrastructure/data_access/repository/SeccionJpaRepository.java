package co.com.gedsys.base.infrastructure.data_access.repository;

import co.com.gedsys.base.infrastructure.data_access.SeccionEntity;
import co.com.gedsys.base.domain.organizacion.EstadoSeccion;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface SeccionJpaRepository extends JpaRepository<SeccionEntity, UUID> {
    Optional<SeccionEntity> findByCodigo(String id);

    List<SeccionEntity> findByEstado(EstadoSeccion estado);

    Optional<SeccionEntity> findByPadreIsNullAndEstadoIs(EstadoSeccion estado);

    Boolean existsByPadre_Id(UUID padreId);

    List<SeccionEntity> findByUsuarios_Username(String username);

    boolean existsByUsuarios_Seccion_Id(UUID id);

    List<SeccionEntity> findByPadre_IdAndEstado(UUID padreId, EstadoSeccion estado);

    boolean existsByPadre_IdAndEstado(UUID padreId, EstadoSeccion estado);

    boolean existsByIdAndPadre_Estado(UUID seccionId, EstadoSeccion estadoPadre);

}
