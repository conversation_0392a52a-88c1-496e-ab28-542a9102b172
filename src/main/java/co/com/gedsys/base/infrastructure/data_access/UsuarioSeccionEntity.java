package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.organizacion.TipoRelacionUsuarioSeccion;
import jakarta.persistence.*;
import lombok.*;

import java.util.Objects;
import java.util.UUID;

@Entity
@Table(name = "usuarios_secciones")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UsuarioSeccionEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(nullable = false)
    private String username;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private TipoRelacionUsuarioSeccion relacion;

    @ManyToOne
    @JoinColumn(name = "seccion_id", nullable = false)
    private SeccionEntity seccion;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UsuarioSeccionEntity that = (UsuarioSeccionEntity) o;
        return Objects.equals(username, that.username) &&
               relacion == that.relacion &&
               Objects.equals(seccion, that.seccion);
    }

    @Override
    public int hashCode() {
        return Objects.hash(username, relacion, seccion);
    }
}