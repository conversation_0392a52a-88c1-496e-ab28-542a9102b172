package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.documento.EstadoDocumento;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.*;

@Entity
@Getter
@NoArgsConstructor
@Setter
@Table(name = "documentos")
public class DocumentoEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(nullable = false)
    private UUID id;
    private String fileId;
    @Enumerated(EnumType.STRING)
    private EstadoDocumento estado;
    @Column(nullable = false)
    private String titulo;
    @Column
    private String autor;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "unidad_documental_id", updatable = false, insertable = false)
    private UUID unidadDocumentalId;

    @Column(name = "tipo_documental_id", updatable = false, insertable = false)
    private UUID tipoDocumentalId;

    @ManyToOne(optional = false)
    @JoinColumn(name = "unidad_documental_id", nullable = false)
    private UnidadDocumentalEntity unidadDocumental;

    @ManyToOne(optional = false)
    @JoinColumn(name = "tipo_documental_id", nullable = false)
    private TipoDocumentalEntity tipoDocumental;

    @OneToMany(cascade = {CascadeType.ALL}, orphanRemoval = true, mappedBy = "documento")
    private Set<MetadatoEntity> metadatos = new LinkedHashSet<>();

    @OneToMany(mappedBy = "documento", orphanRemoval = true)
    private List<RadicadoEntity> radicados = new ArrayList<>();

    @OneToMany(mappedBy = "documento", cascade = {CascadeType.ALL}, orphanRemoval = true)
    private List<FirmaUsuarioEntity> firmas = new ArrayList<>();

    @OneToMany(mappedBy = "documento", cascade = {CascadeType.ALL}, orphanRemoval = true)
    private List<AprobacionEntity> aprobaciones = new ArrayList<>();

    @OneToMany(mappedBy = "documento", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<AnexoEntity> anexos = new ArrayList<>();

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    public void setFirmas(List<FirmaUsuarioEntity> firmas) {
        if (firmas == null) return;
        this.firmas.addAll(firmas);
        firmas.forEach(f -> f.setDocumento(this));
    }

    public void setAprobaciones(List<AprobacionEntity> aprobaciones) {
        if (aprobaciones == null) return;
        this.aprobaciones.addAll(aprobaciones);
        aprobaciones.forEach(a -> a.setDocumento(this));
    }

    public void setAnexos(List<AnexoEntity> anexos) {
        if (anexos == null) return;
        this.anexos.addAll(anexos);
        anexos.forEach(a -> a.setDocumento(this));
    }

    public void setMetadatos(Collection<MetadatoEntity> metadatos) {
        if (metadatos == null) return;
        this.metadatos.addAll(metadatos);
        metadatos.forEach(m -> m.setDocumento(this));
    }

}
