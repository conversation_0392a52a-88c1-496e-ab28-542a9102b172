package co.com.gedsys.base.infrastructure.data_access.repository;

import co.com.gedsys.base.domain.usuario_externo.ExternalUserIdentificationType;
import co.com.gedsys.base.infrastructure.data_access.ExternalUserEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;
import java.util.UUID;

public interface ExternalUsersJpaRepository extends JpaRepository<ExternalUserEntity, UUID> {
    // Métodos específicos para tipo NA
    boolean existsByNameAndIdentificationType(String name, ExternalUserIdentificationType identificationType);
    Optional<ExternalUserEntity> findByNameAndIdentificationType(String name, ExternalUserIdentificationType identificationType);
}