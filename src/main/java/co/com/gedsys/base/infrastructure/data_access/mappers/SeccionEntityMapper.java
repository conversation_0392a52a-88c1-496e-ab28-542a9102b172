package co.com.gedsys.base.infrastructure.data_access.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.infrastructure.data_access.SeccionEntity;

@Mapper(componentModel = "spring", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {UsuarioSeccionEntityMapper.class})
public interface SeccionEntityMapper {

    SeccionEntity toEntity(Seccion seccion);

    @Mapping(target = "hijos", ignore = true)
    @Mapping(target = "usuarios", source = "usuarios")
    Seccion toDomainEntity(SeccionEntity entity);
}