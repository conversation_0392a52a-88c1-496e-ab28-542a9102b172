package co.com.gedsys.base.infrastructure.data_access;

import co.com.gedsys.base.domain.documento.ResultadoAprobacion;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.UUID;

@Table(name = "aprobaciones")
@Entity
@Getter
@Setter
@NoArgsConstructor
public class AprobacionEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "aprobador", nullable = false)
    private String aprobador;

    @Column(name = "observaciones")
    private String observaciones;

    @Enumerated(EnumType.STRING)
    @Column(name = "estado", nullable = false)
    private ResultadoAprobacion estado;

    @Column(name = "aprobado_en")
    private LocalDateTime aprobadoEn;

    @ManyToOne
    @JoinColumn(name = "documento_id")
    private DocumentoEntity documento;
}
