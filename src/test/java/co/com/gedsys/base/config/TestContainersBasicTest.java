package co.com.gedsys.base.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Prueba básica para verificar que TestContainers funciona correctamente.
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@Testcontainers
@ActiveProfiles("test")
public class TestContainersBasicTest {

    /**
     * Contenedor de PostgreSQL para la prueba.
     */
    @Container
    public static PostgreSQLContainer<?> postgreSQLContainer = new PostgreSQLContainer<>("postgres:16-alpine")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");

    /**
     * Configuración dinámica de propiedades para Spring Boot.
     */
    @DynamicPropertySource
    static void registerPgProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgreSQLContainer::getJdbcUrl);
        registry.add("spring.datasource.username", postgreSQLContainer::getUsername);
        registry.add("spring.datasource.password", postgreSQLContainer::getPassword);
        // Deshabilitar Flyway para esta prueba
        registry.add("spring.flyway.enabled", () -> "false");
        // Configurar Hibernate para crear el esquema
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
    }

    @Test
    public void testContainerRunning() {
        assertTrue(postgreSQLContainer.isRunning(), "El contenedor de PostgreSQL debería estar en ejecución");
        System.out.println("URL JDBC: " + postgreSQLContainer.getJdbcUrl());
        System.out.println("Usuario: " + postgreSQLContainer.getUsername());
        System.out.println("Contraseña: " + postgreSQLContainer.getPassword());
    }
}
