package co.com.gedsys.base.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Prueba de integración simple que combina Spring Boot y TestContainers.
 */
@SpringBootTest
@Testcontainers
public class SpringBootTestContainersTest {

    @Container
    private static final PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:16-alpine")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Configuración dinámica de propiedades para Spring Boot.
     */
    @DynamicPropertySource
    static void registerPgProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        // Deshabilitar Flyway para esta prueba
        registry.add("spring.flyway.enabled", () -> "false");
        // Configurar Hibernate para crear el esquema
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
    }

    /**
     * Prueba que verifica que el contenedor de PostgreSQL está en ejecución
     * y que se puede conectar a la base de datos.
     */
    @Test
    public void testDatabaseConnection() {
        assertTrue(postgres.isRunning(), "El contenedor de PostgreSQL debería estar en ejecución");

        // Crear una tabla de prueba
        jdbcTemplate.execute("CREATE TABLE test_table (id SERIAL PRIMARY KEY, name VARCHAR(255))");

        // Insertar datos
        jdbcTemplate.update("INSERT INTO test_table (name) VALUES (?)", "Test Name");

        // Consultar datos
        Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM test_table", Integer.class);

        // Verificar resultado
        assertEquals(1, count, "Debería haber un registro en la tabla");

        System.out.println("Conexión a la base de datos exitosa");
    }
}
