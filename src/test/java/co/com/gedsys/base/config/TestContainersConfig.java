package co.com.gedsys.base.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;

/**
 * Configuración de TestContainers para pruebas de integración.
 * Esta clase configura un contenedor de PostgreSQL para las pruebas.
 */
@TestConfiguration
public class TestContainersConfig {

    /**
     * Contenedor de PostgreSQL estático para ser compartido entre todas las pruebas.
     */
    @Container
    private static final PostgreSQLContainer<?> POSTGRES_CONTAINER = 
        new PostgreSQLContainer<>("postgres:16-alpine")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test")
            .withReuse(true);

    static {
        // Iniciar el contenedor
        POSTGRES_CONTAINER.start();
    }

    /**
     * Configura dinámicamente las propiedades de Spring para usar el contenedor.
     * 
     * @param registry Registro de propiedades dinámicas
     */
    @DynamicPropertySource
    static void registerPgProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", POSTGRES_CONTAINER::getJdbcUrl);
        registry.add("spring.datasource.username", POSTGRES_CONTAINER::getUsername);
        registry.add("spring.datasource.password", POSTGRES_CONTAINER::getPassword);
    }
}
