package co.com.gedsys.base.config;

import org.junit.jupiter.api.Test;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Prueba simple que usa solo TestContainers sin Spring Boot.
 */
@Testcontainers
public class SimpleTestContainersTest {

    /**
     * Contenedor de PostgreSQL para la prueba.
     */
    @Container
    public static PostgreSQLContainer<?> postgreSQLContainer = new PostgreSQLContainer<>("postgres:16-alpine")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");

    /**
     * Prueba que verifica que el contenedor de PostgreSQL está en ejecución.
     */
    @Test
    public void testContainerRunning() {
        assertTrue(postgreSQLContainer.isRunning(), "El contenedor de PostgreSQL debería estar en ejecución");
        System.out.println("URL JDBC: " + postgreSQLContainer.getJdbcUrl());
        System.out.println("Usuario: " + postgreSQLContainer.getUsername());
        System.out.println("Contraseña: " + postgreSQLContainer.getPassword());
    }
}