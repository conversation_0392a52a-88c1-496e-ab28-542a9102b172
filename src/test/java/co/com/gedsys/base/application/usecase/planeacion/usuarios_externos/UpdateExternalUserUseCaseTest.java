package co.com.gedsys.base.application.usecase.planeacion.usuarios_externos;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.mapper.ExternalUsersAppMapper;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.UpdateExternalUserCommand;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserIdentificationType;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("Pruebas para UpdateExternalUserUseCase")
class UpdateExternalUserUseCaseTest {

    @Mock
    private ExternalUsersRepository repository;
    
    @Mock
    private ExternalUsersAppMapper mapper;
    
    private UpdateExternalUserUseCase useCase;
    private UUID userId;

    @BeforeEach
    void setUp() {
        useCase = new UpdateExternalUserUseCase(repository, mapper);
        userId = UUID.randomUUID();
    }

    @Nested
    @DisplayName("con cambios a tipo NA")
    class ConCambiosATipoNA {

        @Test
        @DisplayName("debe actualizar usuario de CC a NA exitosamente")
        void deberiaActualizarUsuarioDeCCANAExitosamente() {
            // Arrange
            var existingUser = new ExternalUser("Usuario Test", "CC", "12345678");
            existingUser.setId(userId);
            
            var command = new UpdateExternalUserCommand(
                userId,
                "Usuario Test",
                "Saludo",
                "NA", // Cambio a tipo NA
                "cualquier-valor", // Será normalizado a null
                "Notas",
                "ACTIVO",
                new ArrayList<>()
            );
            
            var updatedUser = new ExternalUser("Usuario Test", "NA", null);
            updatedUser.setId(userId);
            
            when(repository.findById(userId)).thenReturn(Optional.of(existingUser));
            when(mapper.toDomain(any(UpdateExternalUserCommand.class), eq(existingUser))).thenReturn(updatedUser);
            when(repository.findByNameAndIdentificationType("Usuario Test", ExternalUserIdentificationType.NA))
                .thenReturn(Optional.empty());
            when(repository.update(userId, updatedUser)).thenReturn(updatedUser);
            when(mapper.toDto(updatedUser)).thenReturn(null);
            
            // Act & Assert
            assertDoesNotThrow(() -> useCase.execute(command));
            
            // Verify que se validó unicidad para tipo NA
            verify(repository).findByNameAndIdentificationType("Usuario Test", ExternalUserIdentificationType.NA);
        }

        @Test
        @DisplayName("debe lanzar excepción si ya existe otro usuario con mismo nombre y tipo NA")
        void deberiaLanzarExcepcionSiYaExisteOtroUsuarioConMismoNombreYTipoNA() {
            // Arrange
            var existingUser = new ExternalUser("Usuario Test", "CC", "12345678");
            existingUser.setId(userId);
            
            var otherUserId = UUID.randomUUID();
            var otherUser = new ExternalUser("Usuario Test", "NA", null);
            otherUser.setId(otherUserId);
            
            var command = new UpdateExternalUserCommand(
                userId,
                "Usuario Test",
                "Saludo",
                "NA",
                null,
                "Notas",
                "ACTIVO",
                new ArrayList<>()
            );
            
            var updatedUser = new ExternalUser("Usuario Test", "NA", null);
            updatedUser.setId(userId);
            
            when(repository.findById(userId)).thenReturn(Optional.of(existingUser));
            when(mapper.toDomain(any(UpdateExternalUserCommand.class), eq(existingUser))).thenReturn(updatedUser);
            when(repository.findByNameAndIdentificationType("Usuario Test", ExternalUserIdentificationType.NA))
                .thenReturn(Optional.of(otherUser));
            
            // Act & Assert
            var exception = assertThrows(EntityAlreadyExistsException.class, 
                () -> useCase.execute(command));
            assertTrue(exception.getMessage().contains("Ya existe otro usuario con nombre 'Usuario Test' y tipo NA"));
        }

        @Test
        @DisplayName("debe permitir actualizar usuario NA a sí mismo")
        void deberiaPermitirActualizarUsuarioNAASiMismo() {
            // Arrange
            var existingUser = new ExternalUser("Usuario NA", "NA", null);
            existingUser.setId(userId);
            
            var command = new UpdateExternalUserCommand(
                userId,
                "Usuario NA Actualizado", // Cambio de nombre
                "Saludo",
                "NA",
                null,
                "Notas",
                "ACTIVO",
                new ArrayList<>()
            );
            
            var updatedUser = new ExternalUser("Usuario NA Actualizado", "NA", null);
            updatedUser.setId(userId);
            
            when(repository.findById(userId)).thenReturn(Optional.of(existingUser));
            when(mapper.toDomain(any(UpdateExternalUserCommand.class), eq(existingUser))).thenReturn(updatedUser);
            when(repository.findByNameAndIdentificationType("Usuario NA Actualizado", ExternalUserIdentificationType.NA))
                .thenReturn(Optional.empty());
            when(repository.update(userId, updatedUser)).thenReturn(updatedUser);
            when(mapper.toDto(updatedUser)).thenReturn(null);
            
            // Act & Assert
            assertDoesNotThrow(() -> useCase.execute(command));
        }
    }

    @Nested
    @DisplayName("con cambios desde tipo NA")
    class ConCambiosDesdeTipoNA {

        @Test
        @DisplayName("debe actualizar usuario de NA a CC exitosamente")
        void deberiaActualizarUsuarioDeNAACCExitosamente() {
            // Arrange
            var existingUser = new ExternalUser("Usuario NA", "NA", null);
            existingUser.setId(userId);
            
            var command = new UpdateExternalUserCommand(
                userId,
                "Usuario NA",
                "Saludo",
                "CC", // Cambio a tipo CC
                "12345678", // Ahora requiere número
                "Notas",
                "ACTIVO",
                new ArrayList<>()
            );
            
            var updatedUser = new ExternalUser("Usuario NA", "CC", "12345678");
            updatedUser.setId(userId);
            
            when(repository.findById(userId)).thenReturn(Optional.of(existingUser));
            when(mapper.toDomain(any(UpdateExternalUserCommand.class), eq(existingUser))).thenReturn(updatedUser);
            when(repository.update(userId, updatedUser)).thenReturn(updatedUser);
            when(mapper.toDto(updatedUser)).thenReturn(null);
            
            // Act & Assert
            assertDoesNotThrow(() -> useCase.execute(command));
            
            // Verify que NO se validó unicidad por nombre (ya no es tipo NA)
            verify(repository, never()).findByNameAndIdentificationType(anyString(), eq(ExternalUserIdentificationType.NA));
        }
    }

    @Nested
    @DisplayName("con otros tipos de identificación")
    class ConOtrosTiposDeIdentificacion {

        @Test
        @DisplayName("debe actualizar usuario tipo CC normalmente")
        void deberiaActualizarUsuarioTipoCCNormalmente() {
            // Arrange
            var existingUser = new ExternalUser("Usuario CC", "CC", "12345678");
            existingUser.setId(userId);
            
            var command = new UpdateExternalUserCommand(
                userId,
                "Usuario CC Actualizado",
                "Saludo",
                "CC",
                "87654321", // Cambio de número
                "Notas",
                "ACTIVO",
                new ArrayList<>()
            );
            
            var updatedUser = new ExternalUser("Usuario CC Actualizado", "CC", "87654321");
            updatedUser.setId(userId);
            
            when(repository.findById(userId)).thenReturn(Optional.of(existingUser));
            when(mapper.toDomain(any(UpdateExternalUserCommand.class), eq(existingUser))).thenReturn(updatedUser);
            when(repository.update(userId, updatedUser)).thenReturn(updatedUser);
            when(mapper.toDto(updatedUser)).thenReturn(null);
            
            // Act & Assert
            assertDoesNotThrow(() -> useCase.execute(command));
            
            // Verify que NO se validó unicidad por nombre (no es tipo NA)
            verify(repository, never()).findByNameAndIdentificationType(anyString(), any());
        }
    }

    @Nested
    @DisplayName("validaciones generales")
    class ValidacionesGenerales {

        @Test
        @DisplayName("debe lanzar excepción si usuario no existe")
        void deberiaLanzarExcepcionSiUsuarioNoExiste() {
            // Arrange
            var command = new UpdateExternalUserCommand(
                userId,
                "Usuario",
                "Saludo",
                "CC",
                "12345678",
                "Notas",
                "ACTIVO",
                new ArrayList<>()
            );
            
            when(repository.findById(userId)).thenReturn(Optional.empty());
            
            // Act & Assert
            var exception = assertThrows(EntityNotExistsException.class, 
                () -> useCase.execute(command));
            assertTrue(exception.getMessage().contains("No se encontró el usuario con id " + userId));
        }

        @Test
        @DisplayName("debe normalizar número a null para tipo NA")
        void deberiaNormalizarNumeroANullParaTipoNA() {
            // Arrange
            var existingUser = new ExternalUser("Usuario Test", "CC", "12345678");
            existingUser.setId(userId);
            
            var command = new UpdateExternalUserCommand(
                userId,
                "Usuario Test",
                "Saludo",
                "NA",
                "valor-a-normalizar", // Debe ser normalizado a null
                "Notas",
                "ACTIVO",
                new ArrayList<>()
            );
            
            var updatedUser = new ExternalUser("Usuario Test", "NA", null);
            updatedUser.setId(userId);

            when(repository.findById(userId)).thenReturn(Optional.of(existingUser));
            when(mapper.toDomain(any(UpdateExternalUserCommand.class), any())).thenReturn(updatedUser);
            when(repository.findByNameAndIdentificationType(anyString(), any())).thenReturn(Optional.empty());
            when(repository.update(any(), any())).thenReturn(updatedUser);
            when(mapper.toDto(any())).thenReturn(null);
            
            // Act
            useCase.execute(command);
            
            // Assert - Verificar que el mapper recibió un comando con número null
            verify(mapper).toDomain(argThat((UpdateExternalUserCommand cmd) ->
                cmd.identificationType().equals("NA") && cmd.identificationNumber() == null
            ), any(ExternalUser.class));
        }
    }
}
