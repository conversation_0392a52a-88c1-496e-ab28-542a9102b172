package co.com.gedsys.base.application.usecase.planeacion.consecutivos;

import co.com.gedsys.base.application.dto.ConsecutivoDTO;
import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CrearConsecutivoUseCaseTest {

    @Mock
    private ConsecutivoRepository consecutivoRepository;

    @Mock
    private TipoDocumentalRepository tipoDocumentalRepository;

    @Mock
    private ClasificacionDocumentalRepository clasificacionDocumentalRepository;

    @Mock
    private ConsecutivoMapper mapper;

    @InjectMocks
    private CrearConsecutivoUseCase useCase;

    @Captor
    private ArgumentCaptor<Consecutivo> consecutivoCaptor;

    private UUID tipoDocumentalId;
    private UUID clasificacionDocumentalId;
    private TipoDocumental tipoDocumental;
    private ClasificacionDocumental clasificacionDocumental;
    private ConsecutivoDTO consecutivoDTO;

    @BeforeEach
    void setUp() {
        tipoDocumentalId = UUID.randomUUID();
        clasificacionDocumentalId = UUID.randomUUID();

        tipoDocumental = new TipoDocumental("Tipo Documental Test");
        tipoDocumental.setId(tipoDocumentalId);

        Seccion seccion = mock(Seccion.class);
        SerieDocumental serieDocumental = mock(SerieDocumental.class);
        clasificacionDocumental = new ClasificacionDocumental(seccion, serieDocumental);
        clasificacionDocumental.setId(clasificacionDocumentalId);

        consecutivoDTO = new ConsecutivoDTO(
            UUID.randomUUID().toString(),
            "RECEPCION",
            "ACTIVO",
            "PRE",
            "SUF",
            1,
            null,
            null
        );
    }

    @Test
    @DisplayName("Debe crear consecutivo regular cuando no existe ninguno del mismo tipo")
    void debeCrearConsecutivoRegularCuandoNoExisteDelMismoTipo() {
        // Arrange
        CrearConsecutivoCommand command = new CrearConsecutivoCommand(
                TipoConsecutivo.RECEPCION,
                "PRE",
                "SUF",
                1,
                null,
                null
        );

        Consecutivo consecutivoGuardado = new Consecutivo("PRE", "SUF", 1, TipoConsecutivo.RECEPCION);
        consecutivoGuardado.setId(UUID.randomUUID());

        when(consecutivoRepository.buscarConsecutivoPorTipo(TipoConsecutivo.RECEPCION))
                .thenReturn(Collections.emptyList());
        when(consecutivoRepository.save(any(Consecutivo.class))).thenReturn(consecutivoGuardado);
        when(mapper.toDto(any(Consecutivo.class))).thenReturn(consecutivoDTO);

        // Act
        ConsecutivoDTO resultado = useCase.execute(command);

        // Assert
        verify(consecutivoRepository).buscarConsecutivoPorTipo(TipoConsecutivo.RECEPCION);
        verify(consecutivoRepository).save(consecutivoCaptor.capture());
        
        Consecutivo consecutivoGuardadoCapturado = consecutivoCaptor.getValue();
        assertEquals(TipoConsecutivo.RECEPCION, consecutivoGuardadoCapturado.getTipoConsecutivo());
        assertEquals("PRE", consecutivoGuardadoCapturado.getPrefijo());
        assertEquals("SUF", consecutivoGuardadoCapturado.getSufijo());
        assertEquals(1, consecutivoGuardadoCapturado.getContador());

        assertNotNull(resultado);
    }

    @Test
    @DisplayName("Debe lanzar ConsecutivoUnicoTipoException cuando ya existe consecutivo del mismo tipo")
    void debeLanzarConsecutivoUnicoTipoExceptionCuandoYaExisteDelMismoTipo() {
        // Arrange
        CrearConsecutivoCommand command = new CrearConsecutivoCommand(
                TipoConsecutivo.RECEPCION,
                "PRE",
                "SUF",
                1,
                null,
                null
        );

        Consecutivo consecutivoExistente = new Consecutivo("OTRO", "OTRO", 10, TipoConsecutivo.RECEPCION);
        when(consecutivoRepository.buscarConsecutivoPorTipo(TipoConsecutivo.RECEPCION))
                .thenReturn(List.of(consecutivoExistente));

        // Act & Assert
        assertThrows(ConsecutivoUnicoTipoException.class, () -> useCase.execute(command));
        verify(consecutivoRepository).buscarConsecutivoPorTipo(TipoConsecutivo.RECEPCION);
        verify(consecutivoRepository, never()).save(any());
        verify(mapper, never()).toDto(any());
    }

    @Test
    @DisplayName("Debe crear consecutivo de producción cuando no existe duplicado exacto")
    void debeCrearConsecutivoProduccionCuandoNoExisteDuplicadoExacto() {
        // Arrange
        CrearConsecutivoCommand command = new CrearConsecutivoCommand(
                TipoConsecutivo.PRODUCCION,
                "PRE",
                "SUF",
                1,
                tipoDocumentalId,
                clasificacionDocumentalId
        );

        Consecutivo consecutivoGuardado = new Consecutivo("PRE", "SUF", 1, tipoDocumental, clasificacionDocumental);
        consecutivoGuardado.setId(UUID.randomUUID());

        when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.of(tipoDocumental));
        when(clasificacionDocumentalRepository.findById(clasificacionDocumentalId)).thenReturn(Optional.of(clasificacionDocumental));
        when(consecutivoRepository.findByExample(any(Consecutivo.class))).thenReturn(Optional.empty());
        when(consecutivoRepository.save(any(Consecutivo.class))).thenReturn(consecutivoGuardado);
        when(mapper.toDto(any(Consecutivo.class))).thenReturn(consecutivoDTO);

        // Act
        ConsecutivoDTO resultado = useCase.execute(command);

        // Assert
        verify(tipoDocumentalRepository).findById(tipoDocumentalId);
        verify(clasificacionDocumentalRepository).findById(clasificacionDocumentalId);
        verify(consecutivoRepository).findByExample(any(Consecutivo.class));
        verify(consecutivoRepository).save(any(Consecutivo.class));

        assertNotNull(resultado);
    }

    @Test
    @DisplayName("Debe lanzar ConsecutivoAlreadyExistsException cuando existe consecutivo de producción exacto")
    void debeLanzarConsecutivoAlreadyExistsExceptionCuandoExisteConsecutivoProduccionExacto() {
        // Arrange
        CrearConsecutivoCommand command = new CrearConsecutivoCommand(
                TipoConsecutivo.PRODUCCION,
                "PRE",
                "SUF",
                1,
                tipoDocumentalId,
                clasificacionDocumentalId
        );

        Consecutivo consecutivoExistente = new Consecutivo("PRE", "SUF", 1, tipoDocumental, clasificacionDocumental);
        
        when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.of(tipoDocumental));
        when(clasificacionDocumentalRepository.findById(clasificacionDocumentalId)).thenReturn(Optional.of(clasificacionDocumental));
        when(consecutivoRepository.findByExample(any(Consecutivo.class))).thenReturn(Optional.of(consecutivoExistente));

        // Act & Assert
        assertThrows(ConsecutivoAlreadyExistsException.class, () -> useCase.execute(command));
        verify(consecutivoRepository).findByExample(any(Consecutivo.class));
        verify(consecutivoRepository, never()).save(any());
        verify(mapper, never()).toDto(any());
    }

    @Test
    @DisplayName("Debe crear consecutivo de producción con clasificación documental null")
    void debeCrearConsecutivoProduccionConClasificacionDocumentalNull() {
        // Arrange
        CrearConsecutivoCommand command = new CrearConsecutivoCommand(
                TipoConsecutivo.PRODUCCION,
                "PRE",
                "SUF",
                1,
                tipoDocumentalId,
                clasificacionDocumentalId
        );

        Consecutivo consecutivoGuardado = new Consecutivo("PRE", "SUF", 1, tipoDocumental, null);
        consecutivoGuardado.setId(UUID.randomUUID());

        when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.of(tipoDocumental));
        when(clasificacionDocumentalRepository.findById(clasificacionDocumentalId)).thenReturn(Optional.empty());
        when(consecutivoRepository.findByExample(any(Consecutivo.class))).thenReturn(Optional.empty());
        when(consecutivoRepository.save(any(Consecutivo.class))).thenReturn(consecutivoGuardado);
        when(mapper.toDto(any(Consecutivo.class))).thenReturn(consecutivoDTO);

        // Act
        ConsecutivoDTO resultado = useCase.execute(command);

        // Assert
        verify(tipoDocumentalRepository).findById(tipoDocumentalId);
        verify(clasificacionDocumentalRepository).findById(clasificacionDocumentalId);
        verify(consecutivoRepository).findByExample(any(Consecutivo.class));
        verify(consecutivoRepository).save(any(Consecutivo.class));

        assertNotNull(resultado);
    }
} 
