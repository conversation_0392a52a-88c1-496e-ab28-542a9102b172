package co.com.gedsys.base.application.usecase.planeacion.series_documentales;

import co.com.gedsys.base.application.dto.SerieDocumentalDto;
import co.com.gedsys.base.application.mapper.SerieDocumentalMapper;
import co.com.gedsys.base.domain.serie_documental.EstadoSerie;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import co.com.gedsys.base.domain.serie_documental.SerieDocumentalRepository;
import co.com.gedsys.base.domain.serie_documental.SeriePadreInactivaException;
import co.com.gedsys.base.domain.serie_documental.TipoSerie;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ActivarSerieDocumentalUseCaseTest {

    @Mock
    private SerieDocumentalRepository serieDocumentalRepository;

    @Mock
    private SerieDocumentalMapper mapper;

    @InjectMocks
    private ActivarSerieDocumentalUseCase useCase;

    private UUID serieId;
    private SerieDocumental serieDocumental;
    private SerieDocumentalDto serieDocumentalDto;
    private ActivarSerieDocumentalCommand command;

    @BeforeEach
    void setUp() {
        serieId = UUID.randomUUID();
        serieDocumental = new SerieDocumental("100", "Serie de prueba");
        serieDocumental.setId(serieId);
        serieDocumental.setEstado(EstadoSerie.INACTIVA);
        
        serieDocumentalDto = new SerieDocumentalDto(
            serieId,
            "100",
            "Serie de prueba",
            "SERIE",
            "ACTIVA",
            "",
            ""
        );
        
        command = new ActivarSerieDocumentalCommand(serieId);
    }

    @Test
    @DisplayName("Debe activar una serie documental correctamente")
    void debeActivarSerieDocumentalCorrectamente() {
        // Arrange
        when(serieDocumentalRepository.findById(serieId)).thenReturn(Optional.of(serieDocumental));
        when(mapper.toDto(serieDocumental)).thenReturn(serieDocumentalDto);
        
        // Act
        SerieDocumentalDto resultado = useCase.ejecutar(command);
        
        // Assert
        verify(serieDocumentalRepository).guardar(serieDocumental);
        assertEquals(serieDocumentalDto, resultado);
        assertEquals(EstadoSerie.ACTIVA, serieDocumental.getEstado());
    }

    @Test
    @DisplayName("Debe lanzar excepción cuando la serie no existe")
    void debeLanzarExcepcionCuandoSerieNoExiste() {
        // Arrange
        when(serieDocumentalRepository.findById(serieId)).thenReturn(Optional.empty());
        
        // Act & Assert
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> useCase.ejecutar(command)
        );
        
        assertEquals("Serie documental no encontrada", exception.getMessage());
        verify(serieDocumentalRepository, never()).guardar(any());
    }

    @Test
    @DisplayName("Debe lanzar excepción cuando la serie padre está inactiva")
    void debeLanzarExcepcionCuandoSeriePadreInactiva() {
        // Arrange
        serieDocumental.setTipo(TipoSerie.SUBSERIE);
        when(serieDocumentalRepository.findById(serieId)).thenReturn(Optional.of(serieDocumental));
        when(serieDocumentalRepository.tienePadreInactivo(serieId)).thenReturn(true);
        
        // Act & Assert
        SeriePadreInactivaException exception = assertThrows(
            SeriePadreInactivaException.class,
            () -> useCase.ejecutar(command)
        );
        
        assertEquals("La serie padre se encuentra inactiva y no se puede activar la subserie.", exception.getMessage());
        verify(serieDocumentalRepository, never()).guardar(any());
    }
}