package co.com.gedsys.base.application.usecase.planeacion.series_documentales;

import co.com.gedsys.base.application.dto.SerieDocumentalDto;
import co.com.gedsys.base.application.mapper.SerieDocumentalMapper;
import co.com.gedsys.base.domain.serie_documental.EstadoSerie;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import co.com.gedsys.base.domain.serie_documental.SerieDocumentalRepository;
import co.com.gedsys.base.domain.serie_documental.SerieTieneDependenciasActivasException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InactivarSerieDocumentalUseCaseTest {

    @Mock
    private SerieDocumentalRepository serieDocumentalRepository;

    @Mock
    private SerieDocumentalMapper mapper;

    @InjectMocks
    private InactivarSerieDocumentalUseCase useCase;

    private UUID serieId;
    private SerieDocumental serieDocumental;
    private SerieDocumentalDto serieDocumentalDto;
    private InactivarSerieDocumentalCommand command;

    @BeforeEach
    void setUp() {
        serieId = UUID.randomUUID();
        serieDocumental = new SerieDocumental("100", "Serie de prueba");
        serieDocumental.setId(serieId);
        // Por defecto el estado es ACTIVA según el constructor
        
        serieDocumentalDto = new SerieDocumentalDto(
            serieId,
            "100",
            "Serie de prueba",
            "SERIE",
            "INACTIVA",
            "",
            ""
        );
        
        command = new InactivarSerieDocumentalCommand(serieId);
    }

    @Test
    @DisplayName("Debe inactivar una serie documental correctamente")
    void debeInactivarSerieDocumentalCorrectamente() {
        // Arrange
        when(serieDocumentalRepository.findById(serieId)).thenReturn(Optional.of(serieDocumental));
        when(serieDocumentalRepository.tieneHijasActivas(serieId)).thenReturn(false);
        when(serieDocumentalRepository.tieneClasificacionesDocumentalesAsociadas(serieId)).thenReturn(false);
        when(serieDocumentalRepository.tieneDocumentosAsociados(serieId)).thenReturn(false);
        when(serieDocumentalRepository.tieneUnidadesDocumentalesAsociadas(serieId)).thenReturn(false);
        when(mapper.toDto(serieDocumental)).thenReturn(serieDocumentalDto);
        
        // Act
        SerieDocumentalDto resultado = useCase.ejecutar(command);
        
        // Assert
        verify(serieDocumentalRepository).guardar(serieDocumental);
        assertEquals(serieDocumentalDto, resultado);
        assertEquals(EstadoSerie.INACTIVA, serieDocumental.getEstado());
    }

    @Test
    @DisplayName("Debe lanzar excepción cuando la serie no existe")
    void debeLanzarExcepcionCuandoSerieNoExiste() {
        // Arrange
        when(serieDocumentalRepository.findById(serieId)).thenReturn(Optional.empty());
        
        // Act & Assert
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> useCase.ejecutar(command)
        );
        
        assertEquals("Serie documental no encontrada", exception.getMessage());
        verify(serieDocumentalRepository, never()).guardar(any());
    }

    @Test
    @DisplayName("Debe lanzar excepción cuando la serie tiene hijas activas")
    void debeLanzarExcepcionCuandoSerieTieneHijasActivas() {
        // Arrange
        when(serieDocumentalRepository.findById(serieId)).thenReturn(Optional.of(serieDocumental));
        when(serieDocumentalRepository.tieneHijasActivas(serieId)).thenReturn(true);
        
        // Act & Assert
        SerieTieneDependenciasActivasException exception = assertThrows(
            SerieTieneDependenciasActivasException.class,
            () -> useCase.ejecutar(command)
        );
        
        assertEquals("La serie documental tiene dependencias activas y no puede ser inactivada.", exception.getMessage());
        verify(serieDocumentalRepository, never()).guardar(any());
    }

    @Test
    @DisplayName("Debe lanzar excepción cuando la serie tiene clasificaciones documentales asociadas")
    void debeLanzarExcepcionCuandoSerieTieneClasificacionesDocumentales() {
        // Arrange
        when(serieDocumentalRepository.findById(serieId)).thenReturn(Optional.of(serieDocumental));
        when(serieDocumentalRepository.tieneHijasActivas(serieId)).thenReturn(false);
        when(serieDocumentalRepository.tieneClasificacionesDocumentalesAsociadas(serieId)).thenReturn(true);
        
        // Act & Assert
        SerieTieneDependenciasActivasException exception = assertThrows(
            SerieTieneDependenciasActivasException.class,
            () -> useCase.ejecutar(command)
        );
        
        assertEquals("La serie documental tiene dependencias activas y no puede ser inactivada.", exception.getMessage());
        verify(serieDocumentalRepository, never()).guardar(any());
    }

    @Test
    @DisplayName("Debe lanzar excepción cuando la serie tiene documentos asociados")
    void debeLanzarExcepcionCuandoSerieTieneDocumentosAsociados() {
        // Arrange
        when(serieDocumentalRepository.findById(serieId)).thenReturn(Optional.of(serieDocumental));
        when(serieDocumentalRepository.tieneHijasActivas(serieId)).thenReturn(false);
        when(serieDocumentalRepository.tieneClasificacionesDocumentalesAsociadas(serieId)).thenReturn(false);
        when(serieDocumentalRepository.tieneDocumentosAsociados(serieId)).thenReturn(true);
        
        // Act & Assert
        SerieTieneDependenciasActivasException exception = assertThrows(
            SerieTieneDependenciasActivasException.class,
            () -> useCase.ejecutar(command)
        );
        
        assertEquals("La serie documental tiene dependencias activas y no puede ser inactivada.", exception.getMessage());
        verify(serieDocumentalRepository, never()).guardar(any());
    }

    @Test
    @DisplayName("Debe lanzar excepción cuando la serie tiene unidades documentales asociadas")
    void debeLanzarExcepcionCuandoSerieTieneUnidadesDocumentalesAsociadas() {
        // Arrange
        when(serieDocumentalRepository.findById(serieId)).thenReturn(Optional.of(serieDocumental));
        when(serieDocumentalRepository.tieneHijasActivas(serieId)).thenReturn(false);
        when(serieDocumentalRepository.tieneClasificacionesDocumentalesAsociadas(serieId)).thenReturn(false);
        when(serieDocumentalRepository.tieneDocumentosAsociados(serieId)).thenReturn(false);
        when(serieDocumentalRepository.tieneUnidadesDocumentalesAsociadas(serieId)).thenReturn(true);
        
        // Act & Assert
        SerieTieneDependenciasActivasException exception = assertThrows(
            SerieTieneDependenciasActivasException.class,
            () -> useCase.ejecutar(command)
        );
        
        assertEquals("La serie documental tiene dependencias activas y no puede ser inactivada.", exception.getMessage());
        verify(serieDocumentalRepository, never()).guardar(any());
    }
}