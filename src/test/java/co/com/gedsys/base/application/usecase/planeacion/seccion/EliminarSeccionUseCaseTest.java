package co.com.gedsys.base.application.usecase.planeacion.seccion;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;

@ExtendWith(MockitoExtension.class)
class EliminarSeccionUseCaseTest {

    @Mock
    private SeccionRepository repository;

    @InjectMocks
    private EliminarSeccionUseCase useCase;

    @Test
    void deberiaLanzarExcepcionCuandoNoExiste() {
        final var command = new EliminarSeccionCommand(java.util.UUID.randomUUID());
        when(repository.findById(any())).thenReturn(java.util.Optional.empty());

        assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
    }

    @Test
    void deberiaLanzarExcepcionCuandoTieneHijos() {
        java.util.UUID id = java.util.UUID.randomUUID();
        final var command = new EliminarSeccionCommand(id);
        when(repository.findById(id)).thenReturn(java.util.Optional.of(mock(Seccion.class)));
        when(repository.tieneHijos(id)).thenReturn(true);

        assertThrows(EliminarSeccionException.class, () -> useCase.execute(command));
    }
}