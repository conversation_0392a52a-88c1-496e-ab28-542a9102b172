package co.com.gedsys.base.application.usecase.produccion.radicacion;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.mapper.RadicadoApplicationLayerMapper;
import co.com.gedsys.base.domain.consecutivo.ConsecutivoRepository;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.radicado.RadicadoRepository;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("RadicarDocumentoRecibidoUseCase - obtenerDestino")
class RadicarDocumentoRecibidoUseCaseObtenerDestinoTest {

    @Mock
    private DocumentoRepository documentoRepository;
    @Mock
    private TipoDocumentalRepository tipoDocumentalRepository;
    @Mock
    private UnidadDocumentalRepository unidadDocumentalRepository;
    @Mock
    private DefinicionMetadatosRepository definicionMetadatosRepository;
    @Mock
    private ConsecutivoRepository consecutivoRepository;
    @Mock
    private RadicadoRepository radicadoRepository;
    @Mock
    private RadicadoApplicationLayerMapper radicadoMapper;
    @Mock
    private ExternalUsersRepository externalUsersRepository;
    @Mock
    private ClasificacionDocumentalRepository clasificacionDocumentalRepository;

    @InjectMocks
    private RadicarDocumentoRecibidoUseCase useCase;

    private Method obtenerDestinoMethod;

    @BeforeEach
    void setUp() throws Exception {
        obtenerDestinoMethod = RadicarDocumentoRecibidoUseCase.class
                .getDeclaredMethod("obtenerDestino", UnidadDocumental.class);
        obtenerDestinoMethod.setAccessible(true);
    }

    @Test
    @DisplayName("Debe retornar la sección cuando la unidad documental tiene clasificación y sección válidas")
    void deberiaRetornarSeccionCuandoUnidadDocumentalEsValida() throws Exception {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        UUID clasificacionId = UUID.randomUUID();

        Seccion seccionEsperada = new Seccion("001", "Sección Test");
        seccionEsperada.setId(seccionId);

        SerieDocumental serie = mock(SerieDocumental.class);
        ClasificacionDocumental clasificacion = new ClasificacionDocumental(seccionEsperada, serie);
        clasificacion.setId(clasificacionId);

        UnidadDocumental unidadDocumental = new UnidadDocumental("Unidad Test", clasificacion);

        // Mock del repositorio
        when(clasificacionDocumentalRepository.buscarSeccionPorClasificacionId(clasificacionId))
                .thenReturn(Optional.of(seccionEsperada));

        // Act
        Seccion resultado = (Seccion) obtenerDestinoMethod.invoke(useCase, unidadDocumental);

        // Assert
        assertThat(resultado).isNotNull();
        assertThat(resultado).isEqualTo(seccionEsperada);
        assertThat(resultado.getId()).isEqualTo(seccionId);
        assertThat(resultado.codigo()).isEqualTo("01.00.00");
        assertThat(resultado.getNombre()).isEqualTo("Sección Test");
    }

    @Test
    @DisplayName("Debe lanzar EntityNotExistsException cuando la unidad documental es null")
    void deberiaLanzarExcepcionCuandoUnidadDocumentalEsNull() {
        // Act & Assert
        InvocationTargetException exception = assertThrows(InvocationTargetException.class, () -> {
            obtenerDestinoMethod.invoke(useCase, (UnidadDocumental) null);
        });

        assertThat(exception.getCause()).isInstanceOf(EntityNotExistsException.class);
        assertThat(exception.getCause().getMessage())
                .isEqualTo("La unidad documental es requerida para determinar el destino");
    }

    @Test
    @DisplayName("Debe lanzar EntityNotExistsException cuando la clasificación documental es null")
    void deberiaLanzarExcepcionCuandoClasificacionEsNull() {
        // Arrange
        UnidadDocumental unidadDocumental = new UnidadDocumental("Unidad Test", null);

        // Act & Assert
        InvocationTargetException exception = assertThrows(InvocationTargetException.class, () -> {
            obtenerDestinoMethod.invoke(useCase, unidadDocumental);
        });

        assertThat(exception.getCause()).isInstanceOf(EntityNotExistsException.class);
        assertThat(exception.getCause().getMessage())
                .isEqualTo("La unidad documental debe tener una clasificación documental asociada");
    }

    @Test
    @DisplayName("Debe lanzar EntityNotExistsException cuando la clasificación no tiene ID")
    void deberiaLanzarExcepcionCuandoClasificacionNoTieneId() {
        // Arrange
        SerieDocumental serie = mock(SerieDocumental.class);
        Seccion seccionMock = mock(Seccion.class);
        ClasificacionDocumental clasificacion = new ClasificacionDocumental(seccionMock, serie);
        // No se establece ID (queda null)
        UnidadDocumental unidadDocumental = new UnidadDocumental("Unidad Test", clasificacion);

        // Act & Assert
        InvocationTargetException exception = assertThrows(InvocationTargetException.class, () -> {
            obtenerDestinoMethod.invoke(useCase, unidadDocumental);
        });

        assertThat(exception.getCause()).isInstanceOf(EntityNotExistsException.class);
        assertThat(exception.getCause().getMessage())
                .isEqualTo("La clasificación documental debe tener un ID válido");
    }

    @Test
    @DisplayName("Debe lanzar EntityNotExistsException cuando no se encuentra la sección en el repositorio")
    void deberiaLanzarExcepcionCuandoSeccionNoSeEncuentraEnRepositorio() {
        // Arrange
        UUID clasificacionId = UUID.randomUUID();
        SerieDocumental serie = mock(SerieDocumental.class);
        Seccion seccionMock = mock(Seccion.class);
        ClasificacionDocumental clasificacion = new ClasificacionDocumental(seccionMock, serie);
        clasificacion.setId(clasificacionId);
        UnidadDocumental unidadDocumental = new UnidadDocumental("Unidad Test", clasificacion);

        // Mock del repositorio para retornar Optional.empty()
        when(clasificacionDocumentalRepository.buscarSeccionPorClasificacionId(clasificacionId))
                .thenReturn(Optional.empty());

        // Act & Assert
        InvocationTargetException exception = assertThrows(InvocationTargetException.class, () -> {
            obtenerDestinoMethod.invoke(useCase, unidadDocumental);
        });

        assertThat(exception.getCause()).isInstanceOf(EntityNotExistsException.class);
        assertThat(exception.getCause().getMessage())
                .contains("No se encontró la sección asociada a la clasificación documental con ID: " + clasificacionId);
    }

    @Test
    @DisplayName("Debe manejar correctamente una unidad documental con clasificación completa")
    void deberiaManejarUnidadDocumentalCompleta() throws Exception {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        UUID clasificacionId = UUID.randomUUID();

        Seccion seccion = new Seccion("002", "Sección Administrativa");
        seccion.setId(seccionId);
        seccion.setResponsable("admin.test");

        SerieDocumental serie = mock(SerieDocumental.class);

        ClasificacionDocumental clasificacion = new ClasificacionDocumental(seccion, serie);
        clasificacion.setId(clasificacionId);

        UnidadDocumental unidadDocumental = new UnidadDocumental("Unidad Administrativa", clasificacion);
        unidadDocumental.setId(UUID.randomUUID());

        // Mock del repositorio
        when(clasificacionDocumentalRepository.buscarSeccionPorClasificacionId(clasificacionId))
                .thenReturn(Optional.of(seccion));

        // Act
        Seccion resultado = (Seccion) obtenerDestinoMethod.invoke(useCase, unidadDocumental);

        // Assert
        assertThat(resultado).isNotNull();
        assertThat(resultado.getId()).isEqualTo(seccionId);
        assertThat(resultado.codigo()).isEqualTo("02.00.00");
        assertThat(resultado.getNombre()).isEqualTo("Sección Administrativa");
        assertThat(resultado.getResponsable()).isEqualTo("admin.test");
    }
}
