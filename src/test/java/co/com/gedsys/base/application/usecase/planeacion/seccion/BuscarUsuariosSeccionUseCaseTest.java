package co.com.gedsys.base.application.usecase.planeacion.seccion;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.dto.UsuarioSeccionDTO;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionMapper;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import co.com.gedsys.base.domain.organizacion.TipoRelacionUsuarioSeccion;
import co.com.gedsys.base.domain.organizacion.UsuarioSeccion;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BuscarUsuariosSeccionUseCaseTest {

    @Mock
    private SeccionRepository repository;

    @Mock
    private SeccionMapper seccionMapper;

    private BuscarUsuariosSeccionUseCase useCase;

    @BeforeEach
    void setUp() {
        useCase = new BuscarUsuariosSeccionUseCase(repository, seccionMapper);
    }

    @Test
    void deberiaBuscarUsuariosDeSeccion() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        var command = new BuscarUsuariosSeccionCommand(seccionId);
        var seccion = new Seccion("01.00.00", "Sección Principal");
        seccion.setId(seccionId);

        // Agregar usuarios a la sección
        seccion.agregarUsuario("usuario1", TipoRelacionUsuarioSeccion.PRIMARIA);
        seccion.agregarUsuario("usuario2", TipoRelacionUsuarioSeccion.SECUNDARIA);

        // Crear los DTOs esperados
        List<UsuarioSeccion> usuariosSeccion = new ArrayList<>(seccion.getUsuarios());
        UsuarioSeccionDTO dto1 = new UsuarioSeccionDTO("usuario1", "PRIMARIA", null);
        UsuarioSeccionDTO dto2 = new UsuarioSeccionDTO("usuario2", "SECUNDARIA", null);

        when(repository.findById(seccionId)).thenReturn(Optional.of(seccion));
        when(seccionMapper.toDTO(usuariosSeccion.get(0))).thenReturn(dto1);
        when(seccionMapper.toDTO(usuariosSeccion.get(1))).thenReturn(dto2);

        // Act
        List<UsuarioSeccionDTO> resultado = useCase.execute(command);

        // Assert
        assertNotNull(resultado);
        assertEquals(2, resultado.size());
        assertTrue(resultado.contains(dto1));
        assertTrue(resultado.contains(dto2));
        
        // Verify
        verify(repository).findById(seccionId);
        verify(seccionMapper).toDTO(usuariosSeccion.get(0));
        verify(seccionMapper).toDTO(usuariosSeccion.get(1));
    }

    @Test
    void deberiaLanzarExcepcionSiSeccionNoExiste() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        var command = new BuscarUsuariosSeccionCommand(seccionId);

        when(repository.findById(seccionId)).thenReturn(Optional.empty());

        // Act & Assert
        var exception = assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
        assertEquals(String.format("No existe una sección con el id '%s'", seccionId), exception.getMessage());

        verify(repository).findById(seccionId);
    }
}