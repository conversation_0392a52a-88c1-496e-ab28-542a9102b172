package co.com.gedsys.base.application.usecase.planeacion.plantillas;

import co.com.gedsys.base.application.common.InvalidBusinessRuleException;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.plantillas.Plantilla;
import co.com.gedsys.base.domain.plantillas.PlantillaRepository;
import co.com.gedsys.base.domain.plantillas.TipoPlantilla;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("Pruebas para ActualizarPlantillaUseCase - Corrección de Bugs Críticos")
class ActualizarPlantillaUseCaseTest {

    @Mock
    private DefinicionMetadatosRepository metadatoRepository;
    
    @Mock
    private PlantillaRepository repository;
    
    @Mock
    private TipoDocumentalRepository tipoDocumentalRepository;
    
    @InjectMocks
    private ActualizarPlantillaUseCase useCase;
    
    private UUID plantillaId;
    private UUID tipoDocumentalId;
    private Plantilla plantillaProduccion;
    private Plantilla plantillaEnvio;
    private TipoDocumental tipoDocumental;

    @BeforeEach
    void setUp() {
        plantillaId = UUID.randomUUID();
        tipoDocumentalId = UUID.randomUUID();
        
        plantillaProduccion = new Plantilla("Plantilla Producción", TipoPlantilla.PRODUCCION);
        plantillaProduccion.setId(plantillaId);
        
        plantillaEnvio = new Plantilla("Plantilla Envío", TipoPlantilla.ENVIO);
        plantillaEnvio.setId(plantillaId);
        
        tipoDocumental = new TipoDocumental("Tipo Test");
        tipoDocumental.setId(tipoDocumentalId);
    }

    @Test
    @DisplayName("Debe lanzar InvalidBusinessRuleException al intentar establecer producidoPorGedsys=true en plantilla no PRODUCCION")
    void deberiaLanzarExcepcionAlEstablecerProducidoPorGedsysTrueEnPlantillaNoProduccion() {
        // Arrange
        when(repository.findById(plantillaId)).thenReturn(Optional.of(plantillaEnvio));
        
        var command = ActualizarPlantillaCommand.builder()
                .plantillaId(plantillaId)
                .producidoPorGedsys(true)
                .build();

        // Act & Assert
        InvalidBusinessRuleException exception = assertThrows(
            InvalidBusinessRuleException.class,
            () -> useCase.execute(command)
        );
        
        assertEquals("Solo las plantillas de tipo PRODUCCION pueden ser producidas por Gedsys", 
                     exception.getMessage());
        
        // Verificar que no se guarda la plantilla
        verify(repository, never()).save(any());
    }

    @Test
    @DisplayName("Debe permitir establecer producidoPorGedsys=true en plantilla PRODUCCION")
    void deberiaPermitirEstablecerProducidoPorGedsysTrueEnPlantillaProduccion() {
        // Arrange
        when(repository.findById(plantillaId)).thenReturn(Optional.of(plantillaProduccion));
        
        var command = ActualizarPlantillaCommand.builder()
                .plantillaId(plantillaId)
                .producidoPorGedsys(true)
                .build();

        // Act
        assertDoesNotThrow(() -> useCase.execute(command));

        // Assert
        assertTrue(plantillaProduccion.isProducidoPorGedsys());
        verify(repository).save(plantillaProduccion);
    }

    @Test
    @DisplayName("Debe permitir establecer producidoPorGedsys=false en cualquier tipo de plantilla")
    void deberiaPermitirEstablecerProducidoPorGedsysFalseEnCualquierTipo() {
        // Arrange
        when(repository.findById(plantillaId)).thenReturn(Optional.of(plantillaEnvio));
        
        var command = ActualizarPlantillaCommand.builder()
                .plantillaId(plantillaId)
                .producidoPorGedsys(false)
                .build();

        // Act
        assertDoesNotThrow(() -> useCase.execute(command));

        // Assert
        assertFalse(plantillaEnvio.isProducidoPorGedsys());
        verify(repository).save(plantillaEnvio);
    }

    @Test
    @DisplayName("Debe manejar correctamente la actualización simultánea de tipoDocumental y producidoPorGedsys")
    void deberiaManejarActualizacionSimultaneaDeTipoDocumentalYProducidoPorGedsys() {
        // Arrange
        when(repository.findById(plantillaId)).thenReturn(Optional.of(plantillaProduccion));
        when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.of(tipoDocumental));
        
        var command = ActualizarPlantillaCommand.builder()
                .plantillaId(plantillaId)
                .tipoDocumentalId(tipoDocumentalId)
                .producidoPorGedsys(true)
                .build();

        // Act
        assertDoesNotThrow(() -> useCase.execute(command));

        // Assert
        assertEquals(tipoDocumental, plantillaProduccion.getTipoDocumental());
        assertTrue(plantillaProduccion.isProducidoPorGedsys());
        verify(repository).save(plantillaProduccion);
    }

    @Test
    @DisplayName("Debe validar reglas de negocio antes de realizar cambios")
    void deberiaValidarReglasDeNegocioAntesDeRealizarCambios() {
        // Arrange
        when(repository.findById(plantillaId)).thenReturn(Optional.of(plantillaEnvio));
        
        var command = ActualizarPlantillaCommand.builder()
                .plantillaId(plantillaId)
                .titulo("Nuevo Título")
                .producidoPorGedsys(true) // Esto debería fallar
                .build();

        // Act & Assert
        InvalidBusinessRuleException exception = assertThrows(
            InvalidBusinessRuleException.class,
            () -> useCase.execute(command)
        );
        
        // Verificar que el título no se cambió porque falló la validación
        assertNotEquals("Nuevo Título", plantillaEnvio.titulo());
        verify(repository, never()).save(any());
    }

    @Test
    @DisplayName("No debe lanzar excepción cuando producidoPorGedsys es null")
    void noDeberiaLanzarExcepcionCuandoProducidoPorGedsysEsNull() {
        // Arrange
        when(repository.findById(plantillaId)).thenReturn(Optional.of(plantillaEnvio));
        
        var command = ActualizarPlantillaCommand.builder()
                .plantillaId(plantillaId)
                .titulo("Nuevo Título")
                .producidoPorGedsys(null) // null no debería causar problemas
                .build();

        // Act & Assert
        assertDoesNotThrow(() -> useCase.execute(command));
        verify(repository).save(plantillaEnvio);
    }
}