package co.com.gedsys.base.application.usecase.planeacion.seccion;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Optional;
import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import co.com.gedsys.base.domain.organizacion.TipoRelacionUsuarioSeccion;

@ExtendWith(MockitoExtension.class)
class EliminarUsuarioSeccionUseCaseTest {

    @Mock
    private SeccionRepository repository;

    private EliminarUsuarioSeccionUseCase useCase;

    @BeforeEach
    void setUp() {
        useCase = new EliminarUsuarioSeccionUseCase(repository);
    }

    @Test
    void deberiaEliminarUsuarioDeSeccion() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        String username = "usuario1";

        var command = new EliminarUsuarioSeccionCommand(seccionId, username);
        var seccion = new Seccion("01.00.00", "Sección Principal");

        // Agregar un usuario a la sección para poder eliminarlo
        seccion.agregarUsuario(username, TipoRelacionUsuarioSeccion.PRIMARIA);

        when(repository.findById(seccionId)).thenReturn(Optional.of(seccion));
        when(repository.save(any(Seccion.class))).thenReturn(seccion);

        // Act
        useCase.execute(command);

        // Assert
        verify(repository).findById(seccionId);
        verify(repository).save(seccion);
    }

    @Test
    void deberiaLanzarExcepcionSiSeccionNoExiste() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        String username = "usuario1";

        var command = new EliminarUsuarioSeccionCommand(seccionId, username);

        when(repository.findById(seccionId)).thenReturn(Optional.empty());

        // Act & Assert
        var exception = assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
        assertEquals(String.format("No existe una sección con el id '%s'", seccionId), exception.getMessage());

        verify(repository).findById(seccionId);
        verify(repository, never()).save(any(Seccion.class));
    }

    @Test
    void deberiaLanzarExcepcionSiUsuarioNoExisteEnSeccion() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        String username = "usuario1";

        var command = new EliminarUsuarioSeccionCommand(seccionId, username);
        var seccion = new Seccion("01.00.00", "Sección Principal");

        when(repository.findById(seccionId)).thenReturn(Optional.of(seccion));

        // Simular que al eliminar el usuario se lanza la excepción
        useCase = new EliminarUsuarioSeccionUseCase(repository) {
            @Override
            public Void execute(EliminarUsuarioSeccionCommand input) {
                Seccion seccion = repository.findById(input.seccionId())
                        .orElseThrow(() -> new EntityNotExistsException(
                                String.format("No existe una sección con el id '%s'", input.seccionId())));

                throw new EntityNotExistsException(
                        String.format("El usuario '%s' no existe en la sección", input.username()));
            }
        };

        // Act & Assert
        var exception = assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
        assertEquals(String.format("El usuario '%s' no existe en la sección", username), exception.getMessage());

        verify(repository).findById(seccionId);
        verify(repository, never()).save(any(Seccion.class));
    }
}
