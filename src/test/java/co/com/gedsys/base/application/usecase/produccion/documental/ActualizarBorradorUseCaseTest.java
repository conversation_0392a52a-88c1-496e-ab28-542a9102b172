package co.com.gedsys.base.application.usecase.produccion.documental;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.application.mapper.DocumentoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.produccion.documental.command.UpdateDraftCommand;
import co.com.gedsys.base.domain.documento.*;
import co.com.gedsys.base.domain.metadato.DefinicionMetadato;
import co.com.gedsys.base.domain.metadato.Metadato;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ActualizarBorradorUseCaseTest {

    @Mock
    private DocumentoRepository documentoRepository;

    @Mock
    private DefinicionMetadatosRepository definicionMetadatosRepository;

    @Mock
    private DocumentoApplicationLayerMapper mapper;

    @Mock
    private ActualizarBorradorUseCaseMapper commandMapper;

    @InjectMocks
    private ActualizarBorradorUseCase useCase;

    private UUID documentoId;
    private Documento documentoExistente;
    private UpdateDraftCommand command;
    private Set<DefinicionMetadato> definicionesMetadatos;
    private Set<Metadato> metadatosExistentes;

    @BeforeEach
    void setUp() {
        documentoId = UUID.randomUUID();
        var tipoDocumental = mock(TipoDocumental.class);
        var unidadDocumental = mock(UnidadDocumental.class);

        // Crear metadatos existentes
        metadatosExistentes = new HashSet<>();
        Metadato metadatoSeccion = mock(Metadato.class);
        lenient().when(metadatoSeccion.patron()).thenReturn("seccion");
        lenient().when(metadatoSeccion.valor()).thenReturn("1");

        Metadato metadatoSerie = mock(Metadato.class);
        lenient().when(metadatoSerie.patron()).thenReturn("serie");
        lenient().when(metadatoSerie.valor()).thenReturn("2");

        metadatosExistentes.add(metadatoSeccion);
        metadatosExistentes.add(metadatoSerie);

        documentoExistente = new Documento(
            documentoId,
            "Título Original",
            "file-123",
            EstadoDocumento.BORRADOR,
            tipoDocumental,
            unidadDocumental,
            "autor1",
            metadatosExistentes,
            List.of(),
            List.of(),
            List.of(),
            List.of()
        );

        var metadatos = new LinkedHashMap<String, String>();
        metadatos.put("seccion", "1");
        metadatos.put("serie", "2");

        command = new UpdateDraftCommand(
            documentoId,
            "Nuevo Título",
            metadatos,
            List.of(new UpdateDraftCommand.RegistroAnexo(
                "anexo1_documento",
                "Descripción",
                "file-456",
                "abc123",
                1024L,
                "pdf"
            ))
        );

        var definicionSeccion = mock(DefinicionMetadato.class, RETURNS_DEFAULTS);
        var definicionSerie = mock(DefinicionMetadato.class, RETURNS_DEFAULTS);
        lenient().when(definicionSeccion.getPatron()).thenReturn("seccion");
        lenient().when(definicionSerie.getPatron()).thenReturn("serie");
        lenient().when(definicionSeccion.generarMetadato(any())).thenReturn(mock(Metadato.class));
        lenient().when(definicionSerie.generarMetadato(any())).thenReturn(mock(Metadato.class));

        definicionesMetadatos = new HashSet<>(Arrays.asList(definicionSeccion, definicionSerie));

        // Mock del mapper para anexos
        var anexoMock = mock(Anexo.class);
        lenient().when(anexoMock.getId()).thenReturn(UUID.randomUUID());
        lenient().when(commandMapper.toAnexos(any())).thenReturn(List.of(anexoMock));
    }

    @Test
    void deberiaActualizarDocumentoExistente() {
        // Arrange
        // Modificar el comando para que tenga un valor diferente en un metadato existente
        LinkedHashMap<String, String> metadatosModificados = new LinkedHashMap<>();
        metadatosModificados.put("seccion", "1"); // Mismo valor
        metadatosModificados.put("serie", "3");   // Valor diferente
        UpdateDraftCommand commandConCambios = new UpdateDraftCommand(
            documentoId,
            "Nuevo Título",
            metadatosModificados,
            command.anexos()
        );

        when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documentoExistente));
        when(definicionMetadatosRepository.buscarPorPatrones(any())).thenReturn(definicionesMetadatos);
        when(documentoRepository.save(any(Documento.class))).thenReturn(documentoExistente);
        when(mapper.toDTO(any(Documento.class))).thenReturn(mock(DocumentoDTO.class));

        // Act
        useCase.execute(commandConCambios);

        // Assert
        verify(documentoRepository).findById(documentoId);
        // Verificar que se llama a buscarPorPatrones porque hay cambios en los metadatos
        verify(definicionMetadatosRepository).buscarPorPatrones(new ArrayList<>(commandConCambios.metadatos().keySet()));
        verify(documentoRepository).save(any(Documento.class));
        verify(mapper).toDTO(any(Documento.class));
    }

    @Test
    void deberiaDarErrorSiDocumentoNoExiste() {
        // Arrange
        when(documentoRepository.findById(documentoId)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
        verify(documentoRepository).findById(documentoId);
        verify(documentoRepository, never()).save(any());
    }

    @Test
    void deberiaActualizarMetadatosYAnexos() {
        // Arrange
        // Modificar el comando para que tenga un valor diferente en un metadato existente
        LinkedHashMap<String, String> metadatosModificados = new LinkedHashMap<>();
        metadatosModificados.put("seccion", "1"); // Mismo valor
        metadatosModificados.put("serie", "3");   // Valor diferente
        metadatosModificados.put("subserie", "4"); // Nuevo metadato
        UpdateDraftCommand commandConCambios = new UpdateDraftCommand(
            documentoId,
            "Nuevo Título",
            metadatosModificados,
            command.anexos()
        );

        when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documentoExistente));
        when(definicionMetadatosRepository.buscarPorPatrones(any())).thenReturn(definicionesMetadatos);
        when(documentoRepository.save(any(Documento.class))).thenReturn(documentoExistente);
        when(mapper.toDTO(any(Documento.class))).thenReturn(mock(DocumentoDTO.class));

        // Act
        useCase.execute(commandConCambios);

        // Assert
        verify(documentoRepository).findById(documentoId);
        // Verificar que se llama a buscarPorPatrones porque hay cambios en los metadatos
        verify(definicionMetadatosRepository).buscarPorPatrones(new ArrayList<>(commandConCambios.metadatos().keySet()));

        // Verificar que se actualizó el documento con los nuevos metadatos y anexos
        verify(documentoRepository).save(argThat(documento -> 
            documento.getTitulo().equals(commandConCambios.title()) &&
            !documento.getMetadatos().isEmpty() &&
            !documento.getAnexos().isEmpty()
        ));
    }

    @Test
    void deberiaActualizarMetadatosCuandoHayCambios() {
        // Arrange
        when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documentoExistente));
        when(definicionMetadatosRepository.buscarPorPatrones(any())).thenReturn(definicionesMetadatos);
        when(documentoRepository.save(any(Documento.class))).thenReturn(documentoExistente);
        when(mapper.toDTO(any(Documento.class))).thenReturn(mock(DocumentoDTO.class));

        // Modificar el comando para que tenga un valor diferente en un metadato existente
        LinkedHashMap<String, String> metadatosModificados = new LinkedHashMap<>();
        metadatosModificados.put("seccion", "1"); // Mismo valor
        metadatosModificados.put("serie", "3");   // Valor diferente
        UpdateDraftCommand commandConCambios = new UpdateDraftCommand(
            documentoId,
            "Nuevo Título",
            metadatosModificados,
            command.anexos()
        );

        // Act
        useCase.execute(commandConCambios);

        // Assert
        verify(documentoRepository).findById(documentoId);
        verify(definicionMetadatosRepository).buscarPorPatrones(new ArrayList<>(commandConCambios.metadatos().keySet()));
        verify(documentoRepository).save(any(Documento.class));
    }

    @Test
    void noDeberiaActualizarMetadatosCuandoNoHayCambios() {
        // Arrange
        when(documentoRepository.findById(documentoId)).thenReturn(Optional.of(documentoExistente));
        when(documentoRepository.save(any(Documento.class))).thenReturn(documentoExistente);
        when(mapper.toDTO(any(Documento.class))).thenReturn(mock(DocumentoDTO.class));

        // Crear un comando con los mismos valores de metadatos que ya existen
        LinkedHashMap<String, String> metadatosSinCambios = new LinkedHashMap<>();
        metadatosSinCambios.put("seccion", "1");
        metadatosSinCambios.put("serie", "2");
        UpdateDraftCommand commandSinCambios = new UpdateDraftCommand(
            documentoId,
            "Nuevo Título",
            metadatosSinCambios,
            List.of() // Lista vacía en lugar de null
        );

        // Act
        useCase.execute(commandSinCambios);

        // Assert
        verify(documentoRepository).findById(documentoId);
        // No debería llamar a buscarPorPatrones ni generar nuevos metadatos
        verify(definicionMetadatosRepository, never()).buscarPorPatrones(any());
        // Debería guardar el documento con el título actualizado pero sin cambios en metadatos
        verify(documentoRepository).save(any(Documento.class));
    }
} 
