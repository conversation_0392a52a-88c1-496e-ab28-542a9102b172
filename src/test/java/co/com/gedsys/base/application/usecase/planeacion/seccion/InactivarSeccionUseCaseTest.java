package co.com.gedsys.base.application.usecase.planeacion.seccion;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.application.mapper.SeccionUseCaseMapper;
import co.com.gedsys.base.domain.organizacion.EstadoSeccion;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import co.com.gedsys.base.domain.organizacion.SeccionYaInactivaException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("InactivarSeccionUseCase")
class InactivarSeccionUseCaseTest {

    @Mock
    private SeccionRepository repository;

    @Mock
    private SeccionUseCaseMapper mapper;

    private InactivarSeccionUseCase useCase;

    @BeforeEach
    void setUp() {
        useCase = new InactivarSeccionUseCase(repository, mapper);
    }

    @Test
    @DisplayName("debe inactivar sección exitosamente cuando está activa")
    void deberiaInactivarSeccionExitosamenteCuandoEstaActiva() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        var command = new InactivarSeccionCommand(seccionId);
        var seccion = new Seccion("01.00.00", "Sección Test");
        seccion.setId(seccionId);
        seccion.setResponsable("Juan Pérez");
        seccion.setEstado(EstadoSeccion.ACTIVA);

        var seccionInactivada = new Seccion("01.00.00", "Sección Test");
        seccionInactivada.setId(seccionId);
        seccionInactivada.setResponsable("Juan Pérez");
        seccionInactivada.setEstado(EstadoSeccion.INACTIVA);

        var seccionDTO = new SeccionDTO(seccionId.toString(), EstadoSeccion.INACTIVA.name(), null, 
                "01.00.00", "Sección Test", "Juan Pérez");

        when(repository.findById(seccionId)).thenReturn(Optional.of(seccion));
        when(repository.save(any(Seccion.class))).thenReturn(seccionInactivada);
        when(mapper.toDto(any(Seccion.class))).thenReturn(seccionDTO);

        // Act
        SeccionDTO result = useCase.execute(command);

        // Assert
        assertNotNull(result);
        assertEquals(EstadoSeccion.INACTIVA.name(), result.estado());
        assertEquals("Juan Pérez", result.responsable());
        verify(repository).findById(seccionId);
        verify(repository).save(any(Seccion.class));
        verify(mapper).toDto(any(Seccion.class));
    }

    @Test
    @DisplayName("debe lanzar excepción al inactivar sección que ya está inactiva")
    void deberiaLanzarExcepcionAlInactivarSeccionYaInactiva() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        var command = new InactivarSeccionCommand(seccionId);
        var seccion = new Seccion("01.00.00", "Sección Test");
        seccion.setId(seccionId);
        seccion.setResponsable("Juan Pérez");
        seccion.setEstado(EstadoSeccion.INACTIVA);

        when(repository.findById(seccionId)).thenReturn(Optional.of(seccion));

        // Act & Assert
        assertThrows(SeccionYaInactivaException.class, () -> useCase.execute(command));
        verify(repository).findById(seccionId);
        verify(repository, never()).save(any(Seccion.class));
        verify(mapper, never()).toDto(any(Seccion.class));
    }

    @Test
    @DisplayName("debe lanzar excepción cuando la sección no existe")
    void deberiaLanzarExcepcionCuandoSeccionNoExiste() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        var command = new InactivarSeccionCommand(seccionId);

        when(repository.findById(seccionId)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
        verify(repository).findById(seccionId);
        verify(repository, never()).save(any(Seccion.class));
        verify(mapper, never()).toDto(any(Seccion.class));
    }
}
