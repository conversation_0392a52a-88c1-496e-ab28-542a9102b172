package co.com.gedsys.base.application.usecase.planeacion.seccion;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.common.SeccionSinResponsableException;
import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.application.mapper.SeccionUseCaseMapper;
import co.com.gedsys.base.domain.organizacion.EstadoSeccion;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("ActivarSeccionUseCase")
class ActivarSeccionUseCaseTest {

    @Mock
    private SeccionRepository repository;

    @Mock
    private SeccionUseCaseMapper mapper;

    private ActivarSeccionUseCase useCase;

    @BeforeEach
    void setUp() {
        useCase = new ActivarSeccionUseCase(repository, mapper);
    }

    @Test
    @DisplayName("debe activar sección exitosamente cuando tiene responsable")
    void deberiaActivarSeccionExitosamenteConResponsable() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        var command = new ActivarSeccionCommand(seccionId);
        var seccion = new Seccion("01.00.00", "Sección Test");
        seccion.setId(seccionId);
        seccion.setResponsable("Juan Pérez");
        seccion.setEstado(EstadoSeccion.INACTIVA);

        var seccionActivada = new Seccion("01.00.00", "Sección Test");
        seccionActivada.setId(seccionId);
        seccionActivada.setResponsable("Juan Pérez");
        seccionActivada.setEstado(EstadoSeccion.ACTIVA);

        var seccionDTO = new SeccionDTO(seccionId.toString(), EstadoSeccion.ACTIVA.name(), null, 
                "01.00.00", "Sección Test", "Juan Pérez");

        when(repository.findById(seccionId)).thenReturn(Optional.of(seccion));
        when(repository.save(any(Seccion.class))).thenReturn(seccionActivada);
        when(mapper.toDto(any(Seccion.class))).thenReturn(seccionDTO);

        // Act
        SeccionDTO result = useCase.execute(command);

        // Assert
        assertNotNull(result);
        assertEquals(EstadoSeccion.ACTIVA.name(), result.estado());
        assertEquals("Juan Pérez", result.responsable());
        verify(repository).findById(seccionId);
        verify(repository).save(any(Seccion.class));
        verify(mapper).toDto(any(Seccion.class));
    }

    @Test
    @DisplayName("debe lanzar excepción al activar sección sin responsable")
    void deberiaLanzarExcepcionAlActivarSeccionSinResponsable() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        var command = new ActivarSeccionCommand(seccionId);
        var seccion = new Seccion("01.00.00", "Sección Test");
        seccion.setId(seccionId);
        seccion.setResponsable(null);
        seccion.setEstado(EstadoSeccion.INACTIVA);

        when(repository.findById(seccionId)).thenReturn(Optional.of(seccion));

        // Act & Assert
        assertThrows(SeccionSinResponsableException.class, () -> useCase.execute(command));
        verify(repository).findById(seccionId);
        verify(repository, never()).save(any(Seccion.class));
        verify(mapper, never()).toDto(any(Seccion.class));
    }

    @Test
    @DisplayName("debe lanzar excepción cuando la sección no existe")
    void deberiaLanzarExcepcionCuandoSeccionNoExiste() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        var command = new ActivarSeccionCommand(seccionId);

        when(repository.findById(seccionId)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
        verify(repository).findById(seccionId);
        verify(repository, never()).save(any(Seccion.class));
        verify(mapper, never()).toDto(any(Seccion.class));
    }

    @Test
    @DisplayName("debe lanzar excepción al activar sección con responsable vacío")
    void deberiaLanzarExcepcionAlActivarSeccionConResponsableVacio() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        var command = new ActivarSeccionCommand(seccionId);
        var seccion = new Seccion("01.00.00", "Sección Test");
        seccion.setId(seccionId);
        seccion.setResponsable("");
        seccion.setEstado(EstadoSeccion.INACTIVA);

        when(repository.findById(seccionId)).thenReturn(Optional.of(seccion));

        // Act & Assert
        assertThrows(SeccionSinResponsableException.class, () -> useCase.execute(command));
        verify(repository).findById(seccionId);
        verify(repository, never()).save(any(Seccion.class));
        verify(mapper, never()).toDto(any(Seccion.class));
    }

    @Test
    @DisplayName("debe lanzar excepción al activar sección con responsable solo espacios")
    void deberiaLanzarExcepcionAlActivarSeccionConResponsableSoloEspacios() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        var command = new ActivarSeccionCommand(seccionId);
        var seccion = new Seccion("01.00.00", "Sección Test");
        seccion.setId(seccionId);
        seccion.setResponsable("   ");
        seccion.setEstado(EstadoSeccion.INACTIVA);

        when(repository.findById(seccionId)).thenReturn(Optional.of(seccion));

        // Act & Assert
        assertThrows(SeccionSinResponsableException.class, () -> useCase.execute(command));
        verify(repository).findById(seccionId);
        verify(repository, never()).save(any(Seccion.class));
        verify(mapper, never()).toDto(any(Seccion.class));
    }
}
