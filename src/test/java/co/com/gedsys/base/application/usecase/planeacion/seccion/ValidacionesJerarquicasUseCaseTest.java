package co.com.gedsys.base.application.usecase.planeacion.seccion;

import co.com.gedsys.base.application.mapper.SeccionUseCaseMapper;
import co.com.gedsys.base.domain.organizacion.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("Validaciones Jerárquicas en Casos de Uso")
class ValidacionesJerarquicasUseCaseTest {

    @Mock
    private SeccionRepository repository;

    @Mock
    private SeccionUseCaseMapper mapper;

    @InjectMocks
    private ActivarSeccionUseCase activarUseCase;

    @InjectMocks
    private InactivarSeccionUseCase inactivarUseCase;

    private UUID padreId;
    private UUID hijoId;
    private Seccion padre;
    private Seccion hijo;

    @BeforeEach
    void setUp() {
        padreId = UUID.randomUUID();
        hijoId = UUID.randomUUID();

        padre = new Seccion("01.00.00", "Despacho Alcalde");
        padre.setId(padreId);
        padre.setResponsable("Alcalde");
        padre.setEstado(EstadoSeccion.ACTIVA);

        hijo = new Seccion("01.01.00", "Secretaría Gobierno");
        hijo.setId(hijoId);
        hijo.setResponsable("Secretario");
        hijo.setEstado(EstadoSeccion.INACTIVA);
        hijo.setPadre(padre);
    }

    @Nested
    @DisplayName("ActivarSeccionUseCase - Validaciones Jerárquicas")
    class ActivarSeccionValidaciones {

        @Test
        @DisplayName("debe activar sección cuando padre está activo")
        void deberiaActivarSeccionConPadreActivo() {
            // Arrange
            when(repository.findById(hijoId)).thenReturn(Optional.of(hijo));
            when(repository.tienePadreInactivo(hijoId)).thenReturn(false);
            when(repository.save(any(Seccion.class))).thenReturn(hijo);

            var command = new ActivarSeccionCommand(hijoId);

            // Act
            activarUseCase.execute(command);

            // Assert
            verify(repository).tienePadreInactivo(hijoId);
            verify(repository).save(any(Seccion.class));
            assertThat(hijo.getEstado()).isEqualTo(EstadoSeccion.ACTIVA);
        }

        @Test
        @DisplayName("debe lanzar excepción cuando padre está inactivo")
        void deberiaLanzarExcepcionConPadreInactivo() {
            // Arrange
            when(repository.findById(hijoId)).thenReturn(Optional.of(hijo));
            when(repository.tienePadreInactivo(hijoId)).thenReturn(true);
            when(repository.findById(padreId)).thenReturn(Optional.of(padre));

            var command = new ActivarSeccionCommand(hijoId);

            // Act & Assert
            var exception = assertThrows(SeccionPadreInactivaException.class, 
                    () -> activarUseCase.execute(command));

            assertThat(exception.getMessage())
                    .contains("No se puede activar la sección 'Secretaría Gobierno'")
                    .contains("su sección padre 'Despacho Alcalde' está inactiva");

            verify(repository).tienePadreInactivo(hijoId);
            verify(repository, never()).save(any(Seccion.class));
        }

        @Test
        @DisplayName("debe activar sección sin padre")
        void deberiaActivarSeccionSinPadre() {
            // Arrange
            hijo.setPadre(null);
            when(repository.findById(hijoId)).thenReturn(Optional.of(hijo));
            when(repository.tienePadreInactivo(hijoId)).thenReturn(false);
            when(repository.save(any(Seccion.class))).thenReturn(hijo);

            var command = new ActivarSeccionCommand(hijoId);

            // Act
            activarUseCase.execute(command);

            // Assert
            verify(repository).tienePadreInactivo(hijoId);
            verify(repository).save(any(Seccion.class));
            assertThat(hijo.getEstado()).isEqualTo(EstadoSeccion.ACTIVA);
        }
    }

    @Nested
    @DisplayName("InactivarSeccionUseCase - Validaciones Jerárquicas")
    class InactivarSeccionValidaciones {

        @Test
        @DisplayName("debe inactivar sección sin hijos activos")
        void deberiaInactivarSeccionSinHijosActivos() {
            // Arrange
            padre.setEstado(EstadoSeccion.ACTIVA);
            when(repository.findById(padreId)).thenReturn(Optional.of(padre));
            when(repository.tieneHijasActivas(padreId)).thenReturn(false);
            when(repository.save(any(Seccion.class))).thenReturn(padre);

            var command = new InactivarSeccionCommand(padreId);

            // Act
            inactivarUseCase.execute(command);

            // Assert
            verify(repository).tieneHijasActivas(padreId);
            verify(repository).save(any(Seccion.class));
            assertThat(padre.getEstado()).isEqualTo(EstadoSeccion.INACTIVA);
        }

        @Test
        @DisplayName("debe lanzar excepción cuando tiene hijos activos")
        void deberiaLanzarExcepcionConHijosActivos() {
            // Arrange
            padre.setEstado(EstadoSeccion.ACTIVA);
            hijo.setEstado(EstadoSeccion.ACTIVA);
            
            List<Seccion> hijosActivos = List.of(hijo);
            
            when(repository.findById(padreId)).thenReturn(Optional.of(padre));
            when(repository.tieneHijasActivas(padreId)).thenReturn(true);
            when(repository.buscarHijasConEstado(padreId, EstadoSeccion.ACTIVA))
                    .thenReturn(hijosActivos);

            var command = new InactivarSeccionCommand(padreId);

            // Act & Assert
            var exception = assertThrows(SeccionConHijasActivasException.class, 
                    () -> inactivarUseCase.execute(command));

            assertThat(exception.getMessage())
                    .contains("No se puede inactivar la sección 'Despacho Alcalde'")
                    .contains("tiene secciones hijas activas")
                    .contains("Secretaría Gobierno");

            verify(repository).tieneHijasActivas(padreId);
            verify(repository).buscarHijasConEstado(padreId, EstadoSeccion.ACTIVA);
            verify(repository, never()).save(any(Seccion.class));
        }

        @Test
        @DisplayName("debe inactivar sección sin hijos")
        void deberiaInactivarSeccionSinHijos() {
            // Arrange
            hijo.setEstado(EstadoSeccion.ACTIVA);
            when(repository.findById(hijoId)).thenReturn(Optional.of(hijo));
            when(repository.tieneHijasActivas(hijoId)).thenReturn(false);
            when(repository.save(any(Seccion.class))).thenReturn(hijo);

            var command = new InactivarSeccionCommand(hijoId);

            // Act
            inactivarUseCase.execute(command);

            // Assert
            verify(repository).tieneHijasActivas(hijoId);
            verify(repository).save(any(Seccion.class));
            assertThat(hijo.getEstado()).isEqualTo(EstadoSeccion.INACTIVA);
        }
    }
}
