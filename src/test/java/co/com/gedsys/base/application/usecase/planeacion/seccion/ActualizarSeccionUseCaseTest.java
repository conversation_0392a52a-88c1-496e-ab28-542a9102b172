package co.com.gedsys.base.application.usecase.planeacion.seccion;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Optional;
import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.application.mapper.SeccionUseCaseMapper;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;

@ExtendWith(MockitoExtension.class)
@DisplayName("Pruebas para el caso de uso de actualización de sección")
public class ActualizarSeccionUseCaseTest {

    @Mock
    private SeccionRepository repository;

    @Mock
    private SeccionUseCaseMapper mapper;

    private ActualizarSeccionUseCase useCase;

    @BeforeEach
    void setUp() {
        useCase = new ActualizarSeccionUseCase(repository, mapper);
    }

    @Nested
    @DisplayName("Cuando la sección existe")
    class CuandoSeccionExiste {

        @Test
        @DisplayName("Debería actualizar tanto nombre como responsable")
        void debeActualizarSeccionCompleta() {
            // Arrange
            UUID id = UUID.randomUUID();
            String nuevoNombre = "Nueva Sección";
            String nuevoResponsable = "Nuevo Responsable";

            var command = new ActualizarSeccionCommand(id, nuevoNombre, nuevoResponsable);
            var seccionExistente = new Seccion("01.00.00", "Sección Original");
            var seccionActualizada = new Seccion("01.00.00", nuevoNombre);
            seccionActualizada.setResponsable(nuevoResponsable);

            var seccionDTO = new SeccionDTO(
                    id.toString(),
                    seccionActualizada.getEstado().name(),
                    null,
                    seccionActualizada.getCodigo(),
                    nuevoNombre,
                    nuevoResponsable);

            when(repository.findById(id)).thenReturn(Optional.of(seccionExistente));
            when(repository.save(any(Seccion.class))).thenReturn(seccionActualizada);
            when(mapper.toDto(any(Seccion.class))).thenReturn(seccionDTO);

            // Act
            var resultado = useCase.execute(command);

            // Assert
            assertNotNull(resultado);
            assertEquals(nuevoNombre, resultado.nombre());
            assertEquals(nuevoResponsable, resultado.responsable());
            assertEquals(id.toString(), resultado.id());
            verify(repository).findById(id);
            verify(repository).save(any(Seccion.class));
            verify(mapper).toDto(any(Seccion.class));
        }

        @Test
        @DisplayName("Debería actualizar solo el nombre cuando el responsable es null")
        void debeActualizarSoloNombre() {
            // Arrange
            UUID id = UUID.randomUUID();
            String nuevoNombre = "Nueva Sección";
            String responsableOriginal = "Responsable Original";

            var command = new ActualizarSeccionCommand(id, nuevoNombre, null);
            var seccionExistente = new Seccion("01.00.00", "Sección Original");
            seccionExistente.setResponsable(responsableOriginal);

            var seccionActualizada = new Seccion("01.00.00", nuevoNombre);
            seccionActualizada.setResponsable(responsableOriginal);

            var seccionDTO = new SeccionDTO(
                    id.toString(),
                    seccionActualizada.getEstado().name(),
                    null,
                    seccionActualizada.getCodigo(),
                    nuevoNombre,
                    responsableOriginal);

            when(repository.findById(id)).thenReturn(Optional.of(seccionExistente));
            when(repository.save(any(Seccion.class))).thenReturn(seccionActualizada);
            when(mapper.toDto(any(Seccion.class))).thenReturn(seccionDTO);

            // Act
            var resultado = useCase.execute(command);

            // Assert
            assertNotNull(resultado);
            assertEquals(nuevoNombre, resultado.nombre());
            assertEquals(responsableOriginal, resultado.responsable());
            verify(repository).findById(id);
            verify(repository).save(any(Seccion.class));
            verify(mapper).toDto(any(Seccion.class));
        }

        @Test
        @DisplayName("Debería actualizar solo el responsable cuando el nombre es null")
        void debeActualizarSoloResponsable() {
            // Arrange
            UUID id = UUID.randomUUID();
            String nombreOriginal = "Sección Original";
            String nuevoResponsable = "Nuevo Responsable";

            var command = new ActualizarSeccionCommand(id, null, nuevoResponsable);
            var seccionExistente = new Seccion("01.00.00", nombreOriginal);

            var seccionActualizada = new Seccion("01.00.00", nombreOriginal);
            seccionActualizada.setResponsable(nuevoResponsable);

            var seccionDTO = new SeccionDTO(
                    id.toString(),
                    seccionActualizada.getEstado().name(),
                    null,
                    seccionActualizada.getCodigo(),
                    nombreOriginal,
                    nuevoResponsable);

            when(repository.findById(id)).thenReturn(Optional.of(seccionExistente));
            when(repository.save(any(Seccion.class))).thenReturn(seccionActualizada);
            when(mapper.toDto(any(Seccion.class))).thenReturn(seccionDTO);

            // Act
            var resultado = useCase.execute(command);

            // Assert
            assertNotNull(resultado);
            assertEquals(nombreOriginal, resultado.nombre());
            assertEquals(nuevoResponsable, resultado.responsable());
            verify(repository).findById(id);
            verify(repository).save(any(Seccion.class));
            verify(mapper).toDto(any(Seccion.class));
        }
    }

    @Nested
    @DisplayName("Cuando la sección no existe")
    class CuandoSeccionNoExiste {

        @Test
        @DisplayName("Debería lanzar una excepción EntityNotExistsException")
        void debeLanzarExcepcion() {
            // Arrange
            UUID id = UUID.randomUUID();
            var command = new ActualizarSeccionCommand(id, "Nombre", "Responsable");

            when(repository.findById(id)).thenReturn(Optional.empty());

            // Act & Assert
            assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
            verify(repository).findById(id);
            verify(repository, never()).save(any());
            verify(mapper, never()).toDto(any());
        }
    }
}
