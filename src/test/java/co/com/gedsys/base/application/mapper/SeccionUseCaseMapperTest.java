package co.com.gedsys.base.application.mapper;

import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.application.dto.UnidadProductoraOrganigramaDS;
import co.com.gedsys.base.domain.organizacion.Seccion;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@SpringJUnitConfig(SeccionUseCaseMapperTest.MapperTestConfig.class)
public class SeccionUseCaseMapperTest {
    
    @TestConfiguration
    @ComponentScan(
        basePackageClasses = SeccionUseCaseMapper.class,
        includeFilters = @ComponentScan.Filter(
            type = FilterType.ASSIGNABLE_TYPE,
            classes = SeccionUseCaseMapper.class
        )
    )
    static class MapperTestConfig {
        // Configuración vacía para cargar solo el mapper
    }
    
    @Autowired
    private SeccionUseCaseMapper mapper;
    
    private Seccion seccion;
    private Seccion seccionPadre;
    
    @BeforeEach
    void setUp() {
        // Crear sección padre
        seccionPadre = new Seccion("01.00.00", "Despacho");
        seccionPadre.setId(UUID.randomUUID());
        seccionPadre.setResponsable("Responsable Padre");
        
        // Crear sección hija
        seccion = new Seccion("01.01.00", "Secretaría");
        seccion.setId(UUID.randomUUID());
        seccion.setResponsable("Responsable Hijo");
        seccion.setPadre(seccionPadre);
    }
    
    @Test
    void testToDS() {
        // Ejecutar el mapeo
        UnidadProductoraOrganigramaDS ds = mapper.toDS(seccion);
        
        // Verificar el mapeo correcto
        assertNotNull(ds);
        assertEquals(seccion.getId().toString(), ds.id());
        assertEquals(seccion.getNombre(), ds.nombre());
        assertEquals(seccion.codigo(), ds.codigo()); // Verificar que se use el método codigo()
        assertEquals(seccion.getResponsable(), ds.responsable());
        assertEquals(seccion.getEstado().name(), ds.estado());
    }
    
    @Test
    void testToDto() {
        // Ejecutar el mapeo
        SeccionDTO dto = mapper.toDto(seccion);
        
        // Verificar el mapeo correcto
        assertNotNull(dto);
        assertEquals(seccion.getId().toString(), dto.id());
        assertEquals(seccion.getNombre(), dto.nombre());
        assertEquals(seccion.getCodigo(), dto.codigo());
        assertEquals(seccion.getResponsable(), dto.responsable());
        assertEquals(seccion.getEstado().name(), dto.estado());
        
        // Verificar el mapeo del padre
        assertNotNull(dto.padre());
        assertEquals(seccionPadre.getId().toString(), dto.padre().id());
        assertEquals(seccionPadre.getNombre(), dto.padre().nombre());
        assertEquals(seccionPadre.getCodigo(), dto.padre().codigo());
        assertEquals(seccionPadre.getResponsable(), dto.padre().responsable());
        assertEquals(seccionPadre.getEstado().name(), dto.padre().estado());
        assertNull(dto.padre().padre()); // El padre del padre debe ser null
    }
    
    @Test
    void testToDsWithNullSeccion() {
        assertNull(mapper.toDS(null));
    }
    
    @Test
    void testToDtoWithNullSeccion() {
        assertNull(mapper.toDto(null));
    }
}
