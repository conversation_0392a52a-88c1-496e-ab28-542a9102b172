package co.com.gedsys.base.application.common;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

@DisplayName("SeccionSinResponsableException")
class SeccionSinResponsableExceptionTest {

    @Test
    @DisplayName("debe crear excepción con mensaje por defecto")
    void debeCrearExcepcionConMensajePorDefecto() {
        // When
        var exception = new SeccionSinResponsableException();

        // Then
        assertThat(exception.getMessage())
                .isEqualTo("No se puede activar una sección sin responsable asignado");
        assertThat(exception).isInstanceOf(RuntimeException.class);
    }

    @Test
    @DisplayName("debe crear excepción con mensaje personalizado")
    void debeCrearExcepcionConMensajePersonalizado() {
        // Given
        String mensajePersonalizado = "Mensaje de error personalizado para la sección";

        // When
        var exception = new SeccionSinResponsableException(mensajePersonalizado);

        // Then
        assertThat(exception.getMessage()).isEqualTo(mensajePersonalizado);
        assertThat(exception).isInstanceOf(RuntimeException.class);
    }

    @Test
    @DisplayName("debe crear excepción con mensaje formateado")
    void debeCrearExcepcionConMensajeFormateado() {
        // Given
        String formato = "La sección con código %s no puede ser activada sin responsable";
        String codigoSeccion = "01.02.03";

        // When
        var exception = new SeccionSinResponsableException(formato, codigoSeccion);

        // Then
        assertThat(exception.getMessage())
                .isEqualTo("La sección con código 01.02.03 no puede ser activada sin responsable");
        assertThat(exception).isInstanceOf(RuntimeException.class);
    }

    @Test
    @DisplayName("debe crear excepción con múltiples argumentos en formato")
    void debeCrearExcepcionConMultiplesArgumentosEnFormato() {
        // Given
        String formato = "La sección '%s' con código %s y ID %d no puede ser activada";
        String nombre = "Secretaría General";
        String codigo = "01.01.00";
        Long id = 123L;

        // When
        var exception = new SeccionSinResponsableException(formato, nombre, codigo, id);

        // Then
        assertThat(exception.getMessage())
                .isEqualTo("La sección 'Secretaría General' con código 01.01.00 y ID 123 no puede ser activada");
        assertThat(exception).isInstanceOf(RuntimeException.class);
    }

    @Test
    @DisplayName("debe ser lanzada correctamente")
    void debeSerLanzadaCorrectamente() {
        // When & Then
        var exception = assertThrows(SeccionSinResponsableException.class, () -> {
            throw new SeccionSinResponsableException();
        });

        assertThat(exception.getMessage())
                .isEqualTo("No se puede activar una sección sin responsable asignado");
    }

    @Test
    @DisplayName("debe manejar formato con argumentos nulos")
    void debeManejarFormatoConArgumentosNulos() {
        // Given
        String formato = "Sección %s no válida";
        String argumentoNulo = null;

        // When
        var exception = new SeccionSinResponsableException(formato, argumentoNulo);

        // Then
        assertThat(exception.getMessage()).isEqualTo("Sección null no válida");
    }
} 