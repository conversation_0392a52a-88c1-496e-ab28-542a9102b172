package co.com.gedsys.base.application.usecase.produccion.documental;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.application.mapper.DocumentoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.produccion.documental.command.AgregarAnexoCommand;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.documento.DocumentoDescartadoException;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.documento.EstadoDocumento;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("AgregarAnexoUseCase")
class AgregarAnexoUseCaseTest {

    @Mock
    private DocumentoRepository documentoRepository;

    @Mock
    private DocumentoApplicationLayerMapper mapper;

    private AgregarAnexoUseCase useCase;

    @BeforeEach
    void setUp() {
        useCase = new AgregarAnexoUseCase(documentoRepository, mapper);
    }

    @Nested
    @DisplayName("Escenarios exitosos")
    class EscenariosExitosos {

        @Test
        @DisplayName("debe agregar anexo exitosamente a documento en estado BORRADOR")
        void deberiaAgregarAnexoExitosamenteADocumentoBorrador() {
            // Arrange
            UUID documentId = UUID.randomUUID();
            var command = new AgregarAnexoCommand(
                    documentId,
                    "Anexo Test",
                    "Descripción del anexo",
                    UUID.randomUUID().toString(),
                    "999b5233407e71fb28c80c218006341179cd67a83b64a56c0ac25064e094c441",
                    1024L,
                    "pdf"
            );

            var documento = mock(Documento.class);
            when(documento.getEstado()).thenReturn(EstadoDocumento.BORRADOR);
            when(documentoRepository.findById(documentId)).thenReturn(Optional.of(documento));

            var documentoActualizado = mock(Documento.class);
            when(documentoRepository.save(documento)).thenReturn(documentoActualizado);

            var documentoDTO = mock(DocumentoDTO.class);
            when(mapper.toDTO(documentoActualizado)).thenReturn(documentoDTO);

            // Act
            DocumentoDTO result = useCase.execute(command);

            // Assert
            assertNotNull(result);
            assertEquals(documentoDTO, result);
            verify(documentoRepository).findById(documentId);
            verify(documento).agregarAnexo(any());
            verify(documentoRepository).save(documento);
            verify(mapper).toDTO(documentoActualizado);
        }

        @Test
        @DisplayName("debe agregar anexo exitosamente a documento en estado EN_TRAMITE")
        void deberiaAgregarAnexoExitosamenteADocumentoEnTramite() {
            // Arrange
            UUID documentId = UUID.randomUUID();
            var command = new AgregarAnexoCommand(
                    documentId,
                    "Anexo Test",
                    "Descripción del anexo",
                    UUID.randomUUID().toString(),
                    "999b5233407e71fb28c80c218006341179cd67a83b64a56c0ac25064e094c441",
                    1024L,
                    "pdf"
            );

            var documento = mock(Documento.class);
            when(documento.getEstado()).thenReturn(EstadoDocumento.EN_TRAMITE);
            when(documentoRepository.findById(documentId)).thenReturn(Optional.of(documento));

            var documentoActualizado = mock(Documento.class);
            when(documentoRepository.save(documento)).thenReturn(documentoActualizado);

            var documentoDTO = mock(DocumentoDTO.class);
            when(mapper.toDTO(documentoActualizado)).thenReturn(documentoDTO);

            // Act
            DocumentoDTO result = useCase.execute(command);

            // Assert
            assertNotNull(result);
            verify(documento).agregarAnexo(any());
            verify(documentoRepository).save(documento);
        }

        @Test
        @DisplayName("debe agregar anexo exitosamente a documento en estado FINALIZADO")
        void deberiaAgregarAnexoExitosamenteADocumentoFinalizado() {
            // Arrange
            UUID documentId = UUID.randomUUID();
            var command = new AgregarAnexoCommand(
                    documentId,
                    "Anexo Test",
                    "Descripción del anexo",
                    UUID.randomUUID().toString(),
                    "999b5233407e71fb28c80c218006341179cd67a83b64a56c0ac25064e094c441",
                    1024L,
                    "pdf"
            );

            var documento = mock(Documento.class);
            when(documento.getEstado()).thenReturn(EstadoDocumento.FINALIZADO);
            when(documentoRepository.findById(documentId)).thenReturn(Optional.of(documento));

            var documentoActualizado = mock(Documento.class);
            when(documentoRepository.save(documento)).thenReturn(documentoActualizado);

            var documentoDTO = mock(DocumentoDTO.class);
            when(mapper.toDTO(documentoActualizado)).thenReturn(documentoDTO);

            // Act
            DocumentoDTO result = useCase.execute(command);

            // Assert
            assertNotNull(result);
            verify(documento).agregarAnexo(any());
            verify(documentoRepository).save(documento);
        }
    }

    @Nested
    @DisplayName("Validaciones de errores")
    class ValidacionesErrores {

        @Test
        @DisplayName("debe lanzar excepción cuando el documento no existe")
        void deberiaLanzarExcepcionCuandoDocumentoNoExiste() {
            // Arrange
            UUID documentId = UUID.randomUUID();
            var command = new AgregarAnexoCommand(
                    documentId,
                    "Anexo Test",
                    "Descripción del anexo",
                    UUID.randomUUID().toString(),
                    "999b5233407e71fb28c80c218006341179cd67a83b64a56c0ac25064e094c441",
                    1024L,
                    "pdf"
            );

            when(documentoRepository.findById(documentId)).thenReturn(Optional.empty());

            // Act & Assert
            assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
            verify(documentoRepository).findById(documentId);
            verify(documentoRepository, never()).save(any());
            verify(mapper, never()).toDTO(any());
        }

        @Test
        @DisplayName("debe lanzar excepción cuando el documento está DESCARTADO")
        void deberiaLanzarExcepcionCuandoDocumentoEstaDescartado() {
            // Arrange
            UUID documentId = UUID.randomUUID();
            var command = new AgregarAnexoCommand(
                    documentId,
                    "Anexo Test",
                    "Descripción del anexo",
                    UUID.randomUUID().toString(),
                    "999b5233407e71fb28c80c218006341179cd67a83b64a56c0ac25064e094c441",
                    1024L,
                    "pdf"
            );

            var documento = mock(Documento.class);
            when(documento.getEstado()).thenReturn(EstadoDocumento.DESCARTADO);
            when(documentoRepository.findById(documentId)).thenReturn(Optional.of(documento));

            // Act & Assert
            assertThrows(DocumentoDescartadoException.class, () -> useCase.execute(command));
            verify(documentoRepository).findById(documentId);
            verify(documento, never()).agregarAnexo(any());
            verify(documentoRepository, never()).save(any());
            verify(mapper, never()).toDTO(any());
        }
    }
}
