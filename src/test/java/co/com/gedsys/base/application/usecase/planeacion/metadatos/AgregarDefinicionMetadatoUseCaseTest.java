package co.com.gedsys.base.application.usecase.planeacion.metadatos;

import co.com.gedsys.base.domain.metadato.DefinicionMetadato;
import co.com.gedsys.base.domain.metadato.enums.FormatoMetadatoEnum;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("AgregarDefinicionMetadatoUseCase")
class AgregarDefinicionMetadatoUseCaseTest {

    @Mock
    private DefinicionMetadatosRepository repository;

    private AgregarDefinicionMetadatoUseCase useCase;

    @BeforeEach
    void setUp() {
        useCase = new AgregarDefinicionMetadatoUseCase(repository);
    }

    @Nested
    @DisplayName("Al agregar una definición de metadato")
    class AgregarDefinicionMetadato {

        @Test
        @DisplayName("debería permitir agregar un metadato cuando no existe otro con el mismo nombre y tipo")
        void deberiaPermitirAgregarMetadatoCuandoNoExisteOtroConMismoNombreYTipo() {
            // Arrange
            var command = new AgregarDefinicionMetadatoCommand(
                    "nombreMetadato",
                    "Descripción del metadato",
                    TipoMetadatoEnum.CONTENIDO,
                    FormatoMetadatoEnum.ALFANUMERICO,
                    "requerido:true"
            );
            
            when(repository.checkStockByPatronAndTipo("nombreMetadato", TipoMetadatoEnum.CONTENIDO)).thenReturn(false);
            when(repository.save(any(DefinicionMetadato.class))).thenReturn(new DefinicionMetadato(
                    "nombreMetadato",
                    TipoMetadatoEnum.CONTENIDO,
                    FormatoMetadatoEnum.ALFANUMERICO
            ));

            // Act & Assert
            assertDoesNotThrow(() -> useCase.execute(command));
            verify(repository).checkStockByPatronAndTipo("nombreMetadato", TipoMetadatoEnum.CONTENIDO);
            verify(repository).save(any(DefinicionMetadato.class));
        }

        @Test
        @DisplayName("debería permitir agregar un metadato con el mismo nombre pero diferente tipo")
        void deberiaPermitirAgregarMetadatoConMismoNombrePeroDistintoTipo() {
            // Arrange
            var command = new AgregarDefinicionMetadatoCommand(
                    "nombreMetadato",
                    "Descripción del metadato",
                    TipoMetadatoEnum.GESTION,
                    FormatoMetadatoEnum.ALFANUMERICO,
                    "requerido:true"
            );
            
            when(repository.checkStockByPatronAndTipo("nombreMetadato", TipoMetadatoEnum.GESTION)).thenReturn(false);
            when(repository.save(any(DefinicionMetadato.class))).thenReturn(new DefinicionMetadato(
                    "nombreMetadato",
                    TipoMetadatoEnum.GESTION,
                    FormatoMetadatoEnum.ALFANUMERICO
            ));

            // Act & Assert
            assertDoesNotThrow(() -> useCase.execute(command));
            verify(repository).checkStockByPatronAndTipo("nombreMetadato", TipoMetadatoEnum.GESTION);
            verify(repository).save(any(DefinicionMetadato.class));
        }

        @Test
        @DisplayName("debería lanzar excepción cuando ya existe un metadato con el mismo nombre y tipo")
        void deberiaLanzarExcepcionCuandoYaExisteMetadatoConMismoNombreYTipo() {
            // Arrange
            var command = new AgregarDefinicionMetadatoCommand(
                    "nombreMetadato",
                    "Descripción del metadato",
                    TipoMetadatoEnum.CONTENIDO,
                    FormatoMetadatoEnum.ALFANUMERICO,
                    "requerido:true"
            );
            
            when(repository.checkStockByPatronAndTipo("nombreMetadato", TipoMetadatoEnum.CONTENIDO)).thenReturn(true);

            // Act & Assert
            var exception = assertThrows(IllegalArgumentException.class, () -> useCase.execute(command));
            assertEquals("Ya existe una definición de metadato con el mismo nombre y tipo", exception.getMessage());
            verify(repository).checkStockByPatronAndTipo("nombreMetadato", TipoMetadatoEnum.CONTENIDO);
            verify(repository, never()).save(any(DefinicionMetadato.class));
        }
    }
}