package co.com.gedsys.base.application.usecase.planeacion.usuarios_externos;

import co.com.gedsys.base.application.common.EntityAlreadyExistsException;
import co.com.gedsys.base.application.mapper.ExternalUsersAppMapper;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.CreateExternalUserCommand;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserIdentificationType;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("Pruebas para CreateExternalUserUseCase")
class CreateExternalUserUseCaseTest {

    @Mock
    private ExternalUsersRepository repository;
    
    @Mock
    private ExternalUsersAppMapper mapper;
    
    private CreateExternalUserUseCase useCase;

    @BeforeEach
    void setUp() {
        useCase = new CreateExternalUserUseCase(repository, mapper);
    }

    @Nested
    @DisplayName("con tipo NA")
    class ConTipoNA {

        @Test
        @DisplayName("debe crear usuario tipo NA exitosamente")
        void deberiaCrearUsuarioTipoNAExitosamente() {
            // Arrange
            var command = new CreateExternalUserCommand(
                "Usuario Sin ID", 
                "Saludo", 
                "NA", 
                "12345", // Este valor será normalizado a null
                "Notas", 
                new ArrayList<>()
            );
            
            var expectedUser = new ExternalUser("Usuario Sin ID", "NA", null);
            
            when(repository.existsByNameAndIdentificationType("Usuario Sin ID", ExternalUserIdentificationType.NA))
                .thenReturn(false);
            when(mapper.toDomain(any(CreateExternalUserCommand.class), any(ExternalUser.class))).thenReturn(expectedUser);
            when(repository.save(any(ExternalUser.class))).thenReturn(expectedUser);
            when(mapper.toDto(expectedUser)).thenReturn(null); // DTO no es relevante para esta prueba
            
            // Act & Assert
            assertDoesNotThrow(() -> useCase.execute(command));
            
            // Verify que se normalizó el número a null
            verify(repository).existsByNameAndIdentificationType("Usuario Sin ID", ExternalUserIdentificationType.NA);
            verify(repository, never()).checkStock(any(ExternalUser.class));
        }

        @Test
        @DisplayName("debe lanzar excepción si ya existe usuario con mismo nombre y tipo NA")
        void deberiaLanzarExcepcionSiYaExisteUsuarioConMismoNombreYTipoNA() {
            // Arrange
            var command = new CreateExternalUserCommand(
                "Usuario Duplicado", 
                "Saludo", 
                "NA", 
                null, 
                "Notas", 
                new ArrayList<>()
            );
            
            when(repository.existsByNameAndIdentificationType("Usuario Duplicado", ExternalUserIdentificationType.NA))
                .thenReturn(true);
            
            // Act & Assert
            var exception = assertThrows(EntityAlreadyExistsException.class, 
                () -> useCase.execute(command));
            assertTrue(exception.getMessage().contains("Ya existe un usuario con nombre 'Usuario Duplicado' y tipo NA"));
        }

        @Test
        @DisplayName("debe normalizar número de identificación a null para tipo NA")
        void deberiaNormalizarNumeroDeIdentificacionANullParaTipoNA() {
            // Arrange
            var command = new CreateExternalUserCommand(
                "Usuario Test", 
                "Saludo", 
                "NA", 
                "cualquier-valor", // Este valor debe ser normalizado a null
                "Notas", 
                new ArrayList<>()
            );
            
            when(repository.existsByNameAndIdentificationType(anyString(), any())).thenReturn(false);
            when(mapper.toDomain(any(CreateExternalUserCommand.class), any(ExternalUser.class))).thenReturn(new ExternalUser("Usuario Test", "NA", null));
            when(repository.save(any())).thenReturn(new ExternalUser("Usuario Test", "NA", null));
            when(mapper.toDto(any())).thenReturn(null);
            
            // Act
            useCase.execute(command);
            
            // Assert - Verificar que se creó el usuario con número null
            verify(repository).existsByNameAndIdentificationType("Usuario Test", ExternalUserIdentificationType.NA);
        }
    }

    @Nested
    @DisplayName("con otros tipos de identificación")
    class ConOtrosTiposDeIdentificacion {

        @Test
        @DisplayName("debe crear usuario tipo CC exitosamente")
        void deberiaCrearUsuarioTipoCCExitosamente() {
            // Arrange
            var command = new CreateExternalUserCommand(
                "Usuario CC", 
                "Saludo", 
                "CC", 
                "12345678", 
                "Notas", 
                new ArrayList<>()
            );
            
            var expectedUser = new ExternalUser("Usuario CC", "CC", "12345678");
            
            when(repository.checkStock(any(ExternalUser.class))).thenReturn(false);
            when(mapper.toDomain(any(CreateExternalUserCommand.class), any(ExternalUser.class))).thenReturn(expectedUser);
            when(repository.save(any(ExternalUser.class))).thenReturn(expectedUser);
            when(mapper.toDto(expectedUser)).thenReturn(null);
            
            // Act & Assert
            assertDoesNotThrow(() -> useCase.execute(command));
            
            // Verify que se usó checkStock para tipos distintos a NA
            verify(repository).checkStock(any(ExternalUser.class));
            verify(repository, never()).existsByNameAndIdentificationType(anyString(), any());
        }

        @Test
        @DisplayName("debe lanzar excepción si ya existe usuario con mismo número de identificación")
        void deberiaLanzarExcepcionSiYaExisteUsuarioConMismoNumeroDeIdentificacion() {
            // Arrange
            var command = new CreateExternalUserCommand(
                "Usuario CC", 
                "Saludo", 
                "CC", 
                "12345678", 
                "Notas", 
                new ArrayList<>()
            );
            
            when(repository.checkStock(any(ExternalUser.class))).thenReturn(true);
            
            // Act & Assert
            var exception = assertThrows(EntityAlreadyExistsException.class, 
                () -> useCase.execute(command));
            assertTrue(exception.getMessage().contains("El usuario con identificación 12345678 ya existe"));
        }

        @Test
        @DisplayName("no debe normalizar número para tipos distintos a NA")
        void noDeberiaNormalizarNumeroParaTiposDistintosANA() {
            // Arrange
            var command = new CreateExternalUserCommand(
                "Usuario CE", 
                "Saludo", 
                "CE", 
                "CE123456", 
                "Notas", 
                new ArrayList<>()
            );
            
            when(repository.checkStock(any(ExternalUser.class))).thenReturn(false);
            when(mapper.toDomain(any(CreateExternalUserCommand.class), any(ExternalUser.class))).thenReturn(new ExternalUser("Usuario CE", "CE", "CE123456"));
            when(repository.save(any())).thenReturn(new ExternalUser("Usuario CE", "CE", "CE123456"));
            when(mapper.toDto(any())).thenReturn(null);
            
            // Act
            useCase.execute(command);
            
            // Assert - Verificar que se usó checkStock (lógica para tipos no-NA)
            verify(repository).checkStock(any(ExternalUser.class));
        }
    }
}
