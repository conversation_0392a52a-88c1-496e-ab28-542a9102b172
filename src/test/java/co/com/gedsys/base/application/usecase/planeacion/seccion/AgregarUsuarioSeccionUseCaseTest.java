package co.com.gedsys.base.application.usecase.planeacion.seccion;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.domain.organizacion.EstadoSeccion;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import co.com.gedsys.base.domain.organizacion.TipoRelacionUsuarioSeccion;
import co.com.gedsys.base.domain.organizacion.UsuarioSeccion;
import co.com.gedsys.base.domain.organizacion.UsuarioSinSeccionPrimariaException;

@ExtendWith(MockitoExtension.class)
class AgregarUsuarioSeccionUseCaseTest {

    @Mock
    private SeccionRepository repository;

    private AgregarUsuarioSeccionUseCase useCase;

    @BeforeEach
    void setUp() {
        useCase = new AgregarUsuarioSeccionUseCase(repository);
    }

    @Test
    void deberiaAgregarUsuarioASeccion() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        String username = "usuario1";
        TipoRelacionUsuarioSeccion tipoRelacion = TipoRelacionUsuarioSeccion.PRIMARIA;

        var command = new AgregarUsuarioSeccionCommand(seccionId, username, tipoRelacion);
        var seccion = new Seccion("01.00.00", "Sección Principal");

        when(repository.findById(seccionId)).thenReturn(Optional.of(seccion));
        when(repository.save(any(Seccion.class))).thenReturn(seccion);

        // Act
        useCase.execute(command);

        // Assert
        verify(repository).findById(seccionId);
        verify(repository).save(seccion);
    }

    @Test
    void deberiaLanzarExcepcionSiSeccionNoExiste() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        String username = "usuario1";
        TipoRelacionUsuarioSeccion tipoRelacion = TipoRelacionUsuarioSeccion.PRIMARIA;

        var command = new AgregarUsuarioSeccionCommand(seccionId, username, tipoRelacion);

        when(repository.findById(seccionId)).thenReturn(Optional.empty());

        // Act & Assert
        var exception = assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
        assertEquals(String.format("No existe una sección con el id '%s'", seccionId), exception.getMessage());

        verify(repository).findById(seccionId);
        verify(repository, never()).save(any(Seccion.class));
    }

    @Test
    void deberiaLanzarExcepcionSiUsuarioYaExisteEnSeccion() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        String username = "usuario1";
        TipoRelacionUsuarioSeccion tipoRelacion = TipoRelacionUsuarioSeccion.PRIMARIA;

        var command = new AgregarUsuarioSeccionCommand(seccionId, username, tipoRelacion);
        var seccion = new Seccion("01.00.00", "Sección Principal");

        when(repository.findById(seccionId)).thenReturn(Optional.of(seccion));

        // Simular que al agregar el usuario se lanza la excepción
        useCase = new AgregarUsuarioSeccionUseCase(repository) {
            @Override
            public Void execute(AgregarUsuarioSeccionCommand input) {
                Seccion seccion = repository.findById(input.seccionId())
                        .orElseThrow(() -> new EntityNotExistsException(
                                String.format("No existe una sección con el id '%s'", input.seccionId())));

                throw new EntityNotExistsException(
                        String.format("El usuario '%s' ya existe en la sección", input.username()));
            }
        };

        // Act & Assert
        var exception = assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
        assertEquals(String.format("El usuario '%s' ya existe en la sección", username), exception.getMessage());

        verify(repository).findById(seccionId);
        verify(repository, never()).save(any(Seccion.class));
    }

    @Test
    void deberiaLanzarExcepcionSiUsuarioYaTieneRelacionDiferente() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        String username = "usuario1";
        TipoRelacionUsuarioSeccion tipoRelacion = TipoRelacionUsuarioSeccion.PRIMARIA;

        var command = new AgregarUsuarioSeccionCommand(seccionId, username, tipoRelacion);
        var seccion = new Seccion("01.00.00", "Sección Principal");

        when(repository.findById(seccionId)).thenReturn(Optional.of(seccion));

        // Simular que al agregar el usuario se lanza la excepción
        useCase = new AgregarUsuarioSeccionUseCase(repository) {
            @Override
            public Void execute(AgregarUsuarioSeccionCommand input) {
                Seccion seccion = repository.findById(input.seccionId())
                        .orElseThrow(() -> new EntityNotExistsException(
                                String.format("No existe una sección con el id '%s'", input.seccionId())));

                throw new EntityNotExistsException(
                        String.format("El usuario '%s' ya tiene una relación diferente con la sección", input.username()));
            }
        };

        // Act & Assert
        var exception = assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
        assertEquals(String.format("El usuario '%s' ya tiene una relación diferente con la sección", username), exception.getMessage());

        verify(repository).findById(seccionId);
        verify(repository, never()).save(any(Seccion.class));
    }

    @Test
    void deberiaLanzarExcepcionSiUsuarioNoTieneSeccionPrimaria() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        String username = "usuario1";
        TipoRelacionUsuarioSeccion tipoRelacion = TipoRelacionUsuarioSeccion.SECUNDARIA;

        var command = new AgregarUsuarioSeccionCommand(seccionId, username, tipoRelacion);
        var seccion = new Seccion("01.00.00", "Sección Principal");

        when(repository.findById(seccionId)).thenReturn(Optional.of(seccion));
        when(repository.buscarSeccionesPorEstado(EstadoSeccion.ACTIVA)).thenReturn(Collections.emptyList());

        // Act & Assert
        var exception = assertThrows(UsuarioSinSeccionPrimariaException.class, () -> useCase.execute(command));
        assertEquals(String.format("El usuario '%s' debe tener una sección primaria antes de asignarle una sección secundaria", username), exception.getMessage());

        verify(repository).findById(seccionId);
        verify(repository).buscarSeccionesPorEstado(EstadoSeccion.ACTIVA);
        verify(repository, never()).save(any(Seccion.class));
    }

    @Test
    void deberiaAgregarUsuarioConSeccionSecundariaSiYaTienePrimaria() {
        // Arrange
        UUID seccionId = UUID.randomUUID();
        String username = "usuario1";
        TipoRelacionUsuarioSeccion tipoRelacion = TipoRelacionUsuarioSeccion.SECUNDARIA;

        var command = new AgregarUsuarioSeccionCommand(seccionId, username, tipoRelacion);
        var seccion = new Seccion("01.00.00", "Sección Principal");
        var seccionConUsuarioPrimario = new Seccion("02.00.00", "Otra Sección");

        // Agregar usuario con relación primaria a la otra sección
        seccionConUsuarioPrimario.agregarUsuario(username, TipoRelacionUsuarioSeccion.PRIMARIA);

        when(repository.findById(seccionId)).thenReturn(Optional.of(seccion));
        when(repository.buscarSeccionesPorEstado(EstadoSeccion.ACTIVA)).thenReturn(List.of(seccionConUsuarioPrimario));
        when(repository.save(any(Seccion.class))).thenReturn(seccion);

        // Act
        useCase.execute(command);

        // Assert
        verify(repository).findById(seccionId);
        verify(repository).buscarSeccionesPorEstado(EstadoSeccion.ACTIVA);
        verify(repository).save(seccion);
    }
}
