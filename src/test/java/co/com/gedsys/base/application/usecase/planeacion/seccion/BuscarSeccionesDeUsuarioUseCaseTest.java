package co.com.gedsys.base.application.usecase.planeacion.seccion;

import co.com.gedsys.base.application.mapper.SeccionUseCaseMapper;
import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import co.com.gedsys.base.domain.organizacion.TipoRelacionUsuarioSeccion;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

public class BuscarSeccionesDeUsuarioUseCaseTest {

    @Mock
    private SeccionRepository seccionRepository;
    @Mock
    private SeccionUseCaseMapper seccionUseCaseMapper;

    private BuscarSeccionesDeUsuarioUseCase useCase;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        useCase = new BuscarSeccionesDeUsuarioUseCase(seccionRepository, seccionUseCaseMapper);
    }

    @Test
    void deberiaRetornarSeccionesDeUsuario() {
        // Arrange
        String username = "usuario1";
        
        // Crear sección
        Seccion seccion = new Seccion("01.00.00", "Sección Principal");
        seccion.setId(UUID.randomUUID());
        seccion.setResponsable("Responsable");
        
        // Agregar usuario a la sección
        seccion.agregarUsuario(username, TipoRelacionUsuarioSeccion.PRIMARIA);
        
        // Crear DTO esperado
        SeccionDTO seccionDTO = new SeccionDTO(
                seccion.getId().toString(), 
                seccion.getEstado().name(), 
                null, 
                seccion.getCodigo(), 
                seccion.getNombre(), 
                seccion.getResponsable());
        
        // Configurar mocks
        when(seccionRepository.buscarSeccionesPorUsuario(username)).thenReturn(List.of(seccion));
        when(seccionUseCaseMapper.toDto(seccion)).thenReturn(seccionDTO);

        // Act
        List<SeccionDTO> resultado = useCase.execute(username);

        // Assert
        assertNotNull(resultado);
        assertEquals(1, resultado.size());
        assertEquals(seccionDTO, resultado.get(0));
        
        // Verify
        verify(seccionRepository).buscarSeccionesPorUsuario(username);
        verify(seccionUseCaseMapper).toDto(seccion);
    }

    @Test
    void deberiaRetornarListaVaciaSiUsuarioNoTieneRelaciones() {
        // Arrange
        String username = "usuario2";
        
        // Configurar mock para retornar lista vacía
        when(seccionRepository.buscarSeccionesPorUsuario(username)).thenReturn(List.of());
        
        // Act
        List<SeccionDTO> resultado = useCase.execute(username);
        
        // Assert
        assertNotNull(resultado);
        assertEquals(0, resultado.size());
        
        // Verify
        verify(seccionRepository).buscarSeccionesPorUsuario(username);
        verify(seccionUseCaseMapper, never()).toDto(any());
    }
}
