package co.com.gedsys.base.application.usecase.planeacion.seccion;

import co.com.gedsys.base.application.usecase.planeacion.instrumentos.BuscarOrganigramaQuery;
import co.com.gedsys.base.domain.organizacion.EstadoSeccion;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BuscarOrganigramaUseCaseTest {

    @Mock
    private SeccionRepository repository;

    private BuscarOrganigramaUseCase useCase;

    @BeforeEach
    void setUp() {
        useCase = new BuscarOrganigramaUseCase(repository);
    }

    @Test
    @DisplayName("debe construir correctamente el árbol completo del organigrama")
    void debeConstruirArbolCompleto() {
        // Arrange
        Seccion despachoAlcalde = new Seccion("01.00.00", "Despacho del Alcalde");
        despachoAlcalde.setId(UUID.fromString("91f94320-526f-4b8c-bfb4-1d286e2e161f"));

        Seccion secretariaGobierno = new Seccion("01.01.00", "Secretaría de Gobierno");
        secretariaGobierno.setId(UUID.fromString("7db4ac97-f37a-4733-bf75-df7194d59c82"));
        secretariaGobierno.setPadre(despachoAlcalde);

        Seccion juridica = new Seccion("01.01.01", "Juridica");
        juridica.setId(UUID.fromString("2f2ec234-ed88-441f-ac6d-************"));
        juridica.setPadre(secretariaGobierno);

        Seccion secretariaAuxGobierno = new Seccion("02.00.00", "Secretaria aux de Gobierno");
        secretariaAuxGobierno.setId(UUID.fromString("fd23ae64-22cf-41dd-813d-dd69a077b6ca"));
        secretariaAuxGobierno.setPadre(despachoAlcalde);

        when(repository.buscarSeccionesPorEstado(EstadoSeccion.ACTIVA))
                .thenReturn(Arrays.asList(despachoAlcalde, secretariaGobierno, juridica, secretariaAuxGobierno));

        // Act
        Seccion resultado = useCase.execute(new BuscarOrganigramaQuery(EstadoSeccion.ACTIVA));

        // Assert
        assertThat(resultado).isNotNull();
        assertThat(resultado.getId()).isEqualTo(despachoAlcalde.getId());
        assertThat(resultado.getCodigo()).isEqualTo("01.00.00");
        assertThat(resultado.getHijos()).hasSize(2);

        // Verificar estructura completa
        Seccion secGobierno = resultado.getHijos().stream()
                .filter(h -> h.getCodigo().equals("01.01.00"))
                .findFirst()
                .orElse(null);

        assertThat(secGobierno).isNotNull();
        assertThat(secGobierno.getHijos()).hasSize(1);
        assertThat(secGobierno.getHijos().iterator().next().getCodigo()).isEqualTo("01.01.01");
    }
}