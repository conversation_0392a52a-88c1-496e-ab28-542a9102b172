package co.com.gedsys.base.application.usecase.planeacion.seccion;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Optional;
import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.domain.organizacion.CodigoSeccionException;
import co.com.gedsys.base.domain.organizacion.Seccion;
import co.com.gedsys.base.domain.organizacion.SeccionRepository;

@ExtendWith(MockitoExtension.class)
@DisplayName("Pruebas para el caso de uso de mover sección")
class MoverSeccionUseCaseTest {

    @Mock
    private SeccionRepository repository;

    private MoverSeccionUseCase useCase;

    @BeforeEach
    void setUp() {
        useCase = new MoverSeccionUseCase(repository);
    }

    @Nested
    @DisplayName("Cuando la sección y el nuevo padre existen")
    class CuandoSeccionYPadreExisten {

        @Test
        @DisplayName("Debería mover la sección exitosamente")
        void debeMoverSeccionExitosamente() {
            // Arrange
            UUID seccionId = UUID.randomUUID();
            UUID nuevoPadreId = UUID.randomUUID();
            String nuevoCodigo = "02.01.00";

            var command = new MoverSeccionCommand(seccionId, nuevoPadreId, nuevoCodigo);
            
            var seccionAMover = new Seccion("01.01.00", "Sección A Mover");
            var nuevoPadre = new Seccion("02.00.00", "Nuevo Padre");
            var nodoPrincipal = new Seccion("01.00.00", "Nodo Principal");
            var seccionMovida = new Seccion(nuevoCodigo, seccionAMover.getNombre());
            seccionMovida.setPadre(nuevoPadre);

            when(repository.findById(seccionId)).thenReturn(Optional.of(seccionAMover));
            when(repository.findById(nuevoPadreId)).thenReturn(Optional.of(nuevoPadre));
            when(repository.buscarNodoPrincipalDelOrganigrama()).thenReturn(Optional.of(nodoPrincipal));
            when(repository.save(any(Seccion.class))).thenReturn(seccionMovida);

            // Act
            var resultado = useCase.execute(command);

            // Assert
            assertNotNull(resultado);
            assertEquals(nuevoCodigo, resultado.getCodigo());
            assertEquals(nuevoPadre, resultado.getPadre());
            verify(repository).findById(seccionId);
            verify(repository).findById(nuevoPadreId);
            verify(repository).save(any(Seccion.class));
        }
    }

    @Nested
    @DisplayName("Cuando la sección es el nodo principal")
    class CuandoSeccionEsNodoPrincipal {

        @Test
        @DisplayName("Debería lanzar excepción al intentar mover el nodo principal")
        void debeLanzarExcepcionAlMoverNodoPrincipal() {
            // Arrange
            UUID seccionId = UUID.randomUUID();
            UUID nuevoPadreId = UUID.randomUUID();
            
            var seccionPrincipal = new Seccion("01.00.00", "Sección Principal");
            var nuevoPadre = new Seccion("02.00.00", "Nuevo Padre");
            var command = new MoverSeccionCommand(seccionId, nuevoPadreId, "02.00.00");

            when(repository.findById(seccionId)).thenReturn(Optional.of(seccionPrincipal));
            when(repository.findById(nuevoPadreId)).thenReturn(Optional.of(nuevoPadre));
            when(repository.buscarNodoPrincipalDelOrganigrama())
                .thenReturn(Optional.of(seccionPrincipal));

            // Act & Assert
            assertThrows(IllegalArgumentException.class, () -> useCase.execute(command));
            verify(repository).findById(seccionId);
            verify(repository).findById(nuevoPadreId);
            verify(repository).buscarNodoPrincipalDelOrganigrama();
            verify(repository, never()).save(any());
        }
    }

    @Nested
    @DisplayName("Cuando hay errores de validación")
    class CuandoHayErroresValidacion {

        @Test
        @DisplayName("Debería lanzar excepción cuando la sección no existe")
        void debeLanzarExcepcionCuandoSeccionNoExiste() {
            // Arrange
            UUID seccionId = UUID.randomUUID();
            UUID nuevoPadreId = UUID.randomUUID();
            var command = new MoverSeccionCommand(seccionId, nuevoPadreId, "02.01.00");

            when(repository.findById(seccionId)).thenReturn(Optional.empty());

            // Act & Assert
            assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
            verify(repository).findById(seccionId);
            verify(repository, never()).findById(nuevoPadreId);
            verify(repository, never()).buscarNodoPrincipalDelOrganigrama();
            verify(repository, never()).save(any());
        }

        @Test
        @DisplayName("Debería lanzar excepción cuando el nuevo padre no existe")
        void debeLanzarExcepcionCuandoPadreNoExiste() {
            // Arrange
            UUID seccionId = UUID.randomUUID();
            UUID nuevoPadreId = UUID.randomUUID();
            var command = new MoverSeccionCommand(seccionId, nuevoPadreId, "02.01.00");
            
            var seccionAMover = new Seccion("01.01.00", "Sección A Mover");

            when(repository.findById(seccionId)).thenReturn(Optional.of(seccionAMover));
            when(repository.findById(nuevoPadreId)).thenReturn(Optional.empty());

            // Act & Assert
            assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
            verify(repository).findById(seccionId);
            verify(repository).findById(nuevoPadreId);
            verify(repository, never()).buscarNodoPrincipalDelOrganigrama();
            verify(repository, never()).save(any());
        }

        @Test
        @DisplayName("Debería lanzar excepción cuando el código no cumple el formato")
        void debeLanzarExcepcionCuandoCodigoNoEsValido() {
            // Arrange
            UUID seccionId = UUID.randomUUID();
            UUID nuevoPadreId  = UUID.randomUUID();
            var command = new MoverSeccionCommand(seccionId, nuevoPadreId, "codigo-invalido");
            
            var seccionAMover = new Seccion("01.01.00", "Sección A Mover");
            var nuevoPadre = new Seccion("02.00.00", "Nuevo Padre");
            var nodoPrincipal = new Seccion("01.00.00", "Nodo Principal");

            when(repository.findById(seccionId)).thenReturn(Optional.of(seccionAMover));
            when(repository.findById(nuevoPadreId)).thenReturn(Optional.of(nuevoPadre));
            when(repository.buscarNodoPrincipalDelOrganigrama()).thenReturn(Optional.of(nodoPrincipal));

            // Act & Assert
            assertThrows(CodigoSeccionException.class, () -> useCase.execute(command));
            verify(repository).findById(seccionId);
            verify(repository).findById(nuevoPadreId);
            verify(repository).buscarNodoPrincipalDelOrganigrama();
            verify(repository, never()).save(any());
        }
    }
} 