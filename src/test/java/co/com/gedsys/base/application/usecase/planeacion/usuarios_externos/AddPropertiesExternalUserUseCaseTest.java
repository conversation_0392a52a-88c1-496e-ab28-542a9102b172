package co.com.gedsys.base.application.usecase.planeacion.usuarios_externos;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.dto.ExternalUserDto;
import co.com.gedsys.base.application.mapper.ExternalUsersAppMapper;
import co.com.gedsys.base.application.usecase.planeacion.entidades_territoriales.AddPropertiesExternalUserCommand;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserProperty;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserPropertyType;
import co.com.gedsys.base.domain.usuario_externo.ExternalUsersRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("Pruebas para AddPropertiesExternalUserUseCase")
class AddPropertiesExternalUserUseCaseTest {

    @Mock
    private ExternalUsersRepository externalUsersRepository;

    @Mock
    private ExternalUsersAppMapper mapper;

    @InjectMocks
    private AddPropertiesExternalUserUseCase useCase;

    private UUID externalUserId;
    private ExternalUser externalUser;
    private AddPropertiesExternalUserCommand command;

    @BeforeEach
    void setUp() {
        externalUserId = UUID.randomUUID();
        externalUser = new ExternalUser("Juan Pérez", "CC", "12345678");
        externalUser.setId(externalUserId);
        externalUser.setProperties(new ArrayList<>());

        var propertyDS = new AddPropertiesExternalUserCommand.PropertyDS(
                "EMAIL", "Email principal", "<EMAIL>", "Correo principal"
        );
        command = new AddPropertiesExternalUserCommand(externalUserId, List.of(propertyDS));
    }

    @Test
    @DisplayName("debe agregar propiedades exitosamente cuando no hay duplicados")
    void debeAgregarPropiedadesExitosamenteCuandoNoHayDuplicados() {
        // Arrange
        var property = new ExternalUserProperty(ExternalUserPropertyType.EMAIL, "Email principal", "<EMAIL>");
        var expectedDto = new ExternalUserDto(externalUserId, "Juan Pérez", "CC", "12345678", 
                "Estimado", "Notas", "ACTIVO", List.of());

        when(externalUsersRepository.findById(externalUserId)).thenReturn(Optional.of(externalUser));
        when(mapper.toDomain(any(AddPropertiesExternalUserCommand.PropertyDS.class))).thenReturn(property);
        when(externalUsersRepository.save(any(ExternalUser.class))).thenReturn(externalUser);
        when(mapper.toDto(any(ExternalUser.class))).thenReturn(expectedDto);

        // Act
        ExternalUserDto result = useCase.execute(command);

        // Assert
        assertNotNull(result);
        assertEquals(externalUserId, result.id());
        verify(externalUsersRepository).findById(externalUserId);
        verify(externalUsersRepository).save(externalUser);
        verify(mapper).toDomain(any(AddPropertiesExternalUserCommand.PropertyDS.class));
        verify(mapper).toDto(externalUser);
    }

    @Test
    @DisplayName("debe lanzar excepción cuando el usuario externo no existe")
    void debeLanzarExcepcionCuandoElUsuarioExternoNoExiste() {
        // Arrange
        when(externalUsersRepository.findById(externalUserId)).thenReturn(Optional.empty());

        // Act & Assert
        var exception = assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
        assertTrue(exception.getMessage().contains(externalUserId.toString()));
        verify(externalUsersRepository).findById(externalUserId);
        verify(externalUsersRepository, never()).save(any());
    }

    @Test
    @DisplayName("debe lanzar excepción cuando la lista de propiedades está vacía")
    void debeLanzarExcepcionCuandoLaListaDePropiedadesEstaVacia() {
        // Arrange
        var emptyCommand = new AddPropertiesExternalUserCommand(externalUserId, List.of());

        // Act & Assert
        var exception = assertThrows(IllegalArgumentException.class, () -> useCase.execute(emptyCommand));
        assertEquals("No hay propiedades para añadir", exception.getMessage());
        verify(externalUsersRepository, never()).findById(any());
    }

    @Test
    @DisplayName("debe convertir DuplicatePropertyException a IllegalArgumentException")
    void debeConvertirDuplicatePropertyExceptionAIllegalArgumentException() {
        // Arrange
        var existingProperty = new ExternalUserProperty(ExternalUserPropertyType.EMAIL, "Email existente", "<EMAIL>");
        externalUser.getProperties().add(existingProperty);
        
        var duplicateProperty = new ExternalUserProperty(ExternalUserPropertyType.EMAIL, "Email duplicado", "<EMAIL>");

        when(externalUsersRepository.findById(externalUserId)).thenReturn(Optional.of(externalUser));
        when(mapper.toDomain(any(AddPropertiesExternalUserCommand.PropertyDS.class))).thenReturn(duplicateProperty);

        // Act & Assert
        var exception = assertThrows(IllegalArgumentException.class, () -> useCase.execute(command));
        assertTrue(exception.getMessage().contains("<EMAIL>"));
        verify(externalUsersRepository).findById(externalUserId);
        verify(externalUsersRepository, never()).save(any());
    }
}
