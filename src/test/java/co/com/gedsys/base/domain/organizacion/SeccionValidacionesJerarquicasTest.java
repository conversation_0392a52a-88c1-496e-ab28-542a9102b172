package co.com.gedsys.base.domain.organizacion;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("Validaciones Jerárquicas de Seccion")
class SeccionValidacionesJerarquicasTest {

    private Seccion padre;
    private Seccion hijo;
    private Seccion nieto;

    @BeforeEach
    void setUp() {
        padre = new Seccion("01.00.00", "Despacho Alcalde");
        padre.setResponsable("Alcalde");
        padre.setEstado(EstadoSeccion.ACTIVA);

        hijo = new Seccion("01.01.00", "Secretaría Gobierno");
        hijo.setResponsable("Secretario Gobierno");
        hijo.setEstado(EstadoSeccion.INACTIVA);
        hijo.setPadre(padre);

        nieto = new Seccion("01.01.01", "Auxiliar Gobierno");
        nieto.setResponsable("Auxiliar");
        nieto.setEstado(EstadoSeccion.INACTIVA);
        nieto.setPadre(hijo);
    }

    @Nested
    @DisplayName("Validaciones de Activación")
    class ValidacionesActivacion {

        @Test
        @DisplayName("debe permitir activar sección cuando padre está activo")
        void deberiaPermitirActivarSeccionConPadreActivo() {
            // Arrange
            padre.setEstado(EstadoSeccion.ACTIVA);
            hijo.setEstado(EstadoSeccion.INACTIVA);

            // Act & Assert
            hijo.activar();
            assertThat(hijo.getEstado()).isEqualTo(EstadoSeccion.ACTIVA);
        }

        @Test
        @DisplayName("debe permitir activar sección sin validar padre (validación en caso de uso)")
        void deberiaPermitirActivarSeccionSinValidarPadre() {
            // Arrange
            padre.setEstado(EstadoSeccion.INACTIVA);
            hijo.setEstado(EstadoSeccion.INACTIVA);

            // Act & Assert - La validación de padre se hace en el caso de uso, no en la entidad
            hijo.activar();
            assertThat(hijo.getEstado()).isEqualTo(EstadoSeccion.ACTIVA);
        }

        @Test
        @DisplayName("debe permitir activar sección sin padre")
        void deberiaPermitirActivarSeccionSinPadre() {
            // Arrange
            var seccionRaiz = new Seccion("02.00.00", "Otro Despacho");
            seccionRaiz.setResponsable("Responsable");
            seccionRaiz.setEstado(EstadoSeccion.INACTIVA);

            // Act & Assert
            seccionRaiz.activar();
            assertThat(seccionRaiz.getEstado()).isEqualTo(EstadoSeccion.ACTIVA);
        }
    }

    @Nested
    @DisplayName("Validaciones de Inactivación")
    class ValidacionesInactivacion {

        @Test
        @DisplayName("debe permitir inactivar sección sin hijos activos")
        void deberiaPermitirInactivarSeccionSinHijosActivos() {
            // Arrange
            hijo.setEstado(EstadoSeccion.ACTIVA);
            nieto.setEstado(EstadoSeccion.INACTIVA);

            // Act & Assert
            hijo.inactivar();
            assertThat(hijo.getEstado()).isEqualTo(EstadoSeccion.INACTIVA);
        }

        @Test
        @DisplayName("debe permitir inactivar sección sin validar hijos (validación en caso de uso)")
        void deberiaPermitirInactivarSeccionSinValidarHijos() {
            // Arrange
            padre.setEstado(EstadoSeccion.ACTIVA);
            hijo.setEstado(EstadoSeccion.ACTIVA);

            // Act & Assert - La validación de hijos se hace en el caso de uso, no en la entidad
            padre.inactivar();
            assertThat(padre.getEstado()).isEqualTo(EstadoSeccion.INACTIVA);
        }

        @Test
        @DisplayName("debe permitir inactivar sección sin hijos")
        void deberiaPermitirInactivarSeccionSinHijos() {
            // Arrange
            nieto.setEstado(EstadoSeccion.ACTIVA);

            // Act & Assert
            nieto.inactivar();
            assertThat(nieto.getEstado()).isEqualTo(EstadoSeccion.INACTIVA);
        }

        @Test
        @DisplayName("debe permitir inactivar sección con múltiples hijos (validación en caso de uso)")
        void deberiaPermitirInactivarSeccionConMultiplesHijos() {
            // Arrange
            var hijo2 = new Seccion("01.02.00", "Secretaría Hacienda");
            hijo2.setResponsable("Secretario Hacienda");
            hijo2.setEstado(EstadoSeccion.ACTIVA);
            hijo2.setPadre(padre);

            hijo.setEstado(EstadoSeccion.ACTIVA);
            padre.setEstado(EstadoSeccion.ACTIVA);

            // Act & Assert - La validación de múltiples hijos se hace en el caso de uso
            padre.inactivar();
            assertThat(padre.getEstado()).isEqualTo(EstadoSeccion.INACTIVA);
        }
    }
}
