package co.com.gedsys.base.domain.documento;

import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class FirmaUsuarioTest {
    @Test
    void alwaysInitializedAsPendingWhenThisIsNotSet() {
        var firma = FirmaUsuario.builder()
                .id(UUID.randomUUID())
                .x(10)
                .y(10)
                .width(10)
                .height(10)
                .page(10)
                .firmante("jmarin")
                .build();
        assertSame(EstadoDeLaFirma.PENDIENTE, firma.getEstado());
    }

    @Test
    void shouldThrowExceptionWhenNegativeValuesAreProvided() {
        assertThrows(IllegalArgumentException.class, () -> FirmaUsuario.builder()
                .id(UUID.randomUUID())
                .x(-1)
                .y(10)
                .width(10)
                .height(10)
                .page(10)
                .build());

        assertThrows(IllegalArgumentException.class, () -> FirmaUsuario.builder()
                .id(UUID.randomUUID())
                .x(10)
                .y(-1)
                .width(10)
                .height(10)
                .page(10)
                .build());

        assertThrows(IllegalArgumentException.class, () -> FirmaUsuario.builder()
                .id(UUID.randomUUID())
                .x(10)
                .y(10)
                .width(-1)
                .height(10)
                .page(10)
                .build());

        assertThrows(IllegalArgumentException.class, () -> FirmaUsuario.builder()
                .id(UUID.randomUUID())
                .x(10)
                .y(10)
                .width(10)
                .height(-1)
                .page(10)
                .build());

        assertThrows(IllegalArgumentException.class, () -> FirmaUsuario.builder()
                .id(UUID.randomUUID())
                .x(10)
                .y(10)
                .width(10)
                .height(10)
                .page(-1)
                .build());
    }
}