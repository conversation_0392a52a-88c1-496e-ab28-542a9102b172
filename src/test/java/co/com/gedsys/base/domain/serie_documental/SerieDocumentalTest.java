package co.com.gedsys.base.domain.serie_documental;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static co.com.gedsys.base.domain.serie_documental.EstadoSerie.ACTIVA;
import static co.com.gedsys.base.domain.serie_documental.EstadoSerie.INACTIVA;
import static org.junit.jupiter.api.Assertions.*;

class SerieDocumentalTest {

    @Test
    @DisplayName("Debe activar una serie documental inactiva")
    void debeActivarSerieInactiva() {
        // Arrange
        SerieDocumental serie = new SerieDocumental("100", "Serie de prueba");
        serie.setEstado(INACTIVA);
        
        // Act
        serie.activar();
        
        // Assert
        assertEquals(ACTIVA, serie.getEstado());
    }
    
    @Test
    @DisplayName("Debe lanzar excepción al activar una serie ya activa")
    void debeLanzarExcepcionAlActivarSerieActiva() {
        // Arrange
        SerieDocumental serie = new SerieDocumental("100", "Serie de prueba");
        // Por defecto el estado es ACTIVA según el constructor
        
        // Act & Assert
        IllegalStateException exception = assertThrows(
            IllegalStateException.class,
            serie::activar
        );
        
        assertEquals("La serie documental ya se encuentra activa.", exception.getMessage());
    }
    
    @Test
    @DisplayName("Debe inactivar una serie documental activa")
    void debeInactivarSerieActiva() {
        // Arrange
        SerieDocumental serie = new SerieDocumental("100", "Serie de prueba");
        // Por defecto el estado es ACTIVA según el constructor
        
        // Act
        serie.inactivar();
        
        // Assert
        assertEquals(INACTIVA, serie.getEstado());
    }
    
    @Test
    @DisplayName("Debe lanzar excepción al inactivar una serie ya inactiva")
    void debeLanzarExcepcionAlInactivarSerieInactiva() {
        // Arrange
        SerieDocumental serie = new SerieDocumental("100", "Serie de prueba");
        serie.setEstado(INACTIVA);
        
        // Act & Assert
        IllegalStateException exception = assertThrows(
            IllegalStateException.class,
            serie::inactivar
        );
        
        assertEquals("La serie documental ya se encuentra inactiva.", exception.getMessage());
    }
}