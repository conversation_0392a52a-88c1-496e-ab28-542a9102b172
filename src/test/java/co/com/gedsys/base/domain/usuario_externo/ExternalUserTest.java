package co.com.gedsys.base.domain.usuario_externo;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Pruebas para la entidad ExternalUser")
class ExternalUserTest {

    private ExternalUser externalUser;

    @BeforeEach
    void setUp() {
        externalUser = new ExternalUser("<PERSON>", "CC", "12345678");
        externalUser.setProperties(new ArrayList<>());
    }

    @Nested
    @DisplayName("al agregar propiedades")
    class AgregarPropiedades {

        @Test
        @DisplayName("debe permitir agregar propiedades únicas")
        void deberiaPermitirAgregarPropiedadesUnicas() {
            // Arrange
            var property1 = new ExternalUserProperty(ExternalUserPropertyType.EMAIL, "Email principal", "<EMAIL>");
            var property2 = new ExternalUserProperty(ExternalUserPropertyType.PHONE, "Teléfono", "6012345678");
            var properties = List.of(property1, property2);

            // Act & Assert
            assertDoesNotThrow(() -> externalUser.addProperties(properties));
            assertEquals(2, externalUser.getProperties().size());
        }

        @Test
        @DisplayName("debe lanzar excepción al agregar propiedad con valor duplicado existente")
        void deberiaLanzarExcepcionAlAgregarPropiedadConValorDuplicadoExistente() {
            // Arrange
            var existingProperty = new ExternalUserProperty(ExternalUserPropertyType.EMAIL, "Email principal", "<EMAIL>");
            externalUser.addProperties(List.of(existingProperty));

            var duplicateProperty = new ExternalUserProperty(ExternalUserPropertyType.EMAIL, "Email secundario", "<EMAIL>");

            // Act & Assert
            var exception = assertThrows(DuplicatePropertyException.class, 
                    () -> externalUser.addProperties(List.of(duplicateProperty)));
            assertTrue(exception.getMessage().contains("<EMAIL>"));
            assertTrue(exception.getMessage().contains("EMAIL"));
        }

        @Test
        @DisplayName("debe lanzar excepción al agregar múltiples propiedades con valores duplicados entre sí")
        void deberiaLanzarExcepcionAlAgregarMultiplesPropiedadesConValoresDuplicadosEntreSi() {
            // Arrange
            var property1 = new ExternalUserProperty(ExternalUserPropertyType.EMAIL, "Email 1", "<EMAIL>");
            var property2 = new ExternalUserProperty(ExternalUserPropertyType.EMAIL, "Email 2", "<EMAIL>");
            var properties = List.of(property1, property2);

            // Act & Assert
            var exception = assertThrows(DuplicatePropertyException.class, 
                    () -> externalUser.addProperties(properties));
            assertTrue(exception.getMessage().contains("duplicados"));
        }

        @Test
        @DisplayName("debe permitir agregar propiedades cuando no hay propiedades existentes")
        void deberiaPermitirAgregarPropiedadesCuandoNoHayPropiedadesExistentes() {
            // Arrange
            var usuarioSinPropiedades = new ExternalUser("María García", "CC", "87654321");
            usuarioSinPropiedades.setProperties(null);
            
            var property = new ExternalUserProperty(ExternalUserPropertyType.EMAIL, "Email", "<EMAIL>");

            // Act & Assert
            assertDoesNotThrow(() -> usuarioSinPropiedades.addProperties(List.of(property)));
        }

        @Test
        @DisplayName("debe permitir agregar propiedades cuando la lista existente está vacía")
        void deberiaPermitirAgregarPropiedadesCuandoLaListaExistenteEstaVacia() {
            // Arrange
            var usuarioConListaVacia = new ExternalUser("Carlos López", "CC", "11223344");
            usuarioConListaVacia.setProperties(new ArrayList<>());
            
            var property = new ExternalUserProperty(ExternalUserPropertyType.PHONE, "Teléfono", "6011234567");

            // Act & Assert
            assertDoesNotThrow(() -> usuarioConListaVacia.addProperties(List.of(property)));
            assertEquals(1, usuarioConListaVacia.getProperties().size());
        }

        @Test
        @DisplayName("debe establecer el owner correctamente en las propiedades agregadas")
        void deberiaEstablecerElOwnerCorrectamenteEnLasPropiedadesAgregadas() {
            // Arrange
            var property = new ExternalUserProperty(ExternalUserPropertyType.ADDRESS, "Dirección", "Calle 123");

            // Act
            externalUser.addProperties(List.of(property));

            // Assert
            assertEquals(externalUser, property.getOwner());
        }
    }

    @Nested
    @DisplayName("al validar tipo de identificación")
    class ValidarTipoIdentificacion {

        @Test
        @DisplayName("debe crear usuario tipo NA con número null exitosamente")
        void deberiaCrearUsuarioTipoNAConNumeroNullExitosamente() {
            // Act & Assert
            assertDoesNotThrow(() -> new ExternalUser("Usuario Sin ID", "NA", null));
        }

        @Test
        @DisplayName("debe lanzar excepción al crear usuario tipo NA con número no null")
        void deberiaLanzarExcepcionAlCrearUsuarioTipoNAConNumeroNoNull() {
            // Act & Assert
            var exception = assertThrows(IllegalArgumentException.class,
                () -> new ExternalUser("Usuario Inválido", "NA", "12345"));
            assertTrue(exception.getMessage().contains("Usuarios con tipo NA no pueden tener número de identificación"));
        }

        @Test
        @DisplayName("debe crear usuario tipo CC con número válido exitosamente")
        void deberiaCrearUsuarioTipoCCConNumeroValidoExitosamente() {
            // Act & Assert
            assertDoesNotThrow(() -> new ExternalUser("Usuario CC", "CC", "12345678"));
        }

        @Test
        @DisplayName("debe lanzar excepción al crear usuario tipo CC con número null")
        void deberiaLanzarExcepcionAlCrearUsuarioTipoCCConNumeroNull() {
            // Act & Assert
            var exception = assertThrows(IllegalArgumentException.class,
                () -> new ExternalUser("Usuario Inválido", "CC", null));
            assertTrue(exception.getMessage().contains("Usuarios con tipo CC deben tener número de identificación"));
        }

        @Test
        @DisplayName("debe lanzar excepción al crear usuario tipo CC con número blank")
        void deberiaLanzarExcepcionAlCrearUsuarioTipoCCConNumeroBlank() {
            // Act & Assert
            var exception = assertThrows(IllegalArgumentException.class,
                () -> new ExternalUser("Usuario Inválido", "CC", "   "));
            assertTrue(exception.getMessage().contains("Tipos distintos a NA requieren número de identificación"));
        }
    }

    @Nested
    @DisplayName("al cambiar tipo de identificación")
    class CambiarTipoIdentificacion {

        @Test
        @DisplayName("debe permitir cambiar de CC a NA forzando número a null")
        void deberiaPermitirCambiarDeCCANAForzandoNumeroANull() {
            // Arrange
            var usuario = new ExternalUser("Usuario Test", "CC", "12345678");

            // Act
            assertDoesNotThrow(() -> usuario.setIdentificationType(ExternalUserIdentificationType.NA));

            // Assert
            assertEquals(ExternalUserIdentificationType.NA, usuario.getIdentificationType());
            assertNull(usuario.getIdentificationNumber());
        }

        @Test
        @DisplayName("debe lanzar excepción al cambiar de NA a CC sin proporcionar número")
        void deberiaLanzarExcepcionAlCambiarDeNAACCSinProporcionarNumero() {
            // Arrange
            var usuario = new ExternalUser("Usuario NA", "NA", null);

            // Act & Assert
            var exception = assertThrows(IllegalArgumentException.class,
                () -> usuario.setIdentificationType(ExternalUserIdentificationType.CC));
            assertTrue(exception.getMessage().contains("Usuarios con tipo CC deben tener número de identificación"));
        }

        @Test
        @DisplayName("debe permitir cambiar número de identificación para tipo CC")
        void deberiaPermitirCambiarNumeroDeIdentificacionParaTipoCC() {
            // Arrange
            var usuario = new ExternalUser("Usuario CC", "CC", "12345678");

            // Act & Assert
            assertDoesNotThrow(() -> usuario.setIdentificationNumber("87654321"));
            assertEquals("87654321", usuario.getIdentificationNumber());
        }

        @Test
        @DisplayName("debe lanzar excepción al intentar asignar número a usuario tipo NA")
        void deberiaLanzarExcepcionAlIntentarAsignarNumeroAUsuarioTipoNA() {
            // Arrange
            var usuario = new ExternalUser("Usuario NA", "NA", null);

            // Act & Assert
            var exception = assertThrows(IllegalArgumentException.class,
                () -> usuario.setIdentificationNumber("12345"));
            assertTrue(exception.getMessage().contains("Usuarios con tipo NA no pueden tener número de identificación"));
        }
    }
}
