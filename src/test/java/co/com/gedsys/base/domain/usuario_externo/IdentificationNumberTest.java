package co.com.gedsys.base.domain.usuario_externo;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Pruebas para IdentificationNumber")
class IdentificationNumberTest {

    @Nested
    @DisplayName("con tipo NA")
    class ConTipoNA {

        @Test
        @DisplayName("debe aceptar valor null")
        void deberiaAceptarValorNull() {
            // Act & Assert
            assertDoesNotThrow(() -> new IdentificationNumber(null, ExternalUserIdentificationType.NA));
        }

        @Test
        @DisplayName("debe forzar valor a null cuando se proporciona un valor")
        void deberiaForzarValorANullCuandoSeProporcionaUnValor() {
            // Act & Assert
            var exception = assertThrows(IllegalArgumentException.class, 
                () -> new IdentificationNumber("12345", ExternalUserIdentificationType.NA));
            assertTrue(exception.getMessage().contains("Tipo NA requiere número de identificación NULL"));
        }

        @Test
        @DisplayName("debe retornar null como valor")
        void deberiaRetornarNullComoValor() {
            // Arrange
            var identificationNumber = new IdentificationNumber(null, ExternalUserIdentificationType.NA);
            
            // Act & Assert
            assertNull(identificationNumber.getValue());
        }
    }

    @Nested
    @DisplayName("con tipo CC")
    class ConTipoCC {

        @Test
        @DisplayName("debe aceptar valor válido")
        void deberiaAceptarValorValido() {
            // Act & Assert
            assertDoesNotThrow(() -> new IdentificationNumber("12345678", ExternalUserIdentificationType.CC));
        }

        @Test
        @DisplayName("debe lanzar excepción con valor null")
        void deberiaLanzarExcepcionConValorNull() {
            // Act & Assert
            var exception = assertThrows(IllegalArgumentException.class, 
                () -> new IdentificationNumber(null, ExternalUserIdentificationType.CC));
            assertTrue(exception.getMessage().contains("Tipos distintos a NA requieren número de identificación"));
        }

        @Test
        @DisplayName("debe lanzar excepción con valor blank")
        void deberiaLanzarExcepcionConValorBlank() {
            // Act & Assert
            var exception = assertThrows(IllegalArgumentException.class, 
                () -> new IdentificationNumber("   ", ExternalUserIdentificationType.CC));
            assertTrue(exception.getMessage().contains("Tipos distintos a NA requieren número de identificación"));
        }

        @Test
        @DisplayName("debe lanzar excepción con valor vacío")
        void deberiaLanzarExcepcionConValorVacio() {
            // Act & Assert
            var exception = assertThrows(IllegalArgumentException.class, 
                () -> new IdentificationNumber("", ExternalUserIdentificationType.CC));
            assertTrue(exception.getMessage().contains("Tipos distintos a NA requieren número de identificación"));
        }

        @Test
        @DisplayName("debe retornar el valor proporcionado")
        void deberiaRetornarElValorProporcionado() {
            // Arrange
            var identificationNumber = new IdentificationNumber("12345678", ExternalUserIdentificationType.CC);
            
            // Act & Assert
            assertEquals("12345678", identificationNumber.getValue());
        }
    }

    @Nested
    @DisplayName("con otros tipos de identificación")
    class ConOtrosTiposDeIdentificacion {

        @Test
        @DisplayName("debe funcionar correctamente con tipo CE")
        void deberiaFuncionarCorrectamenteConTipoCE() {
            // Act & Assert
            assertDoesNotThrow(() -> new IdentificationNumber("CE123456", ExternalUserIdentificationType.CE));
            
            var exception = assertThrows(IllegalArgumentException.class, 
                () -> new IdentificationNumber(null, ExternalUserIdentificationType.CE));
            assertTrue(exception.getMessage().contains("Tipos distintos a NA requieren número de identificación"));
        }

        @Test
        @DisplayName("debe funcionar correctamente con tipo NIT")
        void deberiaFuncionarCorrectamenteConTipoNIT() {
            // Act & Assert
            assertDoesNotThrow(() -> new IdentificationNumber("900123456-1", ExternalUserIdentificationType.NIT));
            
            var exception = assertThrows(IllegalArgumentException.class, 
                () -> new IdentificationNumber(null, ExternalUserIdentificationType.NIT));
            assertTrue(exception.getMessage().contains("Tipos distintos a NA requieren número de identificación"));
        }

        @Test
        @DisplayName("debe funcionar correctamente con tipo PASAPORTE")
        void deberiaFuncionarCorrectamenteConTipoPASAPORTE() {
            // Act & Assert
            assertDoesNotThrow(() -> new IdentificationNumber("AB123456", ExternalUserIdentificationType.PASAPORTE));
            
            var exception = assertThrows(IllegalArgumentException.class, 
                () -> new IdentificationNumber(null, ExternalUserIdentificationType.PASAPORTE));
            assertTrue(exception.getMessage().contains("Tipos distintos a NA requieren número de identificación"));
        }

        @Test
        @DisplayName("debe funcionar correctamente con tipo TI")
        void deberiaFuncionarCorrectamenteConTipoTI() {
            // Act & Assert
            assertDoesNotThrow(() -> new IdentificationNumber("1234567890", ExternalUserIdentificationType.TI));
            
            var exception = assertThrows(IllegalArgumentException.class, 
                () -> new IdentificationNumber(null, ExternalUserIdentificationType.TI));
            assertTrue(exception.getMessage().contains("Tipos distintos a NA requieren número de identificación"));
        }
    }
}
