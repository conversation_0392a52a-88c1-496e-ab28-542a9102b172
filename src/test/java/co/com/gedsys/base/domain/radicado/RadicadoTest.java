package co.com.gedsys.base.domain.radicado;

import co.com.gedsys.base.domain.consecutivo.Consecutivo;
import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;
import co.com.gedsys.base.domain.documento.Documento;
import co.com.gedsys.base.domain.usuario_externo.ExternalUser;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserStatus;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class RadicadoTest {

    @Test
    void defaultConstructor_initialState() {
        Consecutivo consec = new Consecutivo("PRE", "SUF", 10, TipoConsecutivo.RECEPCION);
        Radicado radicado = new Radicado(consec);
        assertThat(radicado.getEstado()).isEqualTo(EstadoRadicado.INCOMPLETO);
        assertThat(radicado.getNumeroRadicado()).isNull();
        assertThat(radicado.getFechaExpedicion()).isNull();
        assertThat(radicado.getPrefijo()).isEqualTo("PRE");
        assertThat(radicado.getSufijo()).isEqualTo("SUF");
        assertThat(radicado.getTipo()).isEqualTo(TipoConsecutivo.RECEPCION);
        assertThat(radicado.getDestinatario()).isNull();
    }

    @Test
    void expedir_recepcion_withAuthor_assignsEmisor_andUpdatesFields() {
        Consecutivo consec = new Consecutivo("PRE", "SUF", 5, TipoConsecutivo.RECEPCION);
        Radicado radicado = new Radicado(consec);
        Documento doc = new Documento(
            "titulo", "fileId", null, null, "Alice",
            Collections.emptySet(), Collections.emptyList(), Collections.emptyList(), Collections.emptyList()
        );
        LocalDateTime before = LocalDateTime.now();
        radicado.expedir(doc);
        assertThat(radicado.getEstado()).isEqualTo(EstadoRadicado.ACTIVO);
        assertThat(radicado.getNumeroRadicado()).isEqualTo(6);
        assertThat(radicado.getEmisor()).isEqualTo("alice");
        assertThat(radicado.getFechaExpedicion()).isNotNull()
                                         .isAfterOrEqualTo(before);
    }

    @Test
    void expedir_withoutAuthor_leavesEmisorNull() {
        Consecutivo consec = new Consecutivo("PRE", "SUF", 1, TipoConsecutivo.PRODUCCION);
        Radicado radicado = new Radicado(consec);
        Documento doc = new Documento(
            "titulo", "fileId", null, null, null,
            Collections.emptySet(), Collections.emptyList(), Collections.emptyList(), Collections.emptyList()
        );
        radicado.expedir(doc);
        assertThat(radicado.getEmisor()).isNull();
    }

    @Test
    void expedir_nullDocumento_doesNotThrow_forNonEnvio() {
        Consecutivo consec = new Consecutivo("PRE", "SUF", 2, TipoConsecutivo.RECEPCION);
        Radicado radicado = new Radicado(consec);
        radicado.expedir(null);
        assertThat(radicado.getEstado()).isEqualTo(EstadoRadicado.ACTIVO);
        assertThat(radicado.getNumeroRadicado()).isEqualTo(3);
    }

    @Test
    void setDestinatario_nonEnvio_throwsIllegalState() {
        Radicado radicado = new Radicado(new Consecutivo("P", "S", 0, TipoConsecutivo.RECEPCION));
        ExternalUser user = new ExternalUser("Bob", "CC", "123");
        assertThatThrownBy(() -> radicado.setDestinatario(user))
            .isInstanceOf(IllegalStateException.class)
            .hasMessage("Solo los radicados de tipo ENVÍO pueden tener destinatarios");
    }

    @Test
    void setDestinatario_envioNull_throwsIllegalArgument() {
        Radicado radicado = new Radicado(new Consecutivo("P", "S", 0, TipoConsecutivo.ENVIO));
        assertThatThrownBy(() -> radicado.setDestinatario(null))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("El destinatario no puede ser nulo para radicados de tipo ENVÍO");
    }

    @Test
    void setDestinatario_envioInactiveUser_throwsIllegalArgument() {
        Radicado radicado = new Radicado(new Consecutivo("P", "S", 0, TipoConsecutivo.ENVIO));
        ExternalUser user = new ExternalUser("Bob", "CC", "123")
                                  .setStatus(ExternalUserStatus.INACTIVO);
        assertThatThrownBy(() -> radicado.setDestinatario(user))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("Solo los usuarios externos con estado ACTIVO pueden ser destinatarios");
    }

    @Test
    void setDestinatario_envioActiveUser_setsDestinatario() {
        ExternalUser user = new ExternalUser("Bob", "CC", "123");
        Radicado radicado = new Radicado(new Consecutivo("P", "S", 0, TipoConsecutivo.ENVIO));
        radicado.setDestinatario(user);
        assertThat(radicado.getDestinatario()).isEqualTo(user);
    }

    @Test
    void validarDestinatarioParaEnvio_nonEnvio_noException() {
        Radicado radicado = new Radicado(new Consecutivo("P", "S", 0, TipoConsecutivo.PRODUCCION));
        // should not throw
        radicado.validarDestinatarioParaEnvio();
    }

    @Test
    void validarDestinatarioParaEnvio_envioWithoutDest_throwsIllegalState() {
        Radicado radicado = new Radicado(new Consecutivo("P", "S", 0, TipoConsecutivo.ENVIO));
        assertThatThrownBy(radicado::validarDestinatarioParaEnvio)
            .isInstanceOf(IllegalStateException.class)
            .hasMessage("Un radicado de tipo ENVÍO debe tener un destinatario");
    }

    @Test
    void validarDestinatarioParaEnvio_envioWithDest_noException() {
        Radicado radicado = new Radicado(new Consecutivo("P", "S", 0, TipoConsecutivo.ENVIO));
        ExternalUser user = new ExternalUser("Bob", "CC", "123");
        radicado.setDestinatario(user);
        // should not throw
        radicado.validarDestinatarioParaEnvio();
    }

    @Test
    void anular_setsStateToAnulado() {
        Radicado radicado = new Radicado(new Consecutivo("P", "S", 0, TipoConsecutivo.PRODUCCION));
        radicado.anular();
        assertThat(radicado.getEstado()).isEqualTo(EstadoRadicado.ANULADO);
    }

    @Test
    void expedir_envioWithDest_setsDestAndFields() {
        Consecutivo consec = new Consecutivo("PRE", "SUF", 3, TipoConsecutivo.ENVIO);
        Radicado radicado = new Radicado(consec);
        ExternalUser user = new ExternalUser("Bob", "CC", "123");
        radicado.setDestinatario(user);
        Documento doc = new Documento(
            "titulo", "fileId", null, null, "Carol",
            Collections.emptySet(), Collections.emptyList(), Collections.emptyList(), Collections.emptyList()
        );
        radicado.expedir(doc);
        assertThat(radicado.getEstado()).isEqualTo(EstadoRadicado.ACTIVO);
        assertThat(radicado.getNumeroRadicado()).isEqualTo(4);
        assertThat(radicado.getDestinatario()).isEqualTo(user);
        assertThat(radicado.getEmisor()).isEqualTo("carol");
        assertThat(radicado.getFechaExpedicion()).isNotNull();
    }
}