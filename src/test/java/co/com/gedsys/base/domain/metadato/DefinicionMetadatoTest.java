package co.com.gedsys.base.domain.metadato;

import co.com.gedsys.base.domain.metadato.enums.FormatoMetadatoEnum;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("DefinicionMetadato")
class DefinicionMetadatoTest {

    @Nested
    @DisplayName("Al crear una definición de metadato")
    class CrearDefinicionMetadato {

        @Test
        @DisplayName("debería crear una definición de metadato válida con el constructor")
        void deberiaCrearDefinicionMetadatoValidaConConstructor() {
            // Arrange
            String nombre = "nombreMetadato";
            TipoMetadatoEnum tipo = TipoMetadatoEnum.CONTENIDO;
            FormatoMetadatoEnum formato = FormatoMetadatoEnum.ALFANUMERICO;

            // Act
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(nombre, tipo, formato);

            // Assert
            assertNotNull(definicionMetadato);
            assertEquals("nombreMetadato", definicionMetadato.getNombre());
            assertEquals(tipo, definicionMetadato.getTipo());
            assertEquals(formato, definicionMetadato.getFormato());
            assertNull(definicionMetadato.getId());
            assertEquals("", definicionMetadato.getDescripcion());
            assertEquals("nombremetadato", definicionMetadato.getPatron());
            assertEquals("requerido:true", definicionMetadato.getRules());
        }

        @Test
        @DisplayName("debería crear una definición de metadato válida con el método create")
        void deberiaCrearDefinicionMetadatoValidaConCreate() {
            // Arrange
            String nombre = "nombreMetadato";
            TipoMetadatoEnum tipo = TipoMetadatoEnum.GESTION;
            FormatoMetadatoEnum formato = FormatoMetadatoEnum.EMAIL;

            // Act
            DefinicionMetadato definicionMetadato = DefinicionMetadato.create(nombre, tipo, formato);

            // Assert
            assertNotNull(definicionMetadato);
            assertEquals("nombreMetadato", definicionMetadato.getNombre());
            assertEquals(tipo, definicionMetadato.getTipo());
            assertEquals(formato, definicionMetadato.getFormato());
            assertNull(definicionMetadato.getId());
            assertEquals("", definicionMetadato.getDescripcion());
            assertEquals("nombremetadato", definicionMetadato.getPatron());
            assertEquals("requerido:true", definicionMetadato.getRules());
        }
    }

    @Nested
    @DisplayName("Al modificar una definición de metadato")
    class ModificarDefinicionMetadato {

        @Test
        @DisplayName("debería actualizar el nombre correctamente")
        void deberiaActualizarNombreCorrectamente() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreOriginal", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.ALFANUMERICO
            );

            // Act
            definicionMetadato.setNombre("nombreNuevo");

            // Assert
            assertEquals("nombreNuevo", definicionMetadato.getNombre());
            assertEquals("nombrenuevo", definicionMetadato.getPatron());
        }

        @Test
        @DisplayName("debería mantener el nombre original si se intenta actualizar con null")
        void deberiaMantenerNombreOriginalSiSeActualizaConNull() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreOriginal", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.ALFANUMERICO
            );

            // Act
            definicionMetadato.setNombre(null);

            // Assert
            assertEquals("nombreOriginal", definicionMetadato.getNombre());
            assertEquals("nombreoriginal", definicionMetadato.getPatron());
        }

        @Test
        @DisplayName("debería actualizar el tipo correctamente")
        void deberiaActualizarTipoCorrectamente() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreMetadato", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.ALFANUMERICO
            );

            // Act
            definicionMetadato.setTipo(TipoMetadatoEnum.GESTION);

            // Assert
            assertEquals(TipoMetadatoEnum.GESTION, definicionMetadato.getTipo());
        }

        @Test
        @DisplayName("debería actualizar el formato correctamente")
        void deberiaActualizarFormatoCorrectamente() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreMetadato", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.ALFANUMERICO
            );

            // Act
            definicionMetadato.setFormato(FormatoMetadatoEnum.EMAIL);

            // Assert
            assertEquals(FormatoMetadatoEnum.EMAIL, definicionMetadato.getFormato());
        }

        @Test
        @DisplayName("debería actualizar las reglas correctamente")
        void deberiaActualizarReglasCorrectamente() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreMetadato", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.ALFANUMERICO
            );

            // Act
            definicionMetadato.setRules("requerido:false,longitud:10");

            // Assert
            String rules = definicionMetadato.getRules();
            assertTrue(rules.contains("requerido:false"));
            assertTrue(rules.contains("longitud:10"));
        }

        @Test
        @DisplayName("debería mantener las reglas originales si se intenta actualizar con null")
        void deberiaMantenerReglasOriginalesSiSeActualizaConNull() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreMetadato", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.ALFANUMERICO
            );
            String reglasOriginales = definicionMetadato.getRules();

            // Act
            definicionMetadato.setRules(null);

            // Assert
            assertEquals(reglasOriginales, definicionMetadato.getRules());
        }

        @Test
        @DisplayName("debería actualizar la descripción correctamente")
        void deberiaActualizarDescripcionCorrectamente() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreMetadato", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.ALFANUMERICO
            );

            // Act
            definicionMetadato.setDescripcion("Nueva descripción");

            // Assert
            assertEquals("Nueva descripción", definicionMetadato.getDescripcion());
        }

        @Test
        @DisplayName("debería mantener la descripción original si se intenta actualizar con null")
        void deberiaMantenerDescripcionOriginalSiSeActualizaConNull() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreMetadato", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.ALFANUMERICO
            );
            definicionMetadato.setDescripcion("Descripción original");

            // Act
            definicionMetadato.setDescripcion(null);

            // Assert
            assertEquals("Descripción original", definicionMetadato.getDescripcion());
        }

        @Test
        @DisplayName("debería actualizar el ID correctamente")
        void deberiaActualizarIdCorrectamente() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreMetadato", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.ALFANUMERICO
            );
            UUID nuevoId = UUID.randomUUID();

            // Act
            definicionMetadato.setId(nuevoId);

            // Assert
            assertEquals(nuevoId, definicionMetadato.getId());
        }
    }

    @Nested
    @DisplayName("Al validar la compatibilidad de reglas y formato")
    class ValidarCompatibilidadReglasFormato {

        @Test
        @DisplayName("debería validar la compatibilidad al establecer reglas")
        void deberiaValidarCompatibilidadAlEstablecerReglas() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreMetadato", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.ALFANUMERICO
            );

            // Act & Assert
            assertDoesNotThrow(() -> definicionMetadato.setRules("requerido:true,longitud:10"));
        }

        @Test
        @DisplayName("debería lanzar excepción si las reglas no son compatibles con el formato")
        void deberiaLanzarExcepcionSiReglasNoSonCompatiblesConFormato() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreMetadato", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.ALFANUMERICO
            );

            // Act & Assert
            assertThrows(IllegalArgumentException.class, 
                    () -> definicionMetadato.setRules("requerido:true,pasado:true"));
        }
    }

    @Nested
    @DisplayName("Al generar un metadato")
    class GenerarMetadato {

        @Test
        @DisplayName("debería generar un metadato válido")
        void deberiaGenerarMetadatoValido() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreMetadato", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.ALFANUMERICO
            );

            // Act
            Metadato metadato = definicionMetadato.generarMetadato("valor");

            // Assert
            assertNotNull(metadato);
            assertEquals("nombremetadato", metadato.patron());
            assertEquals("nombreMetadato", metadato.nombre());
            assertEquals(TipoMetadatoEnum.CONTENIDO, metadato.tipo());
            assertEquals(FormatoMetadatoEnum.ALFANUMERICO, metadato.formato());
            assertEquals("valor", metadato.valor());
        }

        @Test
        @DisplayName("debería validar las reglas al generar un metadato")
        void deberiaValidarReglasAlGenerarMetadato() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreMetadato", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.ALFANUMERICO
            );
            definicionMetadato.setRules("requerido:true");

            // Act & Assert
            assertThrows(RuntimeException.class, 
                    () -> definicionMetadato.generarMetadato(null));
        }

        @Test
        @DisplayName("debería validar el formato al generar un metadato")
        void deberiaValidarFormatoAlGenerarMetadato() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreMetadato", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.EMAIL
            );

            // Act & Assert
            assertThrows(RuntimeException.class, 
                    () -> definicionMetadato.generarMetadato("valor-invalido"));
        }

        @Test
        @DisplayName("debería generar un metadato con formato de email válido")
        void deberiaGenerarMetadatoConFormatoEmailValido() {
            // Arrange
            DefinicionMetadato definicionMetadato = new DefinicionMetadato(
                    "nombreMetadato", 
                    TipoMetadatoEnum.CONTENIDO, 
                    FormatoMetadatoEnum.EMAIL
            );

            // Act
            Metadato metadato = definicionMetadato.generarMetadato("<EMAIL>");

            // Assert
            assertNotNull(metadato);
            assertEquals("<EMAIL>", metadato.valor());
        }
    }
}
