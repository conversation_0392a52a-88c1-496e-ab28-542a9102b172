package co.com.gedsys.base.domain.plantillas;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

@DisplayName("Pruebas para la entidad Plantilla")
class PlantillaTest {

    @Test
    @DisplayName("Nueva plantilla de tipo PRODUCCION debería tener producidoPorGedsys en false por defecto")
    void crearNuevaPlantillaProduccion_DeberiaTenerProducidoPorGedsysFalsePorDefecto() {
        // Arrange & Act
        Plantilla plantilla = new Plantilla("Nueva Plantilla", TipoPlantilla.PRODUCCION);
        
        // Assert
        assertFalse(plantilla.isProducidoPorGedsys());
    }
    
    @Test
    @DisplayName("Nueva plantilla de tipo no PRODUCCION debería tener producidoPorGedsys en false")
    void crearNuevaPlantillaNoProduccion_DeberiaTenerProducidoPorGedsysFalse() {
        // Arrange & Act - Usamos ENVIO como tipo que no es PRODUCCION
        Plantilla plantilla = new Plantilla("Nueva Plantilla", TipoPlantilla.ENVIO);
        
        // Assert
        assertFalse(plantilla.isProducidoPorGedsys());
    }
    
    @Test
    @DisplayName("Crear plantilla con builder debería respetar el valor de producidoPorGedsys")
    void crearPlantillaConBuilder_DeberiaRespetarValorDeProducidoPorGedsys() {
        // Arrange & Act
        Plantilla plantilla = Plantilla.builder()
                .titulo("Plantilla de prueba")
                .tipoPlantilla(TipoPlantilla.PRODUCCION)
                .producidoPorGedsys(false)
                .build();
        
        // Assert
        assertFalse(plantilla.isProducidoPorGedsys());
    }
    
    @Test
    @DisplayName("setProducidoPorGedsys debería permitir establecer true solo en plantillas de tipo PRODUCCION")
    void setProducidoPorGedsys_DeberiaPermitirTrueSoloEnProduccion() {
        // Arrange
        Plantilla plantillaProduccion = new Plantilla("Plantilla Producción", TipoPlantilla.PRODUCCION);
        Plantilla plantillaOtroTipo = new Plantilla("Otra Plantilla", TipoPlantilla.ENVIO);
        
        // Act & Assert - Debería permitir establecer a true en plantilla de producción
        plantillaProduccion.setProducidoPorGedsys(true);
        assertTrue(plantillaProduccion.isProducidoPorGedsys());
        
        // Act & Assert - No debería permitir establecer a true en plantilla que no es de producción
        assertThrows(IllegalStateException.class, () -> {
            plantillaOtroTipo.setProducidoPorGedsys(true);
        });
        
        // Act & Assert - Debería permitir establecer a false en cualquier caso
        plantillaProduccion.setProducidoPorGedsys(false);
        assertFalse(plantillaProduccion.isProducidoPorGedsys());
        
        plantillaOtroTipo.setProducidoPorGedsys(false);
        assertFalse(plantillaOtroTipo.isProducidoPorGedsys());
    }
    
    @Test
    @DisplayName("Cambiar el tipo de plantilla a no PRODUCCION debería forzar producidoPorGedsys a false")
    void cambiarTipoANoProduccion_DeberiaForzarProducidoPorGedsysFalse() {
        // Arrange
        Plantilla plantilla = new Plantilla("Plantilla de prueba", TipoPlantilla.PRODUCCION);
        plantilla.setProducidoPorGedsys(true);
        
        // Act - Cambiar a un tipo que no es PRODUCCION
        plantilla.setTipoPlantilla(TipoPlantilla.RECEPCION);
        
        // Assert - Debería forzar producidoPorGedsys a false
        assertFalse(plantilla.isProducidoPorGedsys());
    }
}
