package co.com.gedsys.base.domain.control_acceso;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

class ClassificationCodeTest {


    @ParameterizedTest
    @ValueSource(strings = {
            "01",
            "10",
            "01.02.03",
            "10.034.12",
            "09.01.343.32",
            "01.00.00"
    })
    void testValidCodes(String code) {
        assertDoesNotThrow(() -> new ClassificationCode(code));
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "",
            "1",
            "01..23",
            "30.2",
            ".01.02",
            "01.02.",
            "00.00.00"  // Ensure this test case is included as it's invalid due to the first segment being zero
    })
    void testInvalidCodes(String code) {
        assertThrows(IllegalArgumentException.class, () -> new ClassificationCode(code));
        assertThrows(IllegalArgumentException.class, () -> new ClassificationCode(null));
    }

    @Test
    void testReturnValue() {
        String validCode = "01.02.03";
        assertEquals(validCode, new ClassificationCode(validCode).getValue());
    }
}