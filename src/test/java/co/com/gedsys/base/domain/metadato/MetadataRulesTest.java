package co.com.gedsys.base.domain.metadato;

import co.com.gedsys.base.domain.metadato.enums.MetadataRulesEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.EnumSet;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

class MetadataRulesTest {

    @Test
    void constructor_cuandoRecibeReglasValidas_creaMapaConReglas() {
        MetadataRules metadataRules = new MetadataRules("requerido:true,longitud:10");
        
        String result = metadataRules.getValue();
        
        assertTrue(result.contains("requerido:true"));
        assertTrue(result.contains("longitud:10"));
    }
    
    @ParameterizedTest
    @NullAndEmptySource
    @ValueSource(strings = {" ", "  "})
    void constructor_cuandoRecibeReglasVaciasONulas_asignaRequeridoPorDefecto(String reglas) {
        MetadataRules metadataRules = new MetadataRules(reglas);
        
        String result = metadataRules.getValue();
        
        assertEquals("requerido:true", result.toLowerCase());
    }
    
    @Test
    void constructor_cuandoRecibeReglasConEspacios_parseaCorrectamente() {
        MetadataRules metadataRules = new MetadataRules("requerido:true , longitud:10");
        
        String result = metadataRules.getValue();
        
        assertTrue(result.contains("requerido:true"));
        assertTrue(result.contains("longitud:10"));
    }
    
    @Test
    void constructor_cuandoRecibeReglaDuplicada_lanzaExcepcion() {
        Exception exception = assertThrows(IllegalArgumentException.class, () -> 
                new MetadataRules("requerido:true,requerido:false"));
        
        assertTrue(exception.getMessage().contains("Regla repetida"));
    }
    
    @Test
    void getValue_devuelveStringDeReglasEnFormatoCorrecto() {
        MetadataRules metadataRules = new MetadataRules("requerido:false,longitud:10");
        
        String result = metadataRules.getValue();
        
        assertTrue(result.matches(".*requerido:false.*"));
        assertTrue(result.matches(".*longitud:10.*"));
        assertTrue(result.equals(result.toLowerCase()));
    }
    
    @Test
    void toString_devuelveResultadoDeGetValue() {
        MetadataRules metadataRules = new MetadataRules("requerido:true");
        
        assertEquals(metadataRules.getValue(), metadataRules.toString());
    }
    
    @Test
    void equals_cuandoSonIguales_devuelveTrue() {
        MetadataRules rules1 = new MetadataRules("requerido:true,longitud:10");
        MetadataRules rules2 = new MetadataRules("requerido:true,longitud:10");
        
        assertTrue(rules1.equals(rules2));
        assertTrue(rules2.equals(rules1));
    }
    
    @Test
    void equals_cuandoSonDiferentes_devuelveFalse() {
        MetadataRules rules1 = new MetadataRules("requerido:true,longitud:10");
        MetadataRules rules2 = new MetadataRules("requerido:true,longitud:20");
        
        assertFalse(rules1.equals(rules2));
        assertFalse(rules2.equals(rules1));
    }
    
    @Test
    void equals_conNull_devuelveFalse() {
        MetadataRules rules = new MetadataRules("requerido:true");
        
        assertFalse(rules.equals(null));
    }
    
    @Test
    void equals_conObjetoDiferenteClase_devuelveFalse() {
        MetadataRules rules = new MetadataRules("requerido:true");
        
        assertFalse(rules.equals("string"));
    }
    
    @Test
    void equals_mismoObjeto_devuelveTrue() {
        MetadataRules rules = new MetadataRules("requerido:true");
        
        assertTrue(rules.equals(rules));
    }
    
    @Test
    void hashCode_objetosIguales_tienenMismoHashCode() {
        MetadataRules rules1 = new MetadataRules("requerido:true,longitud:10");
        MetadataRules rules2 = new MetadataRules("requerido:true,longitud:10");
        
        assertEquals(rules1.hashCode(), rules2.hashCode());
    }
    
    @Test
    void applyTo_valorNuloYRequerido_lanzaExcepcion() {
        MetadataRules rules = new MetadataRules("requerido:true");
        
        Exception exception = assertThrows(RuntimeException.class, () -> 
                rules.applyTo(null));
        
        assertEquals("El valor es requerido", exception.getMessage());
    }
    
    @Test
    void applyTo_valorNuloYNoRequerido_noLanzaExcepcion() {
        MetadataRules rules = new MetadataRules("requerido:false");
        
        assertDoesNotThrow(() -> rules.applyTo(null));
    }
    
    @Test
    void applyTo_excedeLongitud_lanzaExcepcion() {
        MetadataRules rules = new MetadataRules("longitud:5");
        
        Exception exception = assertThrows(IllegalArgumentException.class, () -> 
                rules.applyTo("123456"));
        
        assertEquals("Longitud excedida 5", exception.getMessage());
    }
    
    @Test
    void applyTo_longitudValida_noLanzaExcepcion() {
        MetadataRules rules = new MetadataRules("longitud:5");
        
        assertDoesNotThrow(() -> rules.applyTo("12345"));
        assertDoesNotThrow(() -> rules.applyTo("1234"));
    }
    
    @Test
    void applyTo_valorNoPositivo_lanzaExcepcion() {
        MetadataRules rules = new MetadataRules("positivo:true");
        
        Exception exception = assertThrows(RuntimeException.class, () -> 
                rules.applyTo("-1"));
        
        assertEquals("El valor debe ser positivo", exception.getMessage());
        
        Exception exception2 = assertThrows(RuntimeException.class, () -> 
                rules.applyTo("0"));
        
        assertEquals("El valor debe ser positivo", exception2.getMessage());
    }
    
    @Test
    void applyTo_valorPositivo_noLanzaExcepcion() {
        MetadataRules rules = new MetadataRules("positivo:true");
        
        assertDoesNotThrow(() -> rules.applyTo("1"));
        assertDoesNotThrow(() -> rules.applyTo("100"));
    }
    
    @Test
    void names_devuelveConjuntoDeReglasAplicadas() {
        MetadataRules rules = new MetadataRules("requerido:true,longitud:10,positivo:true");
        
        EnumSet<MetadataRulesEnum> result = rules.names();
        
        assertEquals(3, result.size());
        assertTrue(result.contains(MetadataRulesEnum.REQUERIDO));
        assertTrue(result.contains(MetadataRulesEnum.LONGITUD));
        assertTrue(result.contains(MetadataRulesEnum.POSITIVO));
    }
    
    @ParameterizedTest
    @MethodSource("provideInvalidRules")
    void constructor_conValoresInvalidos_lanzaExcepcion(String regla, Class<? extends Exception> exceptionClass) {
        assertThrows(exceptionClass, () -> new MetadataRules(regla));
    }
    
    private static Stream<Arguments> provideInvalidRules() {
        return Stream.of(
            Arguments.of("longitud:abc", NumberFormatException.class),
            Arguments.of("INVALID_RULE:10", IllegalArgumentException.class)
        );
    }
}