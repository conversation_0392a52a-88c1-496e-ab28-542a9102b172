package co.com.gedsys.base.domain.metadato;

import co.com.gedsys.base.domain.metadato.enums.FormatoMetadatoEnum;
import co.com.gedsys.base.domain.metadato.enums.MetadataRulesEnum;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

class EmailFormatoTest {

    @Test
    void formatoEmail_debeExistir() {
        // Verificar que el formato EMAIL existe en la enumeración
        assertDoesNotThrow(() -> FormatoMetadatoEnum.valueOf("EMAIL"));
    }

    @Test
    void formatoEmail_debePermitirReglasCorrectas() {
        // Verificar que el formato EMAIL permite las reglas REQUERIDO y LONGITUD
        assertTrue(FormatoMetadatoEnum.EMAIL.getAllowedRules().contains(MetadataRulesEnum.REQUERIDO));
        assertTrue(FormatoMetadatoEnum.EMAIL.getAllowedRules().contains(MetadataRulesEnum.LONGITUD));
    }

    @Test
    void metadataRules_debeValidarFormatoEmailCorrecto() {
        // Crear un metadato con formato EMAIL
        DefinicionMetadato definicion = DefinicionMetadato.create(
                "correo",
                TipoMetadatoEnum.CONTENIDO,
                FormatoMetadatoEnum.EMAIL
        );

        // Establecer reglas
        definicion.setRules("requerido:true,longitud:100");

        // Verificar que un email válido no lanza excepción
        assertDoesNotThrow(() -> definicion.generarMetadato("<EMAIL>"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"usuario", "usuario@", "usuario@dominio", "@dominio.com", "usuario@.com"})
    void metadataRules_debeRechazarEmailsInvalidos(String emailInvalido) {
        // Crear un metadato con formato EMAIL
        DefinicionMetadato definicion = DefinicionMetadato.create(
                "correo",
                TipoMetadatoEnum.CONTENIDO,
                FormatoMetadatoEnum.EMAIL
        );

        // Establecer reglas
        definicion.setRules("requerido:true");

        // Verificar que un email inválido lanza excepción
        Exception exception = assertThrows(RuntimeException.class, 
                () -> definicion.generarMetadato(emailInvalido));

        assertTrue(exception.getMessage().contains("formato de email inválido"));
    }
}
