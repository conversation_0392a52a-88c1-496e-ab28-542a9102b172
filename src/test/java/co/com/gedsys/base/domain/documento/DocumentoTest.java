package co.com.gedsys.base.domain.documento;

import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;

class DocumentoTest {

    private FirmaUsuario crearFirma(String firmante) {
        return FirmaUsuario.builder()
                .id(UUID.randomUUID())
                .firmante(firmante)
                .estado(EstadoDeLaFirma.PENDIENTE)
                .page(1)
                .x(100)
                .y(100)
                .width(100)
                .height(50)
                .observaciones("Firma de prueba")
                .build();
    }

    private Documento crearDocumento(String titulo,
                                     EstadoDocumento estado,
                                     List<FirmaUsuario> firmas) {

        return new Documento(
                UUID.randomUUID(),
                titulo,
                UUID.randomUUID().toString(),
                estado,
                null,
                null,
                "autor",
                null,
                List.of(),
                firmas,
                List.of(), List.of());
    }

    @Test
    void testRetornaFirmaDeUsuario() {
        // Arrange
        FirmaUsuario firmaJuan = crearFirma("juan");
        Documento documento = crearDocumento("Test Document",
                EstadoDocumento.BORRADOR,
                List.of(firmaJuan));

        // Act & Assert
        FirmaUsuario firma = documento.getFirma("juan");
        assertNotNull(firma);
        assertEquals(firmaJuan, firma);
    }

    @Test
    void lanzaErrorSiNoExisteFirma() {
        // Arrange
        FirmaUsuario firmaJuan = crearFirma("juan");
        Documento documento = crearDocumento("Test Document",
                EstadoDocumento.BORRADOR, List.of(firmaJuan));

        // Act & Assert
        assertThrows(
                FirmaDocumentoNoExistenteException.class,
                () -> documento.getFirma("maria")
        );
    }

    @Test
    void testPropietarioDeFirmaPuedeFirmarDocumento() {
        // Arrange
        FirmaUsuario firmaJuan = crearFirma("juan");
        Documento documento = crearDocumento("Test Document",
                EstadoDocumento.BORRADOR, List.of(firmaJuan));
        // Agregar y aprobar aprobación para poder firmar
        documento.setAprobaciones(List.of(new Aprobacion("jmarin")));
        documento.aprobar("jmarin");
        // Act & Assert
        assertDoesNotThrow(() -> documento.firmar("juan"));
        FirmaUsuario firmaRealizada = documento.getFirma("juan");
        assertEquals(EstadoDeLaFirma.COMPLETADA, firmaRealizada.getEstado());
        assertNotNull(firmaRealizada.getFirmadoEn());
    }

    @Test
    void testImpideFirmarDocumentoSiNoEstaAuthorizado() {
        FirmaUsuario firmaJuan = crearFirma("juan");
        Documento documento = crearDocumento("Test Document",
                EstadoDocumento.BORRADOR, List.of(firmaJuan));

        assertThrows(
                FirmaDocumentoNoExistenteException.class,
                () -> documento.firmar("maria")
        );
    }

    @Test
    void cuandoDocumentoRequiereAprobacionNoSePuedeFirmar() {
        // Arrange
        FirmaUsuario firmaJuan = crearFirma("juan");
        Documento documentoBorrador = crearDocumento("Doc Borrador",
                EstadoDocumento.BORRADOR, List.of(firmaJuan));
        documentoBorrador.setAprobaciones(List.of(new Aprobacion("jmarin")));
        // Act & Assert
        assertThrows(
                FirmaDocumentoException.class,
                () -> documentoBorrador.firmar("juan")
        );

        assertThrows(
                FirmaDocumentoException.class,
                () -> documentoBorrador.rechazarFirma("juan", "Observaciones")
        );

    }

    @Test
    void cuandoDocumentoNoTieneAprobacionesSePuedeFirmar() {
        // Arrange
        FirmaUsuario firmaJuan = crearFirma("juan");
        Documento documento = crearDocumento("Test Document",
                EstadoDocumento.BORRADOR,
                List.of(firmaJuan));
        // Act & Assert
        assertDoesNotThrow(() -> documento.firmar("juan"));
    }

    @Test
    void testUnFirmanteNoDeberiaFirmarNuevamente() {
        // Arrange
        FirmaUsuario firmaJuan = crearFirma("juan");
        Documento documento = crearDocumento("Test Document", EstadoDocumento.BORRADOR, List.of(firmaJuan));
        // Agregar y aprobar aprobación para poder firmar
        documento.setAprobaciones(List.of(new Aprobacion("jmarin")));
        documento.aprobar("jmarin");
        // Act & Assert
        assertDoesNotThrow(() -> documento.firmar("juan"));
        assertThrows(
                FirmaDocumentoException.class,
                () -> documento.firmar("juan")
        );
    }

    @Test
    void unDocumentoEstaFirmadoCuandoTieneTodasLasFirmasCompletadas() {
        // Arrange
        FirmaUsuario firmaJuan = crearFirma("juan");
        FirmaUsuario firmaMaria = crearFirma("maria");
        Documento documento = crearDocumento("Test Document", EstadoDocumento.BORRADOR, List.of(firmaJuan, firmaMaria));
        // Agregar y aprobar aprobación para poder firmar
        documento.setAprobaciones(List.of(new Aprobacion("jmarin")));
        documento.aprobar("jmarin");
        // Act & Assert
        assertDoesNotThrow(() -> documento.firmar("juan"));
        assertFalse(documento.estaFirmado());
        assertDoesNotThrow(() -> documento.firmar("maria"));
        assertTrue(documento.estaFirmado());
    }

    @Test
    void deberiaLanzarErrorSiSeRechazaDocumentoFirmado() {
        // Arrange
        FirmaUsuario firmaJuan = crearFirma("juan");
        Documento documento = crearDocumento("Test Document", EstadoDocumento.BORRADOR, List.of(firmaJuan));
        documento.firmar("juan");
        // Act & Assert
        assertThrows(
                RechazoFirmaDocumentoException.class,
                () -> documento.rechazarFirma("juan", "Observacion")
        );
    }

    @Test
    void deberiaLanzarErrorSiLoRechazaUnUsuarioQueNoEsElFirmante() {
        FirmaUsuario firma = crearFirma("juan");
        Documento documento = crearDocumento("Test Document", EstadoDocumento.BORRADOR, List.of(firma));

        assertThrows(
                FirmaDocumentoNoExistenteException.class,
                () -> documento.rechazarFirma("maria", "Observacion")
        );
    }

    @Test
    void unFirmanteDeberiaPoderRechazarFirma() {
        FirmaUsuario firma = crearFirma("juan");
        Documento documento = crearDocumento("Test Document", EstadoDocumento.BORRADOR, List.of(firma));

        assertDoesNotThrow(() -> documento.rechazarFirma("juan", "Observacion"));
        assertTrue(documento.getFirma("juan").esRechazada());
        assertThat(documento.getFirma("juan"))
                .hasFieldOrPropertyWithValue("observaciones", "Observacion");
    }

    @Test
    void deberiaTenerEstadoRechazadoSiSeRechazaUnaFirma() {
        FirmaUsuario firma = crearFirma("juan");
        Documento documento = crearDocumento("Test Document", EstadoDocumento.BORRADOR, List.of(firma));

        assertDoesNotThrow(() -> documento.rechazarFirma("juan", "Observacion"));
        assertTrue(documento.fueRechazado());
    }

    @Test
    void deberiaLanzarErrorSiEsRechazadoSinAgregarObservacion() {
        FirmaUsuario firma = crearFirma("juan");
        Documento documento = crearDocumento("Test Document", EstadoDocumento.BORRADOR, List.of(firma));

        assertThrows(
                RechazoFirmaDocumentoException.class,
                () -> documento.rechazarFirma("juan", "")
        );
    }

    @Test
    void noSeDeberiaPoderRechazarDosVecesConsecutivamente() {
        FirmaUsuario firma = crearFirma("juan");
        Documento documento = crearDocumento("Test Document", EstadoDocumento.BORRADOR, List.of(firma));

        assertDoesNotThrow(() -> documento.rechazarFirma("juan", "Observacion"));
        assertThrows(
                RechazoFirmaDocumentoException.class,
                () -> documento.rechazarFirma("juan", "Observacion")
        );
    }

    @Test
    void deberiaAgregarAprobacionCuandoEsBorrador() {
        FirmaUsuario firma = crearFirma("juan");
        Documento documento = crearDocumento("Test Document", EstadoDocumento.BORRADOR, List.of(firma));
        documento.setAprobaciones(List.of(new Aprobacion("jmarin"), new Aprobacion("emontoya")));
        documento.setAprobaciones(List.of(new Aprobacion("dplata")));
        documento.setAprobaciones(List.of(new Aprobacion("dplata")));
        assertThat(documento.getAprobaciones()).hasSize(3);
    }

    @Test
    void deberiaAgregarAprobacionCuandoFueRechazado() {
        FirmaUsuario firma = crearFirma("juan");
        Documento documento = crearDocumento("Test Document", EstadoDocumento.BORRADOR, List.of(firma));
        documento.rechazarFirma("juan", "Observacion");
        documento.setAprobaciones(List.of(new Aprobacion("jmarin"), new Aprobacion("emontoya")));
        assertThat(documento.getAprobaciones()).hasSize(2);
    }

    @Test
    void deberiaLanzarErrorCuandoSeAgreganAprobacionesAUnDocumentoNoEsBorrador() {
        FirmaUsuario firma = crearFirma("juan");
        Documento documento = crearDocumento("Test Document", EstadoDocumento.EN_TRAMITE, List.of(firma));
        assertThrows(
                AprobacionException.class,
                () -> documento.setAprobaciones(List.of(new Aprobacion("jmarin"), new Aprobacion("emontoya")))
        );
    }

    @Test
    void deberiaPermitirAprobarCuandoEsUnBorrador() {
        FirmaUsuario firma = crearFirma("juan");
        Documento documento = crearDocumento("Test Document", EstadoDocumento.BORRADOR, List.of(firma));
        documento.setAprobaciones(List.of(new Aprobacion("jmarin")));
        documento.aprobar("jmarin");
        assertTrue(documento.estaAprobado());
    }

    @Test
    void lanzaErrorCuandoSeApruebaODesapruebaUnDocumentoQueNoEsBorrador() {
        FirmaUsuario firma = crearFirma("juan");
        // Crear documento en estado EN_TRAMITE directamente
        Documento documento = crearDocumento("Test Document", EstadoDocumento.EN_TRAMITE, List.of(firma));
        // Intentar agregar aprobaciones debería fallar
        assertThrows(AprobacionException.class,
            () -> documento.setAprobaciones(List.of(new Aprobacion("jmarin"))));
    }

    @Test
    void cuandoElUltimoAprobadorDesapruebaDeberiaMarcarDocumentoComoRechazado() {
        FirmaUsuario firma = crearFirma("juan");
        Documento documento = crearDocumento("Test Document", EstadoDocumento.BORRADOR, List.of(firma));
        documento.setAprobaciones(List.of(new Aprobacion("jmarin"), new Aprobacion("emontoya")));
        documento.desaprobar("jmarin", "Observacion");
        assertTrue(documento.fueRechazado());
    }

    @Test
    void documentoEstaAprobadoSiCadaAprobacionEsAprobada() {
        var firma = crearFirma("juan");
        var documento = crearDocumento("Test Document", EstadoDocumento.BORRADOR, List.of(firma));
        documento.setAprobaciones(List.of(new Aprobacion("jmarin"), new Aprobacion("emontoya")));
        assertFalse(documento.estaAprobado());
        documento.aprobar("emontoya");
        assertFalse(documento.estaAprobado());
        documento.aprobar("jmarin");
        assertTrue(documento.estaAprobado());
    }

    @Test
    void deberiaAgregarAnexo() {
        var anexo1 = mock(Anexo.class);
        var anexo2 = mock(Anexo.class);
        var anexo3 = mock(Anexo.class);
        var firma = crearFirma("jmarin");
        var documento = crearDocumento("Test Document", EstadoDocumento.BORRADOR, List.of(firma));

        // Test adding single anexo
        documento.agregarAnexo(anexo1);
        assertThat(documento.getAnexos()).hasSize(1);

        // Test adding null anexo - should not increase size
        documento.agregarAnexo(null);
        assertThat(documento.getAnexos()).hasSize(1);

        // Test setting list of anexos - should append
        documento.setAnexos(List.of(anexo2, anexo3));
        assertThat(documento.getAnexos()).hasSize(3);

        // Test setting duplicate anexos - should not increase size
        documento.setAnexos(List.of(anexo1, anexo1));
        assertThat(documento.getAnexos()).hasSize(3);

        // Test setting list with null anexo - should not change size
        var anexosConNull = new ArrayList<Anexo>();
        anexosConNull.add(null);
        documento.setAnexos(anexosConNull);
        assertThat(documento.getAnexos()).hasSize(3);

        // Test setting null list - should not change size
        documento.setAnexos(null);
        assertThat(documento.getAnexos()).hasSize(3);
    }

    @Test
    void deberiaPermitirCrearDocumentosConNuevosEstados() {
        // Test que se pueden crear documentos con los nuevos estados
        var firma = crearFirma("juan");

        var documentoBorrador = crearDocumento("Doc Borrador", EstadoDocumento.BORRADOR, List.of(firma));
        assertEquals(EstadoDocumento.BORRADOR, documentoBorrador.getEstado());

        var documentoDescartado = crearDocumento("Doc Descartado", EstadoDocumento.DESCARTADO, List.of(firma));
        assertEquals(EstadoDocumento.DESCARTADO, documentoDescartado.getEstado());

        var documentoEnTramite = crearDocumento("Doc En Trámite", EstadoDocumento.EN_TRAMITE, List.of(firma));
        assertEquals(EstadoDocumento.EN_TRAMITE, documentoEnTramite.getEstado());

        var documentoFinalizado = crearDocumento("Doc Finalizado", EstadoDocumento.FINALIZADO, List.of(firma));
        assertEquals(EstadoDocumento.FINALIZADO, documentoFinalizado.getEstado());
    }

}
