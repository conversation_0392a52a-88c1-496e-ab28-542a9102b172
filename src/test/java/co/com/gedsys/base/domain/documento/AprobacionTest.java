package co.com.gedsys.base.domain.documento;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class AprobacionTest {

    @Test
    void iniciaPendiente() {
        var aprobacion = new Aprobacion("jmarin");
        assertTrue(aprobacion.estaPendiente());
    }

    @Test
    void deberiaRequerirObservacionesParaSerRechazada() {
        var aprobacion = new Aprobacion("jmarin");
        assertThrows(AprobacionException.class, () -> aprobacion.rechazar(null));
        assertThrows(AprobacionException.class, () -> aprobacion.rechazar(""));
        assertThrows(AprobacionException.class, () -> aprobacion.rechazar(" "));
        assertDoesNotThrow(() -> aprobacion.rechazar("Observaciones"));
        assertFalse(aprobacion.estaPendiente());
        assertTrue(aprobacion.fueRechazada());
    }

    @Test
    void requireAprobador() {
        assertThrows(AprobacionException.class, () -> new Aprobacion(null));
    }

    @Test
    void deberiaReconocerAprobador() {
        var aprobacion = new Aprobacion("jmarin");
        assertTrue(aprobacion.esAprobador("JMarin"));
        assertFalse(aprobacion.esAprobador("dplata"));
    }

    @Test
    void deberiaPermitirAlAsignadoAprobarUnaAprobacionPendiente() {
        var aprobacion = new Aprobacion("jmarin");
        assertThrows(AprobacionException.class, () -> aprobacion.aprobar("dplata"));
        assertDoesNotThrow(() -> aprobacion.aprobar("jmarin"));
        assertTrue(aprobacion.fueAprobada());
        assertNotNull(aprobacion.getAprobadoEn());
    }

    @Test
    void deberiaLanzarErrorSiSeApruebaNoPendiente() {
        var aprobacion = new Aprobacion("jmarin");
        aprobacion.aprobar("jmarin");
        assertThrows(AprobacionException.class, () -> aprobacion.aprobar("jmarin"));
        assertThrows(AprobacionException.class, () -> aprobacion.desaprueba("jmarin", "Observaciones"));
    }

    @Test
    void deberiaPermitirAlAsignadoDesaprobarUnaAprobacionPendiente() {
        var aprobacion = new Aprobacion("jmarin");
        assertThrows(AprobacionException.class, () -> aprobacion.desaprueba("dplata", "Observaciones"));
        assertThrows(AprobacionException.class, () -> aprobacion.desaprueba("jmarin", ""));
        assertThrows(AprobacionException.class, () -> aprobacion.desaprueba("jmarin", null));
        assertThrows(AprobacionException.class, () -> aprobacion.desaprueba("jmarin", " "));
        assertDoesNotThrow(() -> aprobacion.desaprueba("jmarin", "Observaciones"));
        assertTrue(aprobacion.fueRechazada());
    }

    @Test
    void deberiaVolverALosValoresIniciales() {
        var aprobacion = new Aprobacion("jmarin");
        aprobacion.reset();
        assertFalse(aprobacion.fueAprobada());
        assertFalse(aprobacion.fueRechazada());
        assertTrue(aprobacion.estaPendiente());
    }

    @Test
    void estaResueltaSiTieneFechaDeResolucion(){
        var aprobacion = new Aprobacion("jmarin");
        var aprobacion2 = new Aprobacion("jmarin");
        assertFalse(aprobacion.estaResuelta());
        aprobacion.aprobar("jmarin");
        assertTrue(aprobacion.estaResuelta());

        assertFalse(aprobacion2.estaResuelta());
        aprobacion2.desaprueba("jmarin", "Observaciones");
        assertTrue(aprobacion2.estaResuelta());
    }

}