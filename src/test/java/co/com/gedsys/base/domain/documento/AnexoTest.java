package co.com.gedsys.base.domain.documento;

import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

class AnexoTest {

    @Test
    void deberiaLanzarErrorCuandoFaltanCamposObligatorios() {
        Anexo.AnexoBuilder anexo = Anexo.builder();
        assertThrows(IllegalArgumentException.class, anexo::build);
        anexo.fileId(UUID.randomUUID().toString());
        assertThrows(IllegalArgumentException.class, anexo::build);
        anexo.extension("pdf");
        assertThrows(IllegalArgumentException.class, anexo::build);
        anexo.bytes(100L);
        assertThrows(IllegalArgumentException.class, anexo::build);
        anexo.nombre("Anexo");
        anexo.descripcion("Descripcion");
        assertDoesNotThrow(anexo::build);
    }

}