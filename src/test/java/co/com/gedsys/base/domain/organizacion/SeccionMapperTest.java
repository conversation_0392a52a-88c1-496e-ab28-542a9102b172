package co.com.gedsys.base.domain.organizacion;

import co.com.gedsys.base.application.dto.SeccionDTO;
import co.com.gedsys.base.application.dto.UsuarioSeccionDTO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes = SeccionMapperTest.TestConfig.class)
class SeccionMapperTest {

    @Autowired
    private SeccionMapper seccionMapper;

    @Test
    @DisplayName("Debe mapear correctamente Seccion a SeccionDTO")
    void debeMappearSeccionASeccionDTO() {
        // Arrange
        UUID id = UUID.randomUUID();
        
        Seccion seccionPadre = new Seccion("01.00.00", "Sección Padre");
        seccionPadre.setId(id);
        seccionPadre.setResponsable("Responsable Padre");
        seccionPadre.setEstado(EstadoSeccion.ACTIVA);
        
        Seccion seccion = new Seccion("01.01.00", "Sección Hija");
        seccion.setId(UUID.randomUUID());
        seccion.setPadre(seccionPadre);
        
        // Act
        SeccionDTO resultado = seccionMapper.toDTO(seccion);
        
        // Assert
        assertThat(resultado).isNotNull();
        assertThat(resultado.codigo()).isEqualTo("01.01.00");
        assertThat(resultado.nombre()).isEqualTo("Sección Hija");
        assertThat(resultado.padre()).isNotNull();
        assertThat(resultado.padre().id()).isEqualTo(id.toString());
        assertThat(resultado.padre().codigo()).isEqualTo("01.00.00");
        assertThat(resultado.padre().nombre()).isEqualTo("Sección Padre");
        assertThat(resultado.padre().responsable()).isEqualTo("Responsable Padre");
        assertThat(resultado.padre().estado()).isEqualTo("ACTIVA");
    }
    
    @Test
    @DisplayName("Debe mapear correctamente UsuarioSeccion a UsuarioSeccionDTO")
    void debeMappearUsuarioSeccionAUsuarioSeccionDTO() {
        // Arrange
        UUID id = UUID.randomUUID();
        
        Seccion seccion = new Seccion("01.00.00", "Sección Test");
        seccion.setId(id);
        
        UsuarioSeccion usuarioSeccion = new UsuarioSeccion("usuario1", TipoRelacionUsuarioSeccion.PRIMARIA, seccion);
        
        // Act
        UsuarioSeccionDTO resultado = seccionMapper.toDTO(usuarioSeccion);
        
        // Assert
        assertThat(resultado).isNotNull();
        assertThat(resultado.username()).isEqualTo("usuario1");
        assertThat(resultado.relacion()).isEqualTo("PRIMARIA");
        assertThat(resultado.seccion()).isNotNull();
        assertThat(resultado.seccion().id()).isEqualTo(id.toString());
        assertThat(resultado.seccion().codigo()).isEqualTo("01.00.00");
        assertThat(resultado.seccion().nombre()).isEqualTo("Sección Test");
    }

    @Configuration
    @ComponentScan(
        basePackages = "co.com.gedsys.base.domain.organizacion",
        includeFilters = @ComponentScan.Filter(
            type = FilterType.ASSIGNABLE_TYPE,
            classes = SeccionMapper.class
        )
    )
    static class TestConfig {
    }
}