package co.com.gedsys.base.adapter.http.planeacion.series_documentales;

import co.com.gedsys.base.application.dto.SerieDocumentalDto;
import co.com.gedsys.base.application.usecase.planeacion.series_documentales.ActivarSerieDocumentalCommand;
import co.com.gedsys.base.application.usecase.planeacion.series_documentales.ActivarSerieDocumentalUseCase;
import co.com.gedsys.base.application.usecase.planeacion.series_documentales.InactivarSerieDocumentalCommand;
import co.com.gedsys.base.application.usecase.planeacion.series_documentales.InactivarSerieDocumentalUseCase;
import co.com.gedsys.base.domain.serie_documental.SeriePadreInactivaException;
import co.com.gedsys.base.domain.serie_documental.SerieTieneDependenciasActivasException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SerieDocumentalControllerTest {

    @Mock
    private ActivarSerieDocumentalUseCase activarSerieDocumentalUseCase;

    @Mock
    private InactivarSerieDocumentalUseCase inactivarSerieDocumentalUseCase;

    @InjectMocks
    private SerieDocumentalController controller;

    @Test
    @DisplayName("Debe activar una serie documental correctamente")
    void debeActivarSerieDocumentalCorrectamente() {
        // Arrange
        UUID serieId = UUID.randomUUID();
        SerieDocumentalDto serieDocumentalDto = new SerieDocumentalDto(
            serieId,
            "100",
            "Serie de prueba",
            "SERIE",
            "ACTIVA",
            "",
            ""
        );
        
        when(activarSerieDocumentalUseCase.ejecutar(any(ActivarSerieDocumentalCommand.class)))
            .thenReturn(serieDocumentalDto);
        
        // Act
        ResponseEntity<SerieDocumentalDto> response = controller.activar(serieId);
        
        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(serieDocumentalDto, response.getBody());
        verify(activarSerieDocumentalUseCase).ejecutar(new ActivarSerieDocumentalCommand(serieId));
    }

    @Test
    @DisplayName("Debe manejar excepción cuando la serie padre está inactiva")
    void debeManejarExcepcionCuandoSeriePadreInactiva() {
        // Arrange
        UUID serieId = UUID.randomUUID();
        String errorMessage = "La serie padre se encuentra inactiva y no se puede activar la subserie.";
        
        when(activarSerieDocumentalUseCase.ejecutar(any(ActivarSerieDocumentalCommand.class)))
            .thenThrow(new SeriePadreInactivaException(errorMessage));
        
        // Act & Assert
        SeriePadreInactivaException exception = assertThrows(
            SeriePadreInactivaException.class,
            () -> controller.activar(serieId)
        );
        
        assertEquals(errorMessage, exception.getMessage());
    }

    @Test
    @DisplayName("Debe inactivar una serie documental correctamente")
    void debeInactivarSerieDocumentalCorrectamente() {
        // Arrange
        UUID serieId = UUID.randomUUID();
        SerieDocumentalDto serieDocumentalDto = new SerieDocumentalDto(
            serieId,
            "100",
            "Serie de prueba",
            "SERIE",
            "INACTIVA",
            "",
            ""
        );
        
        when(inactivarSerieDocumentalUseCase.ejecutar(any(InactivarSerieDocumentalCommand.class)))
            .thenReturn(serieDocumentalDto);
        
        // Act
        ResponseEntity<SerieDocumentalDto> response = controller.inactivar(serieId);
        
        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(serieDocumentalDto, response.getBody());
        verify(inactivarSerieDocumentalUseCase).ejecutar(new InactivarSerieDocumentalCommand(serieId));
    }

    @Test
    @DisplayName("Debe manejar excepción cuando la serie tiene dependencias activas")
    void debeManejarExcepcionCuandoSerieTieneDependenciasActivas() {
        // Arrange
        UUID serieId = UUID.randomUUID();
        String errorMessage = "La serie documental tiene dependencias activas y no puede ser inactivada.";
        
        when(inactivarSerieDocumentalUseCase.ejecutar(any(InactivarSerieDocumentalCommand.class)))
            .thenThrow(new SerieTieneDependenciasActivasException(errorMessage));
        
        // Act & Assert
        SerieTieneDependenciasActivasException exception = assertThrows(
            SerieTieneDependenciasActivasException.class,
            () -> controller.inactivar(serieId)
        );
        
        assertEquals(errorMessage, exception.getMessage());
    }
}