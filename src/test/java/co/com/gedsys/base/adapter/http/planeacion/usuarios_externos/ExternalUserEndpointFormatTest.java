package co.com.gedsys.base.adapter.http.planeacion.usuarios_externos;

import co.com.gedsys.base.domain.usuario_externo.ExternalUserPropertyType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test para verificar el formato JSON correcto del endpoint de propiedades de usuarios externos
 */
class ExternalUserEndpointFormatTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
    }

    @Test
    @DisplayName("Debe deserializar correctamente un array JSON de ExternalUserPropertyRegistration")
    void debeDeserializarArrayJSONCorrectamente() throws Exception {
        // JSON en formato de array (correcto)
        String jsonArray = """
            [
                {
                    "propertyType": "EMAIL",
                    "propertyName": "Correo principal",
                    "propertyValue": "<EMAIL>",
                    "notes": "Correo de contacto principal"
                },
                {
                    "propertyType": "PHONE",
                    "propertyName": "Teléfono oficina",
                    "propertyValue": "6012345678",
                    "notes": "Teléfono de la oficina principal"
                }
            ]
        """;

        // Verificar que se puede deserializar como List<ExternalUserPropertyRegistration>
        assertDoesNotThrow(() -> {
            List<ExternalUserPropertyRegistration> properties = objectMapper.readValue(
                jsonArray, 
                objectMapper.getTypeFactory().constructCollectionType(List.class, ExternalUserPropertyRegistration.class)
            );
            
            assertNotNull(properties);
            assertEquals(2, properties.size());
            
            ExternalUserPropertyRegistration firstProperty = properties.get(0);
            assertEquals(ExternalUserPropertyType.EMAIL, firstProperty.propertyType());
            assertEquals("Correo principal", firstProperty.propertyName());
            assertEquals("<EMAIL>", firstProperty.propertyValue());
            assertEquals("Correo de contacto principal", firstProperty.notes());
            
            ExternalUserPropertyRegistration secondProperty = properties.get(1);
            assertEquals(ExternalUserPropertyType.PHONE, secondProperty.propertyType());
            assertEquals("Teléfono oficina", secondProperty.propertyName());
            assertEquals("6012345678", secondProperty.propertyValue());
            assertEquals("Teléfono de la oficina principal", secondProperty.notes());
        });
    }

    @Test
    @DisplayName("Debe fallar al deserializar un objeto JSON en lugar de array")
    void debeFallarAlDeserializarObjetoEnLugarDeArray() {
        // JSON en formato de objeto (incorrecto para el endpoint)
        String jsonObject = """
            {
                "propertyType": "EMAIL",
                "propertyName": "Correo principal",
                "propertyValue": "<EMAIL>",
                "notes": "Correo de contacto principal"
            }
        """;

        // Verificar que falla al intentar deserializar como List
        assertThrows(Exception.class, () -> {
            objectMapper.readValue(
                jsonObject, 
                objectMapper.getTypeFactory().constructCollectionType(List.class, ExternalUserPropertyRegistration.class)
            );
        });
    }

    @Test
    @DisplayName("Debe serializar correctamente un array de ExternalUserPropertyRegistration")
    void debeSerializarArrayCorrectamente() throws Exception {
        // Crear lista de propiedades
        List<ExternalUserPropertyRegistration> properties = List.of(
            new ExternalUserPropertyRegistration(
                ExternalUserPropertyType.EMAIL,
                "Correo principal",
                "<EMAIL>",
                "Correo de contacto principal"
            ),
            new ExternalUserPropertyRegistration(
                ExternalUserPropertyType.PHONE,
                "Teléfono oficina",
                "6012345678",
                "Teléfono de la oficina principal"
            )
        );

        // Serializar a JSON
        String json = objectMapper.writeValueAsString(properties);
        
        // Verificar que es un array JSON válido
        assertNotNull(json);
        assertTrue(json.startsWith("["));
        assertTrue(json.endsWith("]"));
        assertTrue(json.contains("EMAIL"));
        assertTrue(json.contains("PHONE"));
        assertTrue(json.contains("<EMAIL>"));
        assertTrue(json.contains("6012345678"));
    }

    @Test
    @DisplayName("Debe validar que el endpoint espera List<ExternalUserPropertyRegistration>")
    void debeValidarTipoEsperadoDelEndpoint() {
        // Verificar que ExternalUserPropertyRegistration es un record válido
        assertNotNull(ExternalUserPropertyRegistration.class);
        assertTrue(ExternalUserPropertyRegistration.class.isRecord());
        
        // Verificar que tiene los campos esperados
        var recordComponents = ExternalUserPropertyRegistration.class.getRecordComponents();
        assertEquals(4, recordComponents.length);
        
        // Verificar nombres de los campos
        assertEquals("propertyType", recordComponents[0].getName());
        assertEquals("propertyName", recordComponents[1].getName());
        assertEquals("propertyValue", recordComponents[2].getName());
        assertEquals("notes", recordComponents[3].getName());
        
        // Verificar tipos de los campos
        assertEquals(ExternalUserPropertyType.class, recordComponents[0].getType());
        assertEquals(String.class, recordComponents[1].getType());
        assertEquals(String.class, recordComponents[2].getType());
        assertEquals(String.class, recordComponents[3].getType());
    }

    @Test
    @DisplayName("Debe validar formato JSON con array vacío")
    void debeValidarArrayVacio() throws Exception {
        String emptyArrayJson = "[]";
        
        assertDoesNotThrow(() -> {
            List<ExternalUserPropertyRegistration> properties = objectMapper.readValue(
                emptyArrayJson, 
                objectMapper.getTypeFactory().constructCollectionType(List.class, ExternalUserPropertyRegistration.class)
            );
            
            assertNotNull(properties);
            assertTrue(properties.isEmpty());
        });
    }
}
