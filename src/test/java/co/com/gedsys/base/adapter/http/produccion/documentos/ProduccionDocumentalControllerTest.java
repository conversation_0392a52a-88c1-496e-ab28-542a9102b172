package co.com.gedsys.base.adapter.http.produccion.documentos;

import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.application.usecase.produccion.documental.ActualizarBorradorUseCase;
import co.com.gedsys.base.application.usecase.produccion.documental.command.UpdateDraftCommand;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import co.com.gedsys.base.application.usecase.produccion.documental.CargarDocumentoUseCase;
import co.com.gedsys.base.application.usecase.produccion.documental.CargarDocumentoCommand;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProduccionDocumentalControllerTest {

    @Mock
    private ActualizarBorradorUseCase actualizarBorradorUseCase;

    @Mock
    private ProduccionDocumentosHttpMapper mapper;

    @Mock
    private CargarDocumentoUseCase cargarDocumentoUseCase;

    @InjectMocks
    private ProduccionDocumentalController controller;

    @Nested
    @DisplayName("Actualización de borradores")
    class ActualizarBorradorTests {

        @Test
        @DisplayName("Debería actualizar un borrador solo con título")
        void deberiaActualizarBorradorSoloConTitulo() {
            // Arrange
            var id = UUID.randomUUID();
            var metadatos = new LinkedHashMap<String, String>();
            var solicitud = new SolicitudActualizacionDocumento(
                    "Nuevo título", metadatos, List.of()
            );
            var documentoDTO = new DocumentoDTO(
                    id.toString(),
                    "Nuevo título",
                    "autor",
                    null,
                    "BORRADOR",
                    new DocumentoDTO.TipoDocumentalDS("1", "tipo", "ACTIVO"),
                    new DocumentoDTO.UnidadDocumentalDS("1", "unidad", null, "001"),
                    Collections.emptySet(),
                    Collections.emptyList(),
                    Collections.emptyList(),
                    Collections.emptyList(),
                    Collections.emptyList()
            );
            var respuestaEsperada = new RespuestaDocumentoProducido(
                    id,
                    "Nuevo título",
                    null,
                    "autor",
                    "001",
                    "unidad",
                    "1",
                    "tipo",
                    "1",
                    List.of(),
                    List.of(),
                    List.of(),
                    List.of()
            );

            when(actualizarBorradorUseCase.execute(any(UpdateDraftCommand.class))).thenReturn(documentoDTO);
            when(mapper.toResponse(documentoDTO)).thenReturn(respuestaEsperada);

            // Act
            ResponseEntity<RespuestaDocumentoProducido> respuesta = controller.actualizarBorrador(id, solicitud);

            // Assert
            assertThat(respuesta.getStatusCode().value()).isEqualTo(200);
            assertThat(respuesta.getBody()).isEqualTo(respuestaEsperada);

            verify(actualizarBorradorUseCase).execute(new UpdateDraftCommand(
                    id, "Nuevo título", metadatos, List.of()
            ));
            verify(mapper).toResponse(documentoDTO);
        }

        @Test
        @DisplayName("Debería actualizar un borrador con metadatos completos")
        void deberiaActualizarBorradorConMetadatosCompletos() {
            // Arrange
            var id = UUID.randomUUID();
            var metadatos = new LinkedHashMap<String, String>();
            metadatos.put("seccion", "1");
            metadatos.put("serie", "2");
            metadatos.put("subserie", "3");
            var solicitud = new SolicitudActualizacionDocumento(
                    "Nuevo título", metadatos, List.of()
            );
            var documentoDTO = new DocumentoDTO(
                    id.toString(),
                    "Nuevo título",
                    "autor",
                    "file-123",
                    "BORRADOR",
                    new DocumentoDTO.TipoDocumentalDS("1", "tipo", "ACTIVO"),
                    new DocumentoDTO.UnidadDocumentalDS("1", "unidad", null, "001"),
                    Collections.emptySet(),
                    Collections.emptyList(),
                    Collections.emptyList(),
                    Collections.emptyList(),
                    Collections.emptyList()
            );
            var respuestaEsperada = new RespuestaDocumentoProducido(
                    id,
                    "Nuevo título",
                    "file-123",
                    "autor",
                    "001",
                    "unidad",
                    "1",
                    "tipo",
                    "1",
                    List.of(),
                    List.of(),
                    List.of(),
                    List.of()
            );

            when(actualizarBorradorUseCase.execute(any(UpdateDraftCommand.class))).thenReturn(documentoDTO);
            when(mapper.toResponse(documentoDTO)).thenReturn(respuestaEsperada);

            // Act
            ResponseEntity<RespuestaDocumentoProducido> respuesta = controller.actualizarBorrador(id, solicitud);

            // Assert
            assertThat(respuesta.getStatusCode().value()).isEqualTo(200);
            assertThat(respuesta.getBody()).isEqualTo(respuestaEsperada);

            verify(actualizarBorradorUseCase).execute(new UpdateDraftCommand(
                    id, "Nuevo título", metadatos, List.of()
            ));
            verify(mapper).toResponse(documentoDTO);
        }

        @Test
        @DisplayName("Debería actualizar un borrador con anexos")
        void deberiaActualizarBorradorConAnexos() {
            // Arrange
            var id = UUID.randomUUID();
            var metadatos = new LinkedHashMap<String, String>();
            var anexos = List.of(
                new SolicitudActualizacionDocumento.RegistroAnexo(
                    "anexo1.pdf",
                    "Descripción del anexo",
                    "file-456",
                    "abc123",
                    1024L,
                    "pdf"
                )
            );
            var solicitud = new SolicitudActualizacionDocumento(
                    "Nuevo título", 
                    metadatos,
                    anexos
            );
            var anexosDTO = anexos.stream()
                .map(anexo -> new DocumentoDTO.AnexoDS(
                    UUID.randomUUID(),
                    anexo.nombre(),
                    anexo.descripcion(),
                    anexo.fileId(),
                    anexo.hash(),
                    anexo.bytes(),
                    anexo.extension()
                ))
                .toList();

            var anexosRespuesta = anexosDTO.stream()
                .map(anexo -> new RespuestaDocumentoProducido.AnexoDS(
                    UUID.randomUUID(),
                    anexo.nombre(),
                    anexo.descripcion(),
                    anexo.fileId(),
                    anexo.hash(),
                    anexo.bytes(),
                    anexo.extension()
                ))
                .toList();

            var documentoDTO = new DocumentoDTO(
                    id.toString(),
                    "Nuevo título",
                    "autor",
                    "file-123",
                    "BORRADOR",
                    new DocumentoDTO.TipoDocumentalDS("1", "tipo", "ACTIVO"),
                    new DocumentoDTO.UnidadDocumentalDS("1", "unidad", null, "001"),
                    Collections.emptySet(),
                    Collections.emptyList(),
                    Collections.emptyList(),
                    Collections.emptyList(),
                    anexosDTO
            );
            var respuestaEsperada = new RespuestaDocumentoProducido(
                    id,
                    "Nuevo título",
                    "file-123",
                    "autor",
                    "001",
                    "unidad",
                    "1",
                    "tipo",
                    "1",
                    List.of(),
                    List.of(),
                    List.of(),
                    anexosRespuesta
            );

            var anexoCommand = new UpdateDraftCommand.RegistroAnexo(
                "anexo1.pdf",
                "Descripción del anexo",
                "file-456",
                "abc123",
                1024L,
                "pdf"
            );

            when(mapper.toCommandAnexo(any(SolicitudActualizacionDocumento.RegistroAnexo.class)))
                .thenReturn(anexoCommand);

            when(actualizarBorradorUseCase.execute(any(UpdateDraftCommand.class))).thenReturn(documentoDTO);
            when(mapper.toResponse(documentoDTO)).thenReturn(respuestaEsperada);

            // Act
            ResponseEntity<RespuestaDocumentoProducido> respuesta = controller.actualizarBorrador(id, solicitud);

            // Assert
            assertThat(respuesta.getStatusCode().value()).isEqualTo(200);
            assertThat(respuesta.getBody()).isEqualTo(respuestaEsperada);

            verify(actualizarBorradorUseCase).execute(new UpdateDraftCommand(
                    id, "Nuevo título", metadatos, List.of(anexoCommand)
            ));
            verify(mapper).toResponse(documentoDTO);
        }
    }

    @Test
    @DisplayName("Debería cargar un documento correctamente (sin firmas ni aprobaciones)")
    void deberiaCargarDocumentoCorrectamente() {
        // Arrange
        var solicitud = new SolicitudCargaDocumental(
                "Título",
                UUID.randomUUID().toString(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                "autor",
                List.of(new MetadadoProduccionDocumental("asunto", "valor", TipoMetadatoEnum.CONTENIDO)),
                Collections.emptyList()  // anexos
        );
        var command = new CargarDocumentoCommand(
                solicitud.titulo(),
                solicitud.autor(),
                solicitud.fileId(),
                solicitud.tipoDocumentalId(),
                solicitud.unidadDocumentalId(),
                List.of(new CargarDocumentoCommand.Metadato("asunto", "valor", co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum.CONTENIDO)),
                Collections.emptyList()
        );
        var documentoDTO = new DocumentoDTO(
                UUID.randomUUID().toString(),
                solicitud.titulo(),
                solicitud.autor(),
                solicitud.fileId(),
                "BORRADOR",
                null, null, Collections.emptySet(), Collections.emptyList(), Collections.emptyList(), Collections.emptyList(), Collections.emptyList()
        );
        var respuestaEsperada = new RespuestaDocumentoProducido(
                UUID.randomUUID(),
                solicitud.titulo(),
                solicitud.fileId(),
                solicitud.autor(),
                null, null, null, null, null,
                List.of(), List.of(), List.of(), List.of()
        );
        when(mapper.toCargarDocumentoCommand(solicitud)).thenReturn(command);
        when(cargarDocumentoUseCase.execute(command)).thenReturn(documentoDTO);
        when(mapper.toResponse(documentoDTO)).thenReturn(respuestaEsperada);

        // Act
        var respuesta = controller.cargarDocumento(solicitud);

        // Assert
        assertThat(respuesta).isEqualTo(respuestaEsperada);
        verify(mapper).toCargarDocumentoCommand(solicitud);
        verify(cargarDocumentoUseCase).execute(command);
        verify(mapper).toResponse(documentoDTO);
    }

    @Test
    @DisplayName("Debe cargar documento con anexos correctamente")
    void deberiaCargarDocumentoConAnexosCorrectamente() {
        // Arrange
        var solicitud = new SolicitudCargaDocumental(
                "Título con anexos",
                UUID.randomUUID().toString(),
                UUID.randomUUID(),
                UUID.randomUUID(),
                "autor",
                List.of(new MetadadoProduccionDocumental("asunto", "valor", TipoMetadatoEnum.CONTENIDO)),
                List.of(new AnexoProduccionDocumental(
                        "anexo1",
                        "Descripción del anexo",
                        UUID.randomUUID().toString(),
                        "hash123",
                        1000L,
                        "pdf"
                ))
        );
        var command = new CargarDocumentoCommand(
                solicitud.titulo(),
                solicitud.autor(),
                solicitud.fileId(),
                solicitud.tipoDocumentalId(),
                solicitud.unidadDocumentalId(),
                List.of(new CargarDocumentoCommand.Metadato("asunto", "valor", co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum.CONTENIDO)),
                List.of(new CargarDocumentoCommand.Anexo("anexo1", "Descripción del anexo", solicitud.anexos().get(0).fileId(), "hash123", 1000L, "pdf"))
        );
        var documentoDTO = new DocumentoDTO(
                UUID.randomUUID().toString(),
                solicitud.titulo(),
                solicitud.autor(),
                solicitud.fileId(),
                "BORRADOR",
                null, null, Collections.emptySet(), Collections.emptyList(), Collections.emptyList(), Collections.emptyList(), Collections.emptyList()
        );
        var respuestaEsperada = new RespuestaDocumentoProducido(
                UUID.randomUUID(),
                solicitud.titulo(),
                solicitud.fileId(),
                solicitud.autor(),
                null, null, null, null, null,
                List.of(), List.of(), List.of(), List.of()
        );
        when(mapper.toCargarDocumentoCommand(solicitud)).thenReturn(command);
        when(cargarDocumentoUseCase.execute(command)).thenReturn(documentoDTO);
        when(mapper.toResponse(documentoDTO)).thenReturn(respuestaEsperada);

        // Act
        var respuesta = controller.cargarDocumento(solicitud);

        // Assert
        assertThat(respuesta).isEqualTo(respuestaEsperada);
        verify(mapper).toCargarDocumentoCommand(solicitud);
        verify(cargarDocumentoUseCase).execute(command);
        verify(mapper).toResponse(documentoDTO);
    }
}
