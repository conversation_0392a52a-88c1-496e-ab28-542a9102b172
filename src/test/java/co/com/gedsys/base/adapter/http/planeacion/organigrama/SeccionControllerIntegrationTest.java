package co.com.gedsys.base.adapter.http.planeacion.organigrama;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import com.fasterxml.jackson.databind.ObjectMapper;

import co.com.gedsys.base.adapter.http.planeacion.secciones.ActualizarSeccionRequest;
import co.com.gedsys.base.adapter.http.planeacion.secciones.EstadoSeccionPlaneacion;
import co.com.gedsys.base.adapter.http.planeacion.secciones.UnidadProductoraRequest;

import org.junit.jupiter.api.BeforeEach;

import java.util.UUID;

import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Pruebas de integración para SeccionController.
 * Estas pruebas utilizan TestContainers para crear un contenedor de PostgreSQL
 * y probar la integración completa del controlador con la base de datos.
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@Testcontainers
@ActiveProfiles("test")
@Transactional
public class SeccionControllerIntegrationTest {

    @Container
    private static final PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:16-alpine")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");

    /**
     * Configuración dinámica de propiedades para Spring Boot.
     */
    @DynamicPropertySource
    static void registerPgProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        // Deshabilitar Flyway para esta prueba
        registry.add("spring.flyway.enabled", () -> "false");
        // Configurar Hibernate para crear el esquema
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
    }

    @Autowired
    private MockMvc mockMvc;

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
    }

    /**
     * Prueba para crear una sección.
     */
    @Test
    public void testCreateSeccion() throws Exception {
        // Crear una solicitud para una nueva sección
        UnidadProductoraRequest request = new UnidadProductoraRequest(
                "1.0.0",
                "Sección de Prueba",
                "Responsable de Prueba",
                null // Sin padre, será una sección raíz
        );

        // Realizar la petición POST
        ResultActions result = mockMvc.perform(post("/api/v1/planeacion/secciones")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)));

        // Verificar la respuesta
        result.andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id", notNullValue()))
                .andExpect(jsonPath("$.codigo", is("01.00.00")))
                .andExpect(jsonPath("$.nombre", is("Sección de Prueba")))
                .andExpect(jsonPath("$.responsable", is("Responsable de Prueba")));
    }

    /**
     * Prueba para obtener el organigrama.
     */
    @Test
    public void testGetOrganizationChart() throws Exception {
        // Primero creamos una sección para asegurarnos de que hay datos
        testCreateSeccion();

        // Realizar la petición GET
        ResultActions result = mockMvc.perform(get("/api/v1/planeacion/secciones")
                .param("estado", EstadoSeccionPlaneacion.ACTIVA.name())
                .contentType(MediaType.APPLICATION_JSON));

        // Verificar la respuesta
        result.andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }

    /**
     * Prueba para actualizar una sección.
     */
    @Test
    public void testUpdateSeccion() throws Exception {
        // Primero creamos una sección
        UnidadProductoraRequest createRequest = new UnidadProductoraRequest(
                "2.0.0",
                "Sección Original",
                "Responsable Original",
                null
        );

        // Realizar la petición POST para crear
        String createResponse = mockMvc.perform(post("/api/v1/planeacion/secciones")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();

        // Extraer el ID de la respuesta
        String id = objectMapper.readTree(createResponse).get("id").asText();

        // Crear solicitud de actualización
        ActualizarSeccionRequest updateRequest = new ActualizarSeccionRequest(
                "Sección Actualizada",
                "Responsable Actualizado"
        );

        // Realizar la petición PATCH para actualizar
        ResultActions updateResult = mockMvc.perform(patch("/api/v1/planeacion/secciones/" + id)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)));

        // Verificar la respuesta
        updateResult.andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id", is(id)))
                .andExpect(jsonPath("$.nombre", is("Sección Actualizada")))
                .andExpect(jsonPath("$.responsable", is("Responsable Actualizado")));
    }

    /**
     * Prueba para eliminar una sección.
     */
    @Test
    public void testDeleteSeccion() throws Exception {
        // Primero creamos una sección
        UnidadProductoraRequest createRequest = new UnidadProductoraRequest(
                "3.0.0",
                "Sección a Eliminar",
                "Responsable",
                null
        );

        // Realizar la petición POST para crear
        String createResponse = mockMvc.perform(post("/api/v1/planeacion/secciones")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();

        // Extraer el ID de la respuesta
        String id = objectMapper.readTree(createResponse).get("id").asText();

        // Realizar la petición DELETE
        ResultActions deleteResult = mockMvc.perform(delete("/api/v1/planeacion/secciones/" + id));

        // Verificar la respuesta
        deleteResult.andExpect(status().isNoContent());

        // Verificar que la sección ya no existe intentando obtenerla
        mockMvc.perform(get("/api/v1/planeacion/secciones")
                .param("estado", EstadoSeccionPlaneacion.ACTIVA.name()))
                .andExpect(status().isOk());
    }

    /**
     * Prueba para crear una sección con un padre.
     */
    @Test
    public void testMoveSeccion() throws Exception {
        // Primero creamos una sección padre (un despacho)
        UnidadProductoraRequest parentRequest = new UnidadProductoraRequest(
                "1.0.0",
                "Sección Padre",
                "Responsable Padre",
                null
        );

        // Realizar la petición POST para crear el padre
        String parentResponse = mockMvc.perform(post("/api/v1/planeacion/secciones")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(parentRequest)))
                .andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();

        // Extraer el ID del padre
        String parentId = objectMapper.readTree(parentResponse).get("id").asText();
        UUID parentUUID = UUID.fromString(parentId);

        // Crear una sección hija (una secretaría bajo el despacho)
        UnidadProductoraRequest childRequest = new UnidadProductoraRequest(
                "1.1.0",
                "Sección Hija",
                "Responsable Hijo",
                parentUUID
        );

        // Realizar la petición POST para crear la sección hija
        ResultActions childResult = mockMvc.perform(post("/api/v1/planeacion/secciones")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(childRequest)));

        // Verificar la respuesta
        childResult.andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id", notNullValue()))
                .andExpect(jsonPath("$.codigo", is("01.01.00")))
                .andExpect(jsonPath("$.nombre", is("Sección Hija")))
                .andExpect(jsonPath("$.responsable", is("Responsable Hijo")));
    }
}
