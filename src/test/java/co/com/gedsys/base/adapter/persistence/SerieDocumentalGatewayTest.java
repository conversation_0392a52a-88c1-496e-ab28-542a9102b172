package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.adapter.persistence.mappers.SerieDocumentalPersistenceMapper;
import co.com.gedsys.base.domain.serie_documental.EstadoSerie;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import co.com.gedsys.base.infrastructure.data_access.SerieDocumentalEntity;
import co.com.gedsys.base.infrastructure.data_access.repository.SerieDocumentalJpaRepository;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Example;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SerieDocumentalGatewayTest {

    @Mock
    private SerieDocumentalJpaRepository jpaRepository;

    @Mock
    private SerieDocumentalPersistenceMapper mapper;

    @InjectMocks
    private SerieDocumentalGateway gateway;

    @Test
    @DisplayName("Debe verificar si tiene hijas activas")
    void debeVerificarSiTieneHijasActivas() {
        // Arrange
        UUID serieId = UUID.randomUUID();
        when(jpaRepository.existsByPadreIdAndEstado(serieId, EstadoSerie.ACTIVA)).thenReturn(true);
        
        // Act
        boolean resultado = gateway.tieneHijasActivas(serieId);
        
        // Assert
        assertTrue(resultado);
        verify(jpaRepository).existsByPadreIdAndEstado(serieId, EstadoSerie.ACTIVA);
    }

    @Test
    @DisplayName("Debe verificar si tiene padre inactivo")
    void debeVerificarSiTienePadreInactivo() {
        // Arrange
        UUID serieId = UUID.randomUUID();
        UUID padreId = UUID.randomUUID();
        
        SerieDocumentalEntity hijaEntity = new SerieDocumentalEntity();
        hijaEntity.setId(serieId);
        
        SerieDocumentalEntity padreEntity = new SerieDocumentalEntity();
        padreEntity.setId(padreId);
        padreEntity.setEstado(EstadoSerie.INACTIVA);
        
        hijaEntity.setPadre(padreEntity);
        
        when(jpaRepository.findById(serieId)).thenReturn(Optional.of(hijaEntity));
        when(jpaRepository.findById(padreId)).thenReturn(Optional.of(padreEntity));
        
        // Act
        boolean resultado = gateway.tienePadreInactivo(serieId);
        
        // Assert
        assertTrue(resultado);
        verify(jpaRepository).findById(serieId);
        verify(jpaRepository).findById(padreId);
    }

    @Test
    @DisplayName("Debe guardar una serie documental")
    void debeGuardarSerieDocumental() {
        // Arrange
        SerieDocumental serieDocumental = new SerieDocumental("100", "Serie de prueba");
        SerieDocumentalEntity entity = new SerieDocumentalEntity();
        
        when(mapper.toEntity(serieDocumental)).thenReturn(entity);
        
        // Act
        gateway.guardar(serieDocumental);
        
        // Assert
        verify(mapper).toEntity(serieDocumental);
        verify(jpaRepository).save(entity);
    }

    @Test
    @DisplayName("Debe buscar una serie documental por ID")
    void debeBuscarSerieDocumentalPorId() {
        // Arrange
        UUID serieId = UUID.randomUUID();
        SerieDocumentalEntity entity = new SerieDocumentalEntity();
        SerieDocumental serieDocumental = new SerieDocumental("100", "Serie de prueba");
        
        when(jpaRepository.findById(serieId)).thenReturn(Optional.of(entity));
        when(mapper.toDomain(entity)).thenReturn(serieDocumental);
        
        // Act
        Optional<SerieDocumental> resultado = gateway.findById(serieId);
        
        // Assert
        assertTrue(resultado.isPresent());
        assertEquals(serieDocumental, resultado.get());
        verify(jpaRepository).findById(serieId);
        verify(mapper).toDomain(entity);
    }

    @Test
    @DisplayName("Debe verificar si existe por código")
    void debeVerificarSiExistePorCodigo() {
        // Arrange
        String codigo = "100";
        when(jpaRepository.exists(any(Example.class))).thenReturn(true);
        
        // Act
        boolean resultado = gateway.existenciaPorCodigo(codigo);
        
        // Assert
        assertTrue(resultado);
        verify(jpaRepository).exists(any(Example.class));
    }

    @Test
    @DisplayName("Debe verificar métodos de validación de dependencias")
    void debeVerificarMetodosDeValidacionDeDependencias() {
        // Arrange
        UUID serieId = UUID.randomUUID();
        
        // Act & Assert
        // Estos métodos aún no están implementados según los TODOs en el código
        assertFalse(gateway.tieneClasificacionesDocumentalesAsociadas(serieId));
        assertFalse(gateway.tieneDocumentosAsociados(serieId));
        assertFalse(gateway.tieneUnidadesDocumentalesAsociadas(serieId));
    }
}