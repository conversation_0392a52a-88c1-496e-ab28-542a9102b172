package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.adapter.persistence.mappers.DataAccessClasificacionDocumentalMapper;
import co.com.gedsys.base.adapter.persistence.mappers.SeccionPersistenceMapper;
import co.com.gedsys.base.infrastructure.data_access.ClasificacionDocumentalEntity;
import co.com.gedsys.base.infrastructure.data_access.SeccionEntity;
import co.com.gedsys.base.infrastructure.data_access.repository.ClasificacionDocumentalJpaRepository;
import co.com.gedsys.base.domain.organizacion.Seccion;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("ClasificacionDocumentalGateway - buscarSeccionPorClasificacionId")
class ClasificacionDocumentalGatewayTest {

    @Mock
    private ClasificacionDocumentalJpaRepository jpaRepository;

    @Mock
    private DataAccessClasificacionDocumentalMapper mapper;

    @Mock
    private SeccionPersistenceMapper seccionMapper;

    @InjectMocks
    private ClasificacionDocumentalGateway gateway;

    private UUID clasificacionId;
    private ClasificacionDocumentalEntity clasificacionEntity;
    private SeccionEntity seccionEntity;
    private Seccion seccionDomain;

    @BeforeEach
    void setUp() {
        clasificacionId = UUID.randomUUID();
        
        seccionEntity = new SeccionEntity();
        seccionEntity.setId(UUID.randomUUID());
        seccionEntity.setCodigo("001");
        seccionEntity.setNombre("Sección Test");
        
        clasificacionEntity = new ClasificacionDocumentalEntity();
        clasificacionEntity.setId(clasificacionId);
        clasificacionEntity.setSeccion(seccionEntity);
        
        seccionDomain = new Seccion("001", "Sección Test");
        seccionDomain.setId(seccionEntity.getId());
    }

    @Test
    @DisplayName("Debe retornar la sección cuando la clasificación existe")
    void deberiaRetornarSeccionCuandoClasificacionExiste() {
        // Arrange
        when(jpaRepository.findById(clasificacionId)).thenReturn(Optional.of(clasificacionEntity));
        when(seccionMapper.toDomain(seccionEntity)).thenReturn(seccionDomain);

        // Act
        Optional<Seccion> resultado = gateway.buscarSeccionPorClasificacionId(clasificacionId);

        // Assert
        assertThat(resultado).isPresent();
        assertThat(resultado.get()).isEqualTo(seccionDomain);
        assertThat(resultado.get().getId()).isEqualTo(seccionEntity.getId());
        assertThat(resultado.get().codigo()).isEqualTo("01.00.00");
        assertThat(resultado.get().getNombre()).isEqualTo("Sección Test");

        verify(jpaRepository).findById(clasificacionId);
        verify(seccionMapper).toDomain(seccionEntity);
    }

    @Test
    @DisplayName("Debe retornar Optional.empty() cuando la clasificación no existe")
    void deberiaRetornarVacioCuandoClasificacionNoExiste() {
        // Arrange
        when(jpaRepository.findById(clasificacionId)).thenReturn(Optional.empty());

        // Act
        Optional<Seccion> resultado = gateway.buscarSeccionPorClasificacionId(clasificacionId);

        // Assert
        assertThat(resultado).isEmpty();

        verify(jpaRepository).findById(clasificacionId);
        verifyNoInteractions(seccionMapper);
    }

    @Test
    @DisplayName("Debe retornar Optional.empty() cuando el ID es null")
    void deberiaRetornarVacioCuandoIdEsNull() {
        // Act
        Optional<Seccion> resultado = gateway.buscarSeccionPorClasificacionId(null);

        // Assert
        assertThat(resultado).isEmpty();

        verifyNoInteractions(jpaRepository);
        verifyNoInteractions(seccionMapper);
    }

    @Test
    @DisplayName("Debe manejar correctamente cuando la clasificación no tiene sección asociada")
    void deberiaManejarClasificacionSinSeccion() {
        // Arrange
        ClasificacionDocumentalEntity clasificacionSinSeccion = new ClasificacionDocumentalEntity();
        clasificacionSinSeccion.setId(clasificacionId);
        clasificacionSinSeccion.setSeccion(null);

        when(jpaRepository.findById(clasificacionId)).thenReturn(Optional.of(clasificacionSinSeccion));

        // Act
        Optional<Seccion> resultado = gateway.buscarSeccionPorClasificacionId(clasificacionId);

        // Assert
        assertThat(resultado).isEmpty();

        verify(jpaRepository).findById(clasificacionId);
        verifyNoInteractions(seccionMapper);
    }
}
