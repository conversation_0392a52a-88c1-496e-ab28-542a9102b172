FROM maven:3-amazoncorretto-23 AS builder
WORKDIR /build

ARG MAVEN_USERNAME
ARG MAVEN_PASSWORD

RUN mkdir -p /root/.m2 \
    && echo "<settings><servers><server><id>gedsys2-commons</id><username>${MAVEN_USERNAME}</username><password>${MAVEN_PASSWORD}</password></server><server><id>gedsys2-core</id><username>${MAVEN_USERNAME}</username><password>${MAVEN_PASSWORD}</password></server></servers></settings>" > /root/.m2/settings.xml

COPY . .
RUN mvn clean package install -DskipITs

FROM amazoncorretto:23-alpine

ENV JAVA_OPTS="-XX:+UseContainerSupport \
               -XX:MaxRAMPercentage=75 \
               -XX:InitialRAMPercentage=50 \
               -XX:+OptimizeStringConcat \
               -XX:+UseStringDeduplication \
               -Djava.security.egd=file:/dev/./urandom"
ENV SPRING_ARGS=""

WORKDIR /app
RUN addgroup -S spring && \
    adduser -S spring -G spring && \
    chown -R spring:spring /app
USER spring
COPY --from=builder /build/target/*.jar /app/app.jar

EXPOSE 8080

ENTRYPOINT ["sh", "-c"]
CMD ["java $JAVA_OPTS -jar $SPRING_ARGS app.jar"]
