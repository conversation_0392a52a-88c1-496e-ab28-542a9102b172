
# Análisis del Caso de Uso: Crear Consecutivos

## Descripción General
El caso de uso "Crear Consecutivos" permite la creación de diferentes tipos de consecutivos en el sistema, los cuales se utilizan para numerar documentos según su naturaleza (recepción, envío o producción).

## Tipos de Consecutivos
El sistema maneja tres tipos de consecutivos definidos en el enum `TipoConsecutivo`:

1. **RECEPCION**: Utilizado para documentos recibidos de fuentes externas.
2. **ENVIO**: Utilizado para documentos enviados a destinatarios externos.
3. **PRODUCCION**: Utilizado para documentos generados internamente.

## Estructura de un Consecutivo
Todos los consecutivos tienen los siguientes atributos:
- **ID**: Identificador único (UUID)
- **Prefijo**: Texto que aparece antes del número (opcional)
- **Sufijo**: Texto que aparece después del número (opcional)
- **Contador**: Valor numérico que se incrementa secuencialmente
- **Estado**: Estado del consecutivo (por defecto ACTIVO)

## Reglas de Negocio

### Reglas Comunes para Todos los Tipos
1. **Validación del Contador**: El contador no puede ser negativo.
2. **Unicidad**: No pueden existir dos consecutivos con la misma combinación de tipo, tipo documental y clasificación documental.
3. **Incremento Secuencial**: El contador se incrementa de uno en uno mediante el método `incrementarContador()`.

### Reglas Específicas por Tipo

#### Consecutivos Regulares (RECEPCION y ENVIO)
- Se crean con los atributos básicos: prefijo, sufijo, contador y tipo.
- No requieren asociación con tipos documentales ni clasificaciones documentales.

#### Consecutivos de PRODUCCION
1. **Tipo Documental Obligatorio**: Deben tener asociado un tipo documental.
   - Si se intenta crear sin tipo documental, se lanza `ConsecutivoWithoutTipoDocumentalException`.
2. **Clasificación Documental Opcional**: Pueden tener asociada una clasificación documental.
3. **Asociación Específica**: Se utilizan para documentos producidos internamente que requieren clasificación según su naturaleza.

## Flujo del Caso de Uso

1. **Recepción de Datos**: El sistema recibe un comando `CrearConsecutivoCommand` con los datos necesarios.
2. **Creación del Consecutivo**: 
   - Si es de tipo PRODUCCION, se crea mediante `createConsecutivoProduccion()`.
   - Si es de otro tipo, se crea mediante `createConsecutivoRegular()`.
3. **Validación de Unicidad**: Se verifica que no exista un consecutivo con las mismas características mediante `validateNoPreexistingConsecutivo()`.
4. **Persistencia**: Se guarda el consecutivo en la base de datos.
5. **Respuesta**: Se devuelve una representación DTO del consecutivo creado.

## Implementación Técnica

El caso de uso está implementado en la clase `CrearConsecutivoUseCase`, que:
- Recibe un comando con los datos del consecutivo a crear
- Delega la creación a métodos específicos según el tipo
- Valida la unicidad mediante el repositorio
- Persiste el consecutivo y devuelve su representación

## Exposición API

El caso de uso se expone a través del endpoint:
```
POST /api/v1/planeacion/consecutivos
```

Que recibe un objeto JSON con:
- `tipo`: Tipo de consecutivo (RECEPCION, ENVIO, PRODUCCION)
- `contador`: Valor inicial del contador
- `prefijo`: Prefijo opcional
- `sufijo`: Sufijo opcional
- `tipoDocumentalId`: ID del tipo documental (obligatorio para PRODUCCION)
- `clasificacionDocumentalId`: ID de la clasificación documental (opcional)

## Conclusión

El caso de uso "Crear Consecutivos" implementa un sistema flexible para la creación de diferentes tipos de consecutivos, con reglas específicas según su naturaleza, garantizando la integridad y unicidad de los datos mediante validaciones apropiadas.