
# Mapa Estructural del Proyecto Gedsys2-Base

Este documento proporciona una visión general de la estructura del proyecto, identificando dónde se encuentra cada tipo de componente dentro de la aplicación.

## Estructura General

El proyecto sigue una arquitectura hexagonal (también conocida como arquitectura de puertos y adaptadores) con una clara separación de capas:

```
gedsys2-base/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── co/com/gedsys/base/
│   │   │       ├── adapter/
│   │   │       ├── application/
│   │   │       ├── domain/
│   │   │       ├── infrastructure/
│   │   │       ├── util/
│   │   │       └── Gedsys2CoreApplication.java
│   │   └── resources/
│   │       ├── db/
│   │       ├── META-INF/
│   │       ├── application.yml
│   │       └── application-dev.yml
│   └── test/
│       └── java/
│           └── co/com/gedsys/base/
│               ├── adapter/
│               ├── application/
│               ├── domain/
│               ├── config/
│               └── Gedsys2CoreApplicationTests.java
├── doc/
├── .junie/
├── .plan/
└── pom.xml
```

## Componentes Principales

### 1. Capa de Dominio (`src/main/java/co/com/gedsys/base/domain/`)

Contiene las entidades de negocio y la lógica central de la aplicación:

- **Entidades de dominio**: `/domain/`
  - Consecutivo: `/domain/consecutivo/`
  - Control de acceso: `/domain/control_acceso/`
  - Documento: `/domain/documento/`
  - Entidad territorial: `/domain/entidad_territorial/`
  - Instrumento: `/domain/instrumento/`
  - Metadato: `/domain/metadato/`
  - Organización: `/domain/organizacion/`
  - Plantillas: `/domain/plantillas/`
  - Radicado: `/domain/radicado/`
  - Serie documental: `/domain/serie_documental/`
  - Tipología: `/domain/tipologia/`
  - Unidad documental: `/domain/unidad_documental/`
  - Usuario externo: `/domain/usuario_externo/`
  - Usuario interno: `/domain/usuario_interno/`

### 2. Capa de Aplicación (`src/main/java/co/com/gedsys/base/application/`)

Contiene los casos de uso que orquestan la lógica de negocio:

- **DTOs (Data Transfer Objects)**: `/application/dto/`
- **Mappers**: `/application/mapper/`
- **Casos de uso**: `/application/usecase/`
- **Componentes comunes**: `/application/common/`

### 3. Capa de Adaptadores (`src/main/java/co/com/gedsys/base/adapter/`)

Implementa los puertos de entrada y salida para comunicarse con el exterior:

- **Controladores HTTP**: `/adapter/http/`
- **Persistencia**: `/adapter/persistence/`
  - Mappers para persistencia: `/adapter/persistence/mappers/`

### 4. Capa de Infraestructura (`src/main/java/co/com/gedsys/base/infrastructure/`)

Proporciona implementaciones técnicas y configuraciones:

- **Programación orientada a aspectos**: `/infrastructure/aop/`
- **Configuraciones**: `/infrastructure/config/`
- **Acceso a datos**: `/infrastructure/data_access/`

### 5. Utilidades (`src/main/java/co/com/gedsys/base/util/`)

Contiene clases de utilidad general:
- `CamelCaseUtil.java`
- `CsvUtil.java`

### 6. Recursos y Configuraciones (`src/main/resources/`)

- **Configuraciones de la aplicación**:
  - `application.yml`
  - `application-dev.yml`

- **Migraciones de base de datos** (Flyway): `/resources/db/migration/`
  - Migraciones estructurales: `/resources/db/migration/structure/`
  - Parches: `/resources/db/migration/patch/`
  - Datos de prueba: `/resources/db/migration/test-data/`

### 7. Pruebas (`src/test/`)

Contiene las pruebas unitarias e integración:

- **Pruebas de adaptadores**: `/test/java/co/com/gedsys/base/adapter/`
- **Pruebas de aplicación**: `/test/java/co/com/gedsys/base/application/`
- **Pruebas de dominio**: `/test/java/co/com/gedsys/base/domain/`
- **Configuraciones de prueba**: `/test/java/co/com/gedsys/base/config/`

## Patrones y Convenciones

- **Arquitectura Hexagonal**: Separación clara entre dominio, aplicación y adaptadores.
- **Patrón Repositorio**: Interfaces en el dominio, implementaciones en la infraestructura.
- **Mappers**: Conversión entre entidades de dominio y DTOs/objetos de persistencia.
- **Casos de Uso**: Implementación de la lógica de negocio específica.
- **Migraciones con Flyway**: Gestión de cambios en la estructura de la base de datos.

## Ubicaciones Específicas

- **Migraciones estructurales de la base de datos con Flyway**: `src/main/resources/db/migration/structure/`
- **Controladores REST**: `src/main/java/co/com/gedsys/base/adapter/http/`
- **Entidades de dominio**: `src/main/java/co/com/gedsys/base/domain/`
- **Casos de uso**: `src/main/java/co/com/gedsys/base/application/usecase/`
- **Configuraciones de la aplicación**: `src/main/resources/application.yml`
- **Implementaciones de repositorios**: `src/main/java/co/com/gedsys/base/infrastructure/data_access/`