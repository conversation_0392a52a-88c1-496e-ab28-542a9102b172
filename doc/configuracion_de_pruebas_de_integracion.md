# Configuración de Pruebas de Integración con TestContainers

## Introducción

Este documento describe la configuración implementada para separar las pruebas de integración que utilizan TestContainers de las pruebas unitarias regulares. Esta separación permite que las pruebas de integración, que suelen ser más lentas debido al uso de contenedores Docker, no se ejecuten durante la compilación normal, mejorando así los tiempos de construcción.

## Configuración Implementada

### Separación de Pruebas

Se ha configurado el proyecto para distinguir entre dos tipos de pruebas:

1. **Pruebas Unitarias**: Se ejecutan durante la compilación normal con `mvn clean install` o `mvn test`.
2. **Pruebas de Integración**: Se ejecutan solo cuando se activa el perfil `integration-tests` con `mvn clean install -Pintegration-tests` o `mvn verify -Pintegration-tests`.

### Identificación de Pruebas de Integración

Las pruebas de integración se identifican por los siguientes patrones:

- Clases que terminan con `IntegrationTest.java`
- Clases que contienen `TestContainers` en su nombre
- Clases que contienen `SimpleTestContainers` en su nombre
- Clases que contienen `SpringBootTestContainers` en su nombre

### Configuración en pom.xml

La configuración se ha implementado en el archivo `pom.xml` mediante:

1. **Plugin maven-surefire-plugin**: Configurado para excluir las pruebas de integración durante la compilación normal.

```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-surefire-plugin</artifactId>
    <configuration>
        <!-- Excluir pruebas de integración durante la compilación normal -->
        <excludes>
            <exclude>**/*IntegrationTest.java</exclude>
            <exclude>**/TestContainers*.java</exclude>
            <exclude>**/SimpleTestContainers*.java</exclude>
            <exclude>**/SpringBootTestContainers*.java</exclude>
        </excludes>
    </configuration>
</plugin>
```

2. **Plugin maven-failsafe-plugin**: Configurado para incluir solo las pruebas de integración y ejecutarlas durante la fase de verificación.

```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-failsafe-plugin</artifactId>
    <configuration>
        <!-- Incluir solo pruebas de integración -->
        <includes>
            <include>**/*IntegrationTest.java</include>
            <include>**/TestContainers*.java</include>
            <include>**/SimpleTestContainers*.java</include>
            <include>**/SpringBootTestContainers*.java</include>
        </includes>
    </configuration>
    <executions>
        <execution>
            <goals>
                <goal>integration-test</goal>
                <goal>verify</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

3. **Perfil de Maven**: Se ha creado un perfil `integration-tests` que activa la ejecución de las pruebas de integración.

```xml
<profiles>
    <!-- Perfil para ejecutar pruebas de integración -->
    <profile>
        <id>integration-tests</id>
        <build>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <!-- La configuración ya está definida en la sección de build -->
                </plugin>
            </plugins>
        </build>
    </profile>
</profiles>
```

## Cómo Utilizar la Configuración

### Compilación Normal (sin pruebas de integración)

Para compilar el proyecto sin ejecutar las pruebas de integración:

```bash
mvn clean install
```

o

```bash
mvn test
```

### Ejecutar Pruebas de Integración

Para compilar el proyecto y ejecutar las pruebas de integración:

```bash
mvn clean install -Pintegration-tests
```

o para ejecutar solo las pruebas de integración:

```bash
mvn verify -Pintegration-tests
```

### Ejecutar una Prueba de Integración Específica

Para ejecutar una prueba de integración específica:

```bash
mvn verify -Pintegration-tests -Dit.test=NombreDeLaPruebaIntegracion
```

Por ejemplo:

```bash
mvn verify -Pintegration-tests -Dit.test=SeccionControllerIntegrationTest
```

## Buenas Prácticas

1. **Nombrado de Pruebas**: Asegúrate de seguir las convenciones de nombrado para que las pruebas se clasifiquen correctamente:
   - Pruebas de integración: `*IntegrationTest.java` o que contengan `TestContainers` en su nombre.
   - Pruebas unitarias: cualquier otro patrón.

2. **Ejecución en CI/CD**: Configura tu pipeline de CI/CD para ejecutar las pruebas de integración en etapas específicas, no en cada compilación.

3. **Optimización de Pruebas**: Utiliza `.withReuse(true)` en tus contenedores para reutilizarlos entre pruebas y mejorar el rendimiento.

## Conclusión

Esta configuración permite separar las pruebas de integración de las pruebas unitarias, mejorando los tiempos de compilación durante el desarrollo normal. Las pruebas de integración se ejecutan solo cuando se solicitan explícitamente, lo que proporciona un flujo de trabajo más eficiente.