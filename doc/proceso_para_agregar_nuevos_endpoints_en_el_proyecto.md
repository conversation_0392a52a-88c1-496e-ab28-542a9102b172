
# Proceso para Agregar Nuevos Endpoints en el Proyecto

Este proyecto sigue una arquitectura hexagonal (también conocida como arquitectura de puertos y adaptadores) con una clara separación entre el dominio, la aplicación y los adaptadores. A continuación, se detalla el flujo para agregar nuevas características (endpoints) al proyecto:

## 1. Modelado del Dominio

Primero, se debe modelar el dominio relacionado con la nueva funcionalidad:

- Crear o extender las entidades de dominio en el paquete `domain`
- Definir interfaces de repositorio necesarias
- Implementar reglas de negocio dentro de las entidades de dominio

Por ejemplo, si observamos la clase `Documento.java`, vemos que contiene toda la lógica de negocio relacionada con documentos, como firmar, aprobar, rechazar, etc.

## 2. Implementación de Casos de Uso

Los casos de uso representan las operaciones que se pueden realizar en el sistema:

- Crear una nueva clase de caso de uso en el paquete `application/usecase` dentro de la categoría correspondiente (produccion, planeacion, gestion_tramite)
- Implementar la interfaz `UseCase<Input, Output>`
- Definir un comando (command) que represente la entrada del caso de uso
- Implementar la lógica del caso de uso utilizando las entidades del dominio

Por ejemplo, el caso de uso `RegistrarBorradorUseCase` implementa la lógica para crear un borrador de documento.

## 3. Definición de la API

Definir la interfaz de la API REST:

- Crear una interfaz en el paquete `adapter/http` que defina los endpoints
- Utilizar anotaciones de Spring y Swagger para documentar la API
- Definir DTOs de solicitud y respuesta

Por ejemplo, la interfaz `ProduccionDocumentosAPI` define los endpoints relacionados con la producción de documentos.

## 4. Implementación del Controlador

Implementar el controlador que expone los endpoints:

- Crear una clase que implemente la interfaz de API definida anteriormente
- Inyectar los casos de uso necesarios
- Mapear las solicitudes HTTP a comandos de casos de uso
- Transformar los resultados de los casos de uso en respuestas HTTP

Por ejemplo, la clase `ProduccionDocumentalController` implementa la interfaz `ProduccionDocumentosAPI` y utiliza los casos de uso correspondientes.

## 5. Implementación de Adaptadores de Persistencia

Si es necesario, implementar los adaptadores de persistencia:

- Crear entidades JPA en el paquete `infrastructure/data_access`
- Implementar repositorios JPA
- Crear mapeadores entre entidades de dominio y entidades JPA

## Ejemplo Completo

Para agregar un nuevo endpoint para, por ejemplo, "Cancelar Documento", seguiríamos estos pasos:

1. **Dominio**: Agregar método `cancelar()` en la entidad `Documento`
2. **Caso de Uso**: Crear `CancelarDocumentoUseCase` y `CancelarDocumentoCommand`
3. **API**: Agregar método `cancelarDocumento()` en la interfaz `ProduccionDocumentosAPI`
4. **Controlador**: Implementar el método en `ProduccionDocumentalController`
5. **Persistencia**: Asegurar que el estado "CANCELADO" esté definido en `EstadoDocumento`

## Estructura de Directorios Relevante

```
src/main/java/co/com/gedsys/base/
├── adapter
│   ├── http                  # Controladores y definiciones de API
│   └── persistence           # Adaptadores de persistencia
├── application
│   └── usecase               # Casos de uso
│       ├── gestion_tramite
│       ├── planeacion
│       └── produccion
├── domain                    # Entidades y reglas de negocio
└── infrastructure            # Implementaciones técnicas
```

Esta arquitectura permite una clara separación de responsabilidades y facilita la extensión del sistema con nuevas funcionalidades manteniendo la integridad del dominio.