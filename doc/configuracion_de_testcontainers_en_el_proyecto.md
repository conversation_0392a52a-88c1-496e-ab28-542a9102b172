
# Configuración de TestContainers en el Proyecto

## Introducción

TestContainers es una biblioteca de Java que proporciona instancias ligeras y desechables de bases de datos, navegadores web, o cualquier otro servicio que pueda ejecutarse en un contenedor Docker. En nuestro proyecto, utilizamos TestContainers principalmente para proporcionar una base de datos PostgreSQL aislada durante las pruebas de integración.

## Configuración Actual

### Versión y Dependencias

Actualmente, el proyecto utiliza la versión **1.19.7** de TestContainers con las siguientes dependencias:

```xml
<!-- TestContainers -->
<dependency>
    <groupId>org.testcontainers</groupId>
    <artifactId>testcontainers</artifactId>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.testcontainers</groupId>
    <artifactId>junit-jupiter</artifactId>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.testcontainers</groupId>
    <artifactId>postgresql</artifactId>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-testcontainers</artifactId>
    <scope>test</scope>
</dependency>
```

### Clases de Configuración

El proyecto incluye varias clases que demuestran diferentes formas de utilizar TestContainers:

1. **TestContainersConfig**: Configuración principal que proporciona un contenedor PostgreSQL compartido para todas las pruebas.
2. **TestContainersBasicTest**: Prueba básica para verificar que TestContainers funciona correctamente.
3. **SimpleTestContainersTest**: Ejemplo de prueba que usa TestContainers sin Spring Boot.
4. **SpringBootTestContainersTest**: Ejemplo de prueba que combina Spring Boot y TestContainers.

### Configuración de Propiedades

El archivo `application-test.properties` contiene la configuración específica para las pruebas:

```properties
# Configuración para pruebas de integración con TestContainers

# La configuración del DataSource se realiza dinámicamente a través de TestContainersConfig

# Configuración de JPA para pruebas
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Configuración de Flyway para pruebas
spring.flyway.enabled=false

# Nivel de logging para pruebas
logging.level.org.springframework=INFO
logging.level.org.hibernate=INFO
logging.level.org.testcontainers=INFO
logging.level.co.com.gedsys=DEBUG
```

## Cómo Agregar Nuevas Pruebas

### 1. Prueba Básica con TestContainers

Para crear una prueba básica que solo verifique la funcionalidad de TestContainers:

```java
@Testcontainers
public class MiPruebaBasica {

    @Container
    public static PostgreSQLContainer<?> postgreSQLContainer = new PostgreSQLContainer<>("postgres:16-alpine")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");

    @Test
    public void testContenedorFuncionando() {
        assertTrue(postgreSQLContainer.isRunning(), "El contenedor de PostgreSQL debería estar en ejecución");
        System.out.println("URL JDBC: " + postgreSQLContainer.getJdbcUrl());
    }
}
```

### 2. Prueba de Integración con Spring Boot

Para crear una prueba de integración que combine Spring Boot y TestContainers:

```java
@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
public class MiPruebaIntegracion {

    @Container
    private static final PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:16-alpine")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");

    @DynamicPropertySource
    static void registrarPropiedadesPg(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        // Deshabilitar Flyway para esta prueba
        registry.add("spring.flyway.enabled", () -> "false");
        // Configurar Hibernate para crear el esquema
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
    }

    @Autowired
    private MiServicio miServicio;

    @Test
    public void testFuncionalidad() {
        // Implementar prueba utilizando el servicio inyectado
        // ...
    }
}
```

### 3. Prueba de Controlador REST

Para probar un controlador REST con TestContainers:

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@Testcontainers
@ActiveProfiles("test")
@Transactional
public class MiControllerIntegrationTest {

    @Container
    private static final PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:16-alpine")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");

    @DynamicPropertySource
    static void registrarPropiedadesPg(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.flyway.enabled", () -> "false");
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
    }

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testEndpoint() throws Exception {
        // Crear datos de prueba
        MiRequest request = new MiRequest("valor1", "valor2");

        // Realizar petición HTTP
        ResultActions result = mockMvc.perform(post("/api/v1/mi-endpoint")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)));

        // Verificar respuesta
        result.andExpect(status().isOk())
              .andExpect(jsonPath("$.propiedad", is("valorEsperado")));
    }
}
```

## Ajustes de Configuración

### Cambiar la Versión de PostgreSQL

Para utilizar una versión diferente de PostgreSQL:

```java
@Container
public static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine");
```

### Configurar Volúmenes y Persistencia

Para mantener los datos entre ejecuciones de pruebas:

```java
@Container
public static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:16-alpine")
        .withDatabaseName("testdb")
        .withUsername("test")
        .withPassword("test")
        .withReuse(true);  // Reutilizar el contenedor entre ejecuciones
```

### Configurar Inicialización de Base de Datos

Para inicializar la base de datos con scripts SQL:

```java
@Container
public static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:16-alpine")
        .withDatabaseName("testdb")
        .withUsername("test")
        .withPassword("test")
        .withInitScript("init-database.sql");  // Script SQL en src/test/resources
```

### Configurar Tiempos de Espera

Para ajustar los tiempos de espera:

```java
@Container
public static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:16-alpine")
        .withDatabaseName("testdb")
        .withUsername("test")
        .withPassword("test")
        .withStartupTimeout(Duration.ofSeconds(60));  // Tiempo de espera para inicio
```

## Buenas Prácticas

1. **Reutilizar contenedores**: Utiliza `.withReuse(true)` para reutilizar contenedores entre ejecuciones de pruebas y mejorar el rendimiento.

2. **Perfiles de prueba**: Utiliza `@ActiveProfiles("test")` para cargar configuraciones específicas para pruebas.

3. **Transaccionalidad**: Utiliza `@Transactional` en pruebas de integración para revertir cambios en la base de datos después de cada prueba.

4. **Contenedores compartidos**: Para pruebas que no necesitan un estado aislado de la base de datos, considera usar un contenedor compartido como en `TestContainersConfig`.

5. **Logging adecuado**: Configura niveles de logging apropiados en `application-test.properties` para facilitar la depuración.

## Solución de Problemas Comunes

1. **Docker no disponible**: Asegúrate de que Docker esté instalado y en ejecución en tu máquina.

2. **Conflictos de puertos**: TestContainers asigna puertos aleatorios, pero si hay conflictos, puedes especificar puertos con `.withExposedPorts()`.

3. **Memoria insuficiente**: Si Docker no tiene suficiente memoria asignada, los contenedores pueden fallar al iniciar. Aumenta la memoria asignada a Docker.

4. **Pruebas lentas**: Utiliza contenedores compartidos y la opción `.withReuse(true)` para mejorar el rendimiento.

## Conclusión

TestContainers proporciona un entorno de pruebas aislado y reproducible que facilita las pruebas de integración. La configuración actual del proyecto permite diferentes enfoques para las pruebas, desde pruebas básicas hasta pruebas de integración completas con Spring Boot.

Al seguir las pautas y ejemplos proporcionados, podrás agregar nuevas pruebas y ajustar la configuración según las necesidades específicas de tu caso de uso.