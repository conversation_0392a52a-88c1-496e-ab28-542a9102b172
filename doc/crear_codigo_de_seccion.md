

# Reglas para Crear Código de Sección

Este archivo describe las reglas para definir y validar un **código de sección** en el formato especificado por la lógica del sistema.

---

## 1. Estructura y Composición

- El código se conforma por **hasta 3 segmentos numéricos**, separados por puntos (.).
- Ejemplo de estructura:  ****
  `XX.YY.ZZ`

---

## 2. Reglas de Validación

- **Obligatoriedad:** El código **no puede ser nulo ni vacío**.
- **Segmentos:**  
  - El código **no puede tener más de 3 segmentos**.
  - Si faltan segmentos, se completan con `"0"` hasta tener tres.  
    Ejemplo: `7.2` → `07.02.00`
- **Numeración:**  
  - **Todos los segmentos deben ser numéricos.**
  - **El primer segmento no puede ser "00"** (no puede iniciar con cero).
- **Formato:**  
  - **Cada segmento debe componerse de dos dígitos** (rellenando con ceros a la izquierda si es necesario).
  - Ejemplo:  
    - `4` → `04.00.00`
    - `1.8` → `01.08.00`
    - `12.5.7` → `12.05.07`

---

## 3. Clasificación por Segmentos

Dependiendo de los valores de los segmentos, el código indica un tipo de unidad:

- **AUXILIAR:** El **tercer segmento** es distinto de `00`.  
  Ejemplo: `02.01.04`
- **SECRETARÍA:** El **segundo segmento** es distinto de `00` y el tercero es `00`.  
  Ejemplo: `12.07.00`
- **DESPACHO:** Solo el **primer segmento** es distinto de `00`, los demás son `00`.  
  Ejemplo: `03.00.00`
- **Inválido:** Todos los segmentos son `00`.  
  Ejemplo: `00.00.00`

---

## 4. Ejemplos

| Código original | Código normalizado | Válido | Tipo de Unidad     |
|-----------------|-------------------|--------|--------------------|
| `3`             | `03.00.00`        | Sí     | DESPACHO           |
| `10.5`          | `10.05.00`        | Sí     | SECRETARÍA         |
| `2.7.4`         | `02.07.04`        | Sí     | AUXILIAR           |
| `01.00.01`      | `01.00.01`        | Sí     | AUXILIAR           |
| `00.05.00`      | `00.05.00`        | **No** | -                  |
| `00.00.00`      | `00.00.00`        | **No** | -                  |
| `3.A.1`         | -                 | **No** | Segmento no numérico|
| `.*******`      | -                 | **No** | Más de 3 segmentos |
| `0.3`           | -                 | **No** | Primer segmento = 0|

---

## 5. Notas Adicionales

- Cualquier código que no cumpla exactamente estas reglas debe considerarse **inválido**.
- Todos los códigos válidos deben poder transformarse al formato final de **tres segmentos de dos dígitos cada uno**.

