

# Lógica de Negocio: Entidad **Sección**

La entidad `Seccion` representa una unidad organizacional jerárquica y su integridad depende de reglas clave de negocio, tanto para la estructura de la propia sección como para su integración en la jerarquía padre-hijo.

---

## 1. **Atributos Principales**

- **id**: Identificador único (UUID).
- **codigo**: Código normalizado y validado según las [reglas de Código de Sección](#codigo).
- **nombre**: Nombre descriptivo (obligatorio).
- **responsable**: Persona o rol responsable.
- **tipo**: Tipo de unidad (`DESPACHO`, `SECRETARIA`, `AUXILIAR`), derivado del código.
- **estado**: Estado de la sección (`ACTIVA` por defecto).
- **padre**: Secci<PERSON> padre (si aplica).
- **hijos**: Colección de secciones hijas.

---

## 2. **Reglas de Negocio y Validación**

### Creación

- **Obligatorio:** `codigo` y `nombre`, de lo contrario, lanza excepción.

- **Código normalizado:** Usa el objeto `CodigoSeccion` para validar y normalizar el código al crear la instancia, determinando automáticamente su tipo.

- **Estado inicial:** Al crear la sección, su `estado` es siempre `ACTIVA`.

---

### Jerarquía (Padre-Hijo)

#### a. **Asignación de Padre (`setPadre`)**

- Un **DESPACHO** **NO puede tener padre** (no se le asigna padre).
- **Una sección no puede ponerse como su propio padre.**
- **Un padre no puede ser cualquiera de los hijos de la sección**, para evitar ciclos.
- Se valida la **estructura del código entre padre e hijo** (ver [coherencia de código](#validacion-codigo-jerarquia)).

#### b. **Agregación de Hijo (`agregarHijo`)**

- **No puede autoagregarse:** Una sección no puede ser su propio hijo.
- **No puede agregar como hijo a su propio padre.**
- No se repite la relación si ya existe.
- Se valida la **jerarquía de tipos**:
  - Un hijo **no puede ser de categoría superior** al padre (e.g., un DESPACHO no puede ser hijo de una SECRETARIA).
- Se valida la **coherencia entre los códigos de padre e hijo**.

#### c. **Remoción de Hijo (`removerHijo`)**

- Sólo se pueden eliminar hijos existentes.

---

### Validación de Jerarquía de Códigos

Para asegurar la correcta pertenencia jerárquica, el método `validarJerarquiaCodigo(padre, hijo)` gestiona las siguientes reglas:

- **El primer segmento** del código del hijo debe coincidir con el del padre.
- Si el padre es un **DESPACHO** y el hijo una **SECRETARIA**
  - Segundo segmento del hijo debe indicar la secretaria y pertenecer al mismo despacho.
- Si el padre es una **SECRETARIA** y el hijo un **AUXILIAR**
  - **Los dos primeros segmentos** de los códigos deben coincidir (misma secretaría).
- Si la validación falla, se lanza una excepción explicativa.

---

## 3. **Ejemplos de Reglas de Jerarquía y Código**

| Caso                                | Ejemplo Padre     | Ejemplo Hijo    | Resultado        | Motivo                                                                              |
|--------------------------------------|-------------------|-----------------|------------------|-------------------------------------------------------------------------------------|
| **DESPACHO sin padre**               | `03.00.00`        | (no aplica)     | Válido           | DESPACHO no tiene padre                                                             |
| **SECRETARIA bajo DESPACHO**         | `03.00.00`        | `03.04.00`      | Válido           | Coinciden primer segmento; secretario dentro del mismo despacho                     |
| **AUXILIAR bajo SECRETARIA**         | `03.04.00`        | `03.04.02`      | Válido           | Coinciden dos primeros segmentos; auxiliar dentro de la secretaria                  |
| **Códigos con diferente jerarquía**  | `03.00.00`        | `02.04.00`      | Inválido         | Primer segmento distinto; no pertenece a la misma organización                      |
| **Categoría superior como hijo**     | `03.04.00`        | `03.00.00`      | Inválido         | SECRETARIA no puede tener hijo DESPACHO                                             |
| **Ciclo (padre-hijo invertido)**     | `03.04.00`        | `03.04.02`      | Inválido         | No se puede agregar como hijo a su propio padre                                     |
| **Autoasignación de padre/hijo**     | `03.04.00`        | `03.04.00`      | Inválido         | No puede ser su propio padre o su propio hijo                                       |

---

## 4. **Notas Adicionales**

- La lógica prohíbe cualquier ciclo en la jerarquía.
- Al asignar un padre o hijo, se actualizan las asociaciones internas automáticamente.
- El tipo y la integridad de la sección derivan exclusiva y automáticamente del valor del código, **no pueden ser manipulados directamente**.

---

### Referencias

- [Reglas de Código de Sección](#reglas-para-crear-codigo-de-seccion)
- Lógica definida en métodos: `setPadre`, `agregarHijo`, `validarJerarquiaCodigo`, y creadores del objeto.

