{"openapi": "3.1.0", "info": {"title": "API Core - Endpoints Específicos", "version": "1.0.0", "description": "Especificación OpenAPI 3.1 para endpoints seleccionados de Producción de Documentos y Planeación de Secciones. Todos los endpoints están bajo el context-path /core."}, "servers": [{"url": "/core"}], "paths": {"/api/v1/produccion/documentos/{documentId}/anexos": {"post": {"tags": ["Producción de Documentos"], "summary": "Agregar anexo a documento", "description": "Agrega un anexo a un documento existente, excepto si está en estado DESCARTADO.", "parameters": [{"name": "documentId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "example": "b3b6c1e2-8f2a-4c1a-9e2b-123456789abc", "description": "ID del documento al que se agregará el anexo."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SolicitudAgregarAnexo"}, "examples": {"ejemplo": {"summary": "Ejemplo de solicitud para agregar anexo", "value": {"nombre": "Contrato escaneado.pdf", "descripcion": "Contrato firmado escaneado en PDF", "fileId": "f1a2b3c4-d5e6-7890-abcd-1234567890ef", "hash": "a1b2c3d4e5f6", "bytes": "JVBERi0xLjQKJcfs...", "extension": ".pdf"}}}}}}, "responses": {"200": {"description": "Anexo agregado exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentoDTO"}, "examples": {"ejemplo": {"summary": "Documento actualizado con anexo", "value": {"id": "b3b6c1e2-8f2a-4c1a-9e2b-123456789abc", "titulo": "Contrato de arrendamiento", "estado": "BORRADOR", "anexos": [{"nombre": "Contrato escaneado.pdf", "descripcion": "Contrato firmado escaneado en PDF", "fileId": "f1a2b3c4-d5e6-7890-abcd-1234567890ef", "extension": ".pdf"}]}}}}}}, "404": {"description": "Documento no encontrado"}, "400": {"description": "No se pueden agregar anexos a documentos descartados"}}}}, "/api/v1/planeacion/secciones/{id}/activar": {"patch": {"tags": ["Planeación de Secciones"], "summary": "Activar sección", "description": "Activa una sección de planeación por su ID.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "example": "c7d8e9f0-1234-5678-abcd-9876543210ef", "description": "ID de la sección a activar."}], "responses": {"200": {"description": "Sección activada exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SeccionPlaneacionDS"}, "examples": {"ejemplo": {"summary": "Sección activada", "value": {"id": "c7d8e9f0-1234-5678-abcd-9876543210ef", "nombre": "Sección Finanzas", "estado": "ACTIVA"}}}}}}}}}, "/api/v1/planeacion/secciones/{id}/inactivar": {"patch": {"tags": ["Planeación de Secciones"], "summary": "Inactivar sección", "description": "Inactiva una sección de planeación por su ID.", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "example": "c7d8e9f0-1234-5678-abcd-9876543210ef", "description": "ID de la sección a inactivar."}], "responses": {"200": {"description": "Sección inactivada exitosamente", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SeccionPlaneacionDS"}, "examples": {"ejemplo": {"summary": "Sección inactivada", "value": {"id": "c7d8e9f0-1234-5678-abcd-9876543210ef", "nombre": "Sección Finanzas", "estado": "INACTIVA"}}}}}}}}}}, "components": {"schemas": {"SolicitudAgregarAnexo": {"type": "object", "properties": {"nombre": {"type": "string", "example": "Contrato escaneado.pdf"}, "descripcion": {"type": "string", "example": "Contrato firmado escaneado en PDF"}, "fileId": {"type": "string", "format": "uuid", "example": "f1a2b3c4-d5e6-7890-abcd-1234567890ef"}, "hash": {"type": "string", "example": "a1b2c3d4e5f6"}, "bytes": {"type": "string", "format": "byte", "example": "JVBERi0xLjQKJcfs..."}, "extension": {"type": "string", "example": ".pdf"}}, "required": ["nombre", "fileId", "extension"]}, "DocumentoDTO": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "b3b6c1e2-8f2a-4c1a-9e2b-123456789abc"}, "titulo": {"type": "string", "example": "Contrato de arrendamiento"}, "estado": {"type": "string", "example": "BORRADOR"}, "anexos": {"type": "array", "items": {"type": "object", "properties": {"nombre": {"type": "string", "example": "Contrato escaneado.pdf"}, "descripcion": {"type": "string", "example": "Contrato firmado escaneado en PDF"}, "fileId": {"type": "string", "format": "uuid", "example": "f1a2b3c4-d5e6-7890-abcd-1234567890ef"}, "extension": {"type": "string", "example": ".pdf"}}}}}}, "SeccionPlaneacionDS": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "c7d8e9f0-1234-5678-abcd-9876543210ef"}, "nombre": {"type": "string", "example": "Sección Finanzas"}, "estado": {"type": "string", "example": "ACTIVA"}}}}}}