# Reglas de Negocio para Consecutivos

## Reglas Actuales
1. **Validación del Contador**: El contador no puede ser negativo.
2. **Unicidad**: No pueden existir dos consecutivos con la misma combinación de tipo, tipo documental y clasificación documental.
3. **Incremento Secuencial**: El contador se incrementa de uno en uno mediante el método `incrementarContador()`.

### Reglas Específicas por Tipo

#### Consecutivos Regulares (RECEPCION y ENVIO)
- Se crean con los atributos básicos: prefijo, sufijo, contador y tipo.
- No requieren asociación con tipos documentales ni clasificaciones documentales.

#### Consecutivos de PRODUCCION
1. **Tipo Documental Obligatorio**: Deben tener asociado un tipo documental.
   - Si se intenta crear sin tipo documental, se lanza `ConsecutivoWithoutTipoDocumentalException`.
2. **Clasificación Documental Opcional**: Pueden tener asociada una clasificación documental.
3. **Asociación Específica**: Se utilizan para documentos producidos internamente que requieren clasificación según su naturaleza.

## Nuevas Reglas de Negocio a Implementar

1. **Unicidad por Tipo para RECEPCION y ENVIO**:
   - Solo puede existir un consecutivo de tipo RECEPCION en todo el sistema.
   - Solo puede existir un consecutivo de tipo ENVIO en todo el sistema.
   - Si se intenta crear un consecutivo de tipo RECEPCION o ENVIO cuando ya existe uno del mismo tipo, se debe lanzar una excepción.

2. **No Asociación para RECEPCION y ENVIO**:
   - Los consecutivos de tipo RECEPCION y ENVIO no deben tener tipo documental ni clasificación documental asociados.
   - Si se intenta crear un consecutivo de tipo RECEPCION o ENVIO con tipo documental o clasificación documental, se debe lanzar una excepción.

## Implementación Propuesta

### 1. Crear una nueva excepción para la unicidad por tipo

```java
package co.com.gedsys.base.application.usecase.planeacion.consecutivos;

import co.com.gedsys.base.domain.consecutivo.TipoConsecutivo;

public class ConsecutivoUnicoTipoException extends RuntimeException {

    public ConsecutivoUnicoTipoException(TipoConsecutivo tipo) {
        super("Ya existe un consecutivo de tipo " + tipo + ". Solo puede existir uno de este tipo.");
    }
}
```

### 2. Modificar el método `createConsecutivoRegular` para validar las nuevas reglas

```java
private Consecutivo createConsecutivoRegular(CrearConsecutivoCommand command) {
    // Para RECEPCION y ENVIO, validar que no exista ya un consecutivo del mismo tipo
    if (command.tipo() == TipoConsecutivo.RECEPCION || command.tipo() == TipoConsecutivo.ENVIO) {
        // Crear un consecutivo de ejemplo con solo el tipo para buscar si ya existe uno del mismo tipo
        Consecutivo ejemploTipo = new Consecutivo(null, null, 1, command.tipo());
        
        consecutivoRepository.findByExample(ejemploTipo)
            .ifPresent(existingConsecutivo -> {
                throw new ConsecutivoUnicoTipoException(command.tipo());
            });
            
        // Verificar que no se estén enviando tipo documental o clasificación documental
        if (command.tipoDocumentalId() != null || command.clasificacionDocumentalId() != null) {
            throw new IllegalArgumentException("Los consecutivos de tipo " + command.tipo() + 
                " no deben tener tipo documental ni clasificación documental asociados.");
        }
    }
    
    return new Consecutivo(
        command.prefijo(),
        command.sufijo(),
        command.contador(),
        command.tipo()
    );
}
```

## Pruebas Unitarias

Se deben actualizar las pruebas unitarias para verificar las nuevas reglas de negocio:

1. Verificar que se lance `ConsecutivoUnicoTipoException` cuando se intenta crear un consecutivo de tipo RECEPCION o ENVIO cuando ya existe uno del mismo tipo.
2. Verificar que se lance `IllegalArgumentException` cuando se intenta crear un consecutivo de tipo RECEPCION o ENVIO con tipo documental o clasificación documental.
3. Verificar que los cambios no afecten el funcionamiento existente para los consecutivos de tipo PRODUCCION.

## Notas Adicionales

- La implementación propuesta no afecta el funcionamiento existente para los consecutivos de tipo PRODUCCION.
- Se recomienda revisar la configuración del proyecto para resolver el error de compilación relacionado con `ConsecutivoMapper`.