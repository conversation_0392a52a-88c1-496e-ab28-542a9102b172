# .::GEDSYS 2 Base::.
ERP para la Gestión Electrónica de Documentos (GEDSYS).

## Instrucciones para iniciar la aplicación

### Requisitos previos

- Java JDK 23
- Maven 3.x
- Docker (Opcional para Docker Compose)

### Pasos para iniciar la aplicación

1. Clonar el repositorio:

```shell
git clone https://github.com/Sucomunicacion/gedsys2-base
cd gedsys2-base
```

2. Compilar el proyecto:

```shell
mvn clean install
```

3. Ejecutar la aplicación:

```shell
mvn spring-boot:run
```

4. La aplicación estará disponible en `http://localhost:8080` (asumiendo que usa el puerto por defecto)

---

## Configuración de la aplicación para desarrollo

### A. Archivo de Configuración de ejemplo para desarrollo

```yaml
spring:
  h2:
    console:
      enabled: true
  datasource:
    url: *******************************************
    username: myuser
    password: mypass
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  flyway:
    locations: classpath:db/migration/structure, classpath:db/migration/test-data, classpath:db/migration/patch
    clean-disabled: false
    clean-on-validation-error: true
  docker:
    compose:
      enabled: true

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    operations-sorter: method

eureka:
  client:
    enabled: false
```
### B. Archivo de docker-compose.yml para desarrollo

```yaml
name: gedsys-base

services:
  postgres:
    image: postgres:17-alpine
    container_name: gedsys2-postgres
    environment:
      POSTGRES_DB: mydatabase
      POSTGRES_USER: myuser
      POSTGRES_PASSWORD: mypass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  rabbitmq:
    image: rabbitmq:3-management
    container_name: gedsys2-rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest

volumes:
  postgres_data:
```
---

### Notas adicionales

- Esta aplicación utiliza Spring Boot 3.4.3
- Se incluye soporte para Docker Compose (Habilitar en el archivo `application-dev.yml` del perfil `dev`)
- La base de datos principal es PostgreSQL
- Se utiliza Flyway para migraciones de base de datos
- La documentación de la API estará disponible en `http://localhost:8080/swagger-ui.html`

### Licencia

Este software está bajo una licencia propietaria de Suministros y Sucomunicación S.A.S. 
Para más detalles, consulte: https://gedsys.com.co/legal